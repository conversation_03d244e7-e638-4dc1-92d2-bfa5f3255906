"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* harmony import */ var _styles_markdown_advanced_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-advanced.css */ \"(app-pages-browser)/./src/styles/markdown-advanced.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\", hasWebSearch = false } = param;\n    // Função para detectar se o conteúdo contém indicadores de web search\n    const detectWebSearch = (text)=>{\n        const webSearchIndicators = [\n            \"A web search was conducted on\",\n            \"web search results\",\n            \"IMPORTANT: Cite them using markdown links\",\n            \"Example: [nytimes.com]\"\n        ];\n        return webSearchIndicators.some((indicator)=>text.includes(indicator));\n    };\n    // Função para extrair e estilizar a seção de web search\n    const processWebSearchContent = (text)=>{\n        const webSearchPattern = /A web search was conducted on[^]*?Example: \\[nytimes\\.com\\]\\([^)]+\\)\\./;\n        const match = text.match(webSearchPattern);\n        if (match) {\n            return {\n                webSearchSection: match[0],\n                mainContent: text.replace(webSearchPattern, \"\").trim()\n            };\n        }\n        return {\n            webSearchSection: \"\",\n            mainContent: text\n        };\n    };\n    const isWebSearchMessage = hasWebSearch || detectWebSearch(content);\n    const { webSearchSection, mainContent } = isWebSearchMessage ? processWebSearchContent(content) : {\n        webSearchSection: \"\",\n        mainContent: content\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            rehypePlugins: [\n                rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                [\n                    rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    {\n                        detect: true,\n                        ignoreMissing: true\n                    }\n                ]\n            ],\n            components: {\n                // Customizar renderização de código\n                code (param) {\n                    let { node, inline, className, children, ...props } = param;\n                    const match = /language-(\\w+)/.exec(className || \"\");\n                    return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: \"bg-gray-800 rounded-lg p-4 overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: className,\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de links\n                a (param) {\n                    let { children, href, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: href,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"text-blue-400 hover:text-blue-300 underline\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de tabelas\n                table (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto my-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full border-collapse border border-gray-600\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0);\n                },\n                th (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                        className: \"border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                td (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                        className: \"border border-gray-600 px-4 py-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de blockquotes\n                blockquote (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de listas\n                ul (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside space-y-1 my-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                ol (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside space-y-1 my-2\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de títulos\n                h1 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h2 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                h3 (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de parágrafos\n                p (param) {\n                    let { children, ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-3 leading-relaxed text-gray-200\",\n                        ...props,\n                        children: children\n                    }, void 0, false, void 0, void 0);\n                },\n                // Customizar renderização de linha horizontal\n                hr (param) {\n                    let { ...props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                        className: \"border-gray-600 my-6\",\n                        ...props\n                    }, void 0, false, void 0, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9NYXJrZG93blJlbmRlcmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2lCO0FBQ1I7QUFDRTtBQUNFO0FBQ1E7QUFFL0MsdUNBQXVDO0FBQ0w7QUFDRztBQUNHO0FBUXhDLE1BQU1NLGlDQUFvRE4saURBQVUsTUFBQztRQUFDLEVBQ3BFUSxPQUFPLEVBQ1BDLFlBQVksRUFBRSxFQUNkQyxlQUFlLEtBQUssRUFDckI7SUFDQyxzRUFBc0U7SUFDdEUsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLE1BQU1DLHNCQUFzQjtZQUMxQjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0QsT0FBT0Esb0JBQW9CQyxJQUFJLENBQUNDLENBQUFBLFlBQWFILEtBQUtJLFFBQVEsQ0FBQ0Q7SUFDN0Q7SUFFQSx3REFBd0Q7SUFDeEQsTUFBTUUsMEJBQTBCLENBQUNMO1FBQy9CLE1BQU1NLG1CQUFtQjtRQUN6QixNQUFNQyxRQUFRUCxLQUFLTyxLQUFLLENBQUNEO1FBRXpCLElBQUlDLE9BQU87WUFDVCxPQUFPO2dCQUNMQyxrQkFBa0JELEtBQUssQ0FBQyxFQUFFO2dCQUMxQkUsYUFBYVQsS0FBS1UsT0FBTyxDQUFDSixrQkFBa0IsSUFBSUssSUFBSTtZQUN0RDtRQUNGO1FBRUEsT0FBTztZQUNMSCxrQkFBa0I7WUFDbEJDLGFBQWFUO1FBQ2Y7SUFDRjtJQUVBLE1BQU1ZLHFCQUFxQmQsZ0JBQWdCQyxnQkFBZ0JIO0lBQzNELE1BQU0sRUFBRVksZ0JBQWdCLEVBQUVDLFdBQVcsRUFBRSxHQUFHRyxxQkFBcUJQLHdCQUF3QlQsV0FBVztRQUFFWSxrQkFBa0I7UUFBSUMsYUFBYWI7SUFBUTtJQUMvSSxxQkFDRSw4REFBQ2lCO1FBQUloQixXQUFXLG9CQUE4QixPQUFWQTtrQkFDbEMsNEVBQUNSLG9EQUFhQTtZQUNaeUIsZUFBZTtnQkFBQ3hCLGtEQUFTQTtnQkFBRUMsbURBQVVBO2FBQUM7WUFDdEN3QixlQUFlO2dCQUNidkIsb0RBQVdBO2dCQUNYO29CQUFDQyx3REFBZUE7b0JBQUU7d0JBQUV1QixRQUFRO3dCQUFNQyxlQUFlO29CQUFLO2lCQUFFO2FBQ3pEO1lBQ0RDLFlBQVk7Z0JBQ1Ysb0NBQW9DO2dCQUNwQ0MsTUFBSyxLQUFvRDt3QkFBcEQsRUFBRUMsSUFBSSxFQUFFQyxNQUFNLEVBQUV4QixTQUFTLEVBQUV5QixRQUFRLEVBQUUsR0FBR0MsT0FBWSxHQUFwRDtvQkFDSCxNQUFNaEIsUUFBUSxpQkFBaUJpQixJQUFJLENBQUMzQixhQUFhO29CQUNqRCxPQUFPLENBQUN3QixVQUFVZCxzQkFDaEIsOERBQUNrQjt3QkFBSTVCLFdBQVU7a0NBQ2IsNEVBQUNzQjs0QkFBS3RCLFdBQVdBOzRCQUFZLEdBQUcwQixLQUFLO3NDQUNsQ0Q7O3NFQUlMLDhEQUFDSDt3QkFDQ3RCLFdBQVU7d0JBQ1QsR0FBRzBCLEtBQUs7a0NBRVJEOztnQkFHUDtnQkFFQSxtQ0FBbUM7Z0JBQ25DSSxHQUFFLEtBQTRCO3dCQUE1QixFQUFFSixRQUFRLEVBQUVLLElBQUksRUFBRSxHQUFHSixPQUFPLEdBQTVCO29CQUNBLHFCQUNFLDhEQUFDRzt3QkFDQ0MsTUFBTUE7d0JBQ05DLFFBQU87d0JBQ1BDLEtBQUk7d0JBQ0poQyxXQUFVO3dCQUNULEdBQUcwQixLQUFLO2tDQUVSRDs7Z0JBR1A7Z0JBRUEscUNBQXFDO2dCQUNyQ1EsT0FBTSxLQUFzQjt3QkFBdEIsRUFBRVIsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7b0JBQ0oscUJBQ0UsOERBQUNWO3dCQUFJaEIsV0FBVTtrQ0FDYiw0RUFBQ2lDOzRCQUNDakMsV0FBVTs0QkFDVCxHQUFHMEIsS0FBSztzQ0FFUkQ7OztnQkFJVDtnQkFFQVMsSUFBRyxLQUFzQjt3QkFBdEIsRUFBRVQsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7b0JBQ0QscUJBQ0UsOERBQUNRO3dCQUNDbEMsV0FBVTt3QkFDVCxHQUFHMEIsS0FBSztrQ0FFUkQ7O2dCQUdQO2dCQUVBVSxJQUFHLEtBQXNCO3dCQUF0QixFQUFFVixRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtvQkFDRCxxQkFDRSw4REFBQ1M7d0JBQ0NuQyxXQUFVO3dCQUNULEdBQUcwQixLQUFLO2tDQUVSRDs7Z0JBR1A7Z0JBRUEseUNBQXlDO2dCQUN6Q1csWUFBVyxLQUFzQjt3QkFBdEIsRUFBRVgsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7b0JBQ1QscUJBQ0UsOERBQUNVO3dCQUNDcEMsV0FBVTt3QkFDVCxHQUFHMEIsS0FBSztrQ0FFUkQ7O2dCQUdQO2dCQUVBLG9DQUFvQztnQkFDcENZLElBQUcsS0FBc0I7d0JBQXRCLEVBQUVaLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO29CQUNELHFCQUNFLDhEQUFDVzt3QkFBR3JDLFdBQVU7d0JBQXdDLEdBQUcwQixLQUFLO2tDQUMzREQ7O2dCQUdQO2dCQUVBYSxJQUFHLEtBQXNCO3dCQUF0QixFQUFFYixRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtvQkFDRCxxQkFDRSw4REFBQ1k7d0JBQUd0QyxXQUFVO3dCQUEyQyxHQUFHMEIsS0FBSztrQ0FDOUREOztnQkFHUDtnQkFFQSxxQ0FBcUM7Z0JBQ3JDYyxJQUFHLEtBQXNCO3dCQUF0QixFQUFFZCxRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtvQkFDRCxxQkFDRSw4REFBQ2E7d0JBQUd2QyxXQUFVO3dCQUEyQyxHQUFHMEIsS0FBSztrQ0FDOUREOztnQkFHUDtnQkFFQWUsSUFBRyxLQUFzQjt3QkFBdEIsRUFBRWYsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7b0JBQ0QscUJBQ0UsOERBQUNjO3dCQUFHeEMsV0FBVTt3QkFBMEMsR0FBRzBCLEtBQUs7a0NBQzdERDs7Z0JBR1A7Z0JBRUFnQixJQUFHLEtBQXNCO3dCQUF0QixFQUFFaEIsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7b0JBQ0QscUJBQ0UsOERBQUNlO3dCQUFHekMsV0FBVTt3QkFBMEMsR0FBRzBCLEtBQUs7a0NBQzdERDs7Z0JBR1A7Z0JBRUEsd0NBQXdDO2dCQUN4Q2lCLEdBQUUsS0FBc0I7d0JBQXRCLEVBQUVqQixRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0QjtvQkFDQSxxQkFDRSw4REFBQ2dCO3dCQUFFMUMsV0FBVTt3QkFBc0MsR0FBRzBCLEtBQUs7a0NBQ3hERDs7Z0JBR1A7Z0JBRUEsOENBQThDO2dCQUM5Q2tCLElBQUcsS0FBWTt3QkFBWixFQUFFLEdBQUdqQixPQUFPLEdBQVo7b0JBQ0QscUJBQ0UsOERBQUNpQjt3QkFBRzNDLFdBQVU7d0JBQXdCLEdBQUcwQixLQUFLOztnQkFFbEQ7WUFDRjtzQkFFQzNCOzs7Ozs7Ozs7OztBQUlUOztBQUVBLCtEQUFlRixnQkFBZ0JBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL01hcmtkb3duUmVuZGVyZXIudHN4PzBhOTkiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSAncmVhY3QtbWFya2Rvd24nO1xuaW1wb3J0IHJlbWFya0dmbSBmcm9tICdyZW1hcmstZ2ZtJztcbmltcG9ydCByZW1hcmtNYXRoIGZyb20gJ3JlbWFyay1tYXRoJztcbmltcG9ydCByZWh5cGVLYXRleCBmcm9tICdyZWh5cGUta2F0ZXgnO1xuaW1wb3J0IHJlaHlwZUhpZ2hsaWdodCBmcm9tICdyZWh5cGUtaGlnaGxpZ2h0JztcblxuLy8gSW1wb3J0YXIgZXN0aWxvcyBkbyBLYVRlWCBlIE1hcmtkb3duXG5pbXBvcnQgJ2thdGV4L2Rpc3Qva2F0ZXgubWluLmNzcyc7XG5pbXBvcnQgJ0Avc3R5bGVzL21hcmtkb3duLWxhdGV4LmNzcyc7XG5pbXBvcnQgJ0Avc3R5bGVzL21hcmtkb3duLWFkdmFuY2VkLmNzcyc7XG5cbmludGVyZmFjZSBNYXJrZG93blJlbmRlcmVyUHJvcHMge1xuICBjb250ZW50OiBzdHJpbmc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgaGFzV2ViU2VhcmNoPzogYm9vbGVhbjtcbn1cblxuY29uc3QgTWFya2Rvd25SZW5kZXJlcjogUmVhY3QuRkM8TWFya2Rvd25SZW5kZXJlclByb3BzPiA9IFJlYWN0Lm1lbW8oKHtcbiAgY29udGVudCxcbiAgY2xhc3NOYW1lID0gJycsXG4gIGhhc1dlYlNlYXJjaCA9IGZhbHNlXG59KSA9PiB7XG4gIC8vIEZ1bsOnw6NvIHBhcmEgZGV0ZWN0YXIgc2UgbyBjb250ZcO6ZG8gY29udMOpbSBpbmRpY2Fkb3JlcyBkZSB3ZWIgc2VhcmNoXG4gIGNvbnN0IGRldGVjdFdlYlNlYXJjaCA9ICh0ZXh0OiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICBjb25zdCB3ZWJTZWFyY2hJbmRpY2F0b3JzID0gW1xuICAgICAgJ0Egd2ViIHNlYXJjaCB3YXMgY29uZHVjdGVkIG9uJyxcbiAgICAgICd3ZWIgc2VhcmNoIHJlc3VsdHMnLFxuICAgICAgJ0lNUE9SVEFOVDogQ2l0ZSB0aGVtIHVzaW5nIG1hcmtkb3duIGxpbmtzJyxcbiAgICAgICdFeGFtcGxlOiBbbnl0aW1lcy5jb21dJ1xuICAgIF07XG4gICAgcmV0dXJuIHdlYlNlYXJjaEluZGljYXRvcnMuc29tZShpbmRpY2F0b3IgPT4gdGV4dC5pbmNsdWRlcyhpbmRpY2F0b3IpKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGV4dHJhaXIgZSBlc3RpbGl6YXIgYSBzZcOnw6NvIGRlIHdlYiBzZWFyY2hcbiAgY29uc3QgcHJvY2Vzc1dlYlNlYXJjaENvbnRlbnQgPSAodGV4dDogc3RyaW5nKTogeyB3ZWJTZWFyY2hTZWN0aW9uOiBzdHJpbmc7IG1haW5Db250ZW50OiBzdHJpbmcgfSA9PiB7XG4gICAgY29uc3Qgd2ViU2VhcmNoUGF0dGVybiA9IC9BIHdlYiBzZWFyY2ggd2FzIGNvbmR1Y3RlZCBvblteXSo/RXhhbXBsZTogXFxbbnl0aW1lc1xcLmNvbVxcXVxcKFteKV0rXFwpXFwuLztcbiAgICBjb25zdCBtYXRjaCA9IHRleHQubWF0Y2god2ViU2VhcmNoUGF0dGVybik7XG5cbiAgICBpZiAobWF0Y2gpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHdlYlNlYXJjaFNlY3Rpb246IG1hdGNoWzBdLFxuICAgICAgICBtYWluQ29udGVudDogdGV4dC5yZXBsYWNlKHdlYlNlYXJjaFBhdHRlcm4sICcnKS50cmltKClcbiAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHdlYlNlYXJjaFNlY3Rpb246ICcnLFxuICAgICAgbWFpbkNvbnRlbnQ6IHRleHRcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IGlzV2ViU2VhcmNoTWVzc2FnZSA9IGhhc1dlYlNlYXJjaCB8fCBkZXRlY3RXZWJTZWFyY2goY29udGVudCk7XG4gIGNvbnN0IHsgd2ViU2VhcmNoU2VjdGlvbiwgbWFpbkNvbnRlbnQgfSA9IGlzV2ViU2VhcmNoTWVzc2FnZSA/IHByb2Nlc3NXZWJTZWFyY2hDb250ZW50KGNvbnRlbnQpIDogeyB3ZWJTZWFyY2hTZWN0aW9uOiAnJywgbWFpbkNvbnRlbnQ6IGNvbnRlbnQgfTtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1hcmtkb3duLWNvbnRlbnQgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8UmVhY3RNYXJrZG93blxuICAgICAgICByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtLCByZW1hcmtNYXRoXX1cbiAgICAgICAgcmVoeXBlUGx1Z2lucz17W1xuICAgICAgICAgIHJlaHlwZUthdGV4LFxuICAgICAgICAgIFtyZWh5cGVIaWdobGlnaHQsIHsgZGV0ZWN0OiB0cnVlLCBpZ25vcmVNaXNzaW5nOiB0cnVlIH1dXG4gICAgICAgIF19XG4gICAgICAgIGNvbXBvbmVudHM9e3tcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGPDs2RpZ29cbiAgICAgICAgICBjb2RlKHsgbm9kZSwgaW5saW5lLCBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnN0IG1hdGNoID0gL2xhbmd1YWdlLShcXHcrKS8uZXhlYyhjbGFzc05hbWUgfHwgJycpO1xuICAgICAgICAgICAgcmV0dXJuICFpbmxpbmUgJiYgbWF0Y2ggPyAoXG4gICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcm91bmRlZC1sZyBwLTQgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxjb2RlIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIHB4LTEuNSBweS0wLjUgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbW9ub1wiIFxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIFxuICAgICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgbGlua3NcbiAgICAgICAgICBhKHsgY2hpbGRyZW4sIGhyZWYsIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj17aHJlZn1cbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTMwMCB1bmRlcmxpbmVcIlxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIFxuICAgICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgdGFiZWxhc1xuICAgICAgICAgIHRhYmxlKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvIG15LTRcIj5cbiAgICAgICAgICAgICAgICA8dGFibGUgXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGJvcmRlci1jb2xsYXBzZSBib3JkZXIgYm9yZGVyLWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgdGgoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPHRoIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS02MDAgYmctZ3JheS03MDAgcHgtNCBweS0yIHRleHQtbGVmdCBmb250LXNlbWlib2xkXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgdGQoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPHRkIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS02MDAgcHgtNCBweS0yXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBibG9ja3F1b3Rlc1xuICAgICAgICAgIGJsb2NrcXVvdGUoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPGJsb2NrcXVvdGUgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWwtNCBib3JkZXItYmx1ZS01MDAgcGwtNCBweS0yIG15LTQgYmctZ3JheS04MDAvNTAgaXRhbGljXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvYmxvY2txdW90ZT5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGxpc3Rhc1xuICAgICAgICAgIHVsKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xIG15LTJcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBvbCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBteS0yXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvb2w+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSB0w610dWxvc1xuICAgICAgICAgIGgxKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNCBtdC02IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBoMih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItMyBtdC01IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBoMyh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItMiBtdC00IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIHBhcsOhZ3JhZm9zXG4gICAgICAgICAgcCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zIGxlYWRpbmctcmVsYXhlZCB0ZXh0LWdyYXktMjAwXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGxpbmhhIGhvcml6b250YWxcbiAgICAgICAgICBocih7IC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxociBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgbXktNlwiIHsuLi5wcm9wc30gLz5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7Y29udGVudH1cbiAgICAgIDwvUmVhY3RNYXJrZG93bj5cbiAgICA8L2Rpdj5cbiAgKTtcbn0pO1xuXG5leHBvcnQgZGVmYXVsdCBNYXJrZG93blJlbmRlcmVyO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUmVhY3RNYXJrZG93biIsInJlbWFya0dmbSIsInJlbWFya01hdGgiLCJyZWh5cGVLYXRleCIsInJlaHlwZUhpZ2hsaWdodCIsIk1hcmtkb3duUmVuZGVyZXIiLCJtZW1vIiwiY29udGVudCIsImNsYXNzTmFtZSIsImhhc1dlYlNlYXJjaCIsImRldGVjdFdlYlNlYXJjaCIsInRleHQiLCJ3ZWJTZWFyY2hJbmRpY2F0b3JzIiwic29tZSIsImluZGljYXRvciIsImluY2x1ZGVzIiwicHJvY2Vzc1dlYlNlYXJjaENvbnRlbnQiLCJ3ZWJTZWFyY2hQYXR0ZXJuIiwibWF0Y2giLCJ3ZWJTZWFyY2hTZWN0aW9uIiwibWFpbkNvbnRlbnQiLCJyZXBsYWNlIiwidHJpbSIsImlzV2ViU2VhcmNoTWVzc2FnZSIsImRpdiIsInJlbWFya1BsdWdpbnMiLCJyZWh5cGVQbHVnaW5zIiwiZGV0ZWN0IiwiaWdub3JlTWlzc2luZyIsImNvbXBvbmVudHMiLCJjb2RlIiwibm9kZSIsImlubGluZSIsImNoaWxkcmVuIiwicHJvcHMiLCJleGVjIiwicHJlIiwiYSIsImhyZWYiLCJ0YXJnZXQiLCJyZWwiLCJ0YWJsZSIsInRoIiwidGQiLCJibG9ja3F1b3RlIiwidWwiLCJvbCIsImgxIiwiaDIiLCJoMyIsInAiLCJociJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});