"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos (novos):\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos novos detalhes:\", JSON.stringify(attachments, null, 2));\n        console.log(\"Web Search Enabled:\", webSearchEnabled);\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex + 1);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            for(let i = messageIndex + 1; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>{\n                    var _msg_attachments;\n                    return ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) > 0;\n                }).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 811,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 826,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 825,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 838,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 856,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 864,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 872,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 809,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"ecm9WaQ7Y3OWDpY07FPOEl6+gfM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});