"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Anexos recebidos (novos):\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos novos detalhes:\", JSON.stringify(attachments, null, 2));\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // Carregar o nome do chat recém-criado com um pequeno delay\n                setTimeout(()=>{\n                    if (chatIdToUse) {\n                        loadChatName(chatIdToUse);\n                    }\n                }, 100);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString()\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                setChatName(chatData.name || \"Conversa sem nome\");\n            } else {\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentChat && currentChat !== actualChatId) {\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex + 1);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            for(let i = messageIndex + 1; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString()\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    const handleFullscreenToggle = ()=>{\n        setIsFullscreen(!isFullscreen);\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>{\n                    var _msg_attachments;\n                    return ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) > 0;\n                }).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen \".concat(isFullscreen ? \"fixed inset-0 z-50 bg-gradient-rafthor\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                isFullscreen: isFullscreen,\n                onFullscreenToggle: handleFullscreenToggle,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 827,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 826,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 839,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 857,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 865,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 873,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 808,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"bRRkI0MWfsaVKuRTeJ3aRUAJvcg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});