"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Deletar arquivo chat.json do Storage\n                try {\n                    const storageRef = ref(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(storageRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo chat.json no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Deletar todos os anexos do chat\n                await deleteChatAttachments(deleteConfirmModal.id);\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\", \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-lg\",\n                                            children: userData.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Carregando...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-400 text-sm\",\n                                                title: openRouterBalance.error,\n                                                children: \"$0.00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: [\n                                                    \"$\",\n                                                    openRouterBalance.balance.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onSettingsOpen,\n                                        className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xc1rea Inicial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewFolder,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium\",\n                                            title: \"Nova Pasta\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Conversas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            onDragOver: (e)=>handleDragOver(e, folder.id),\n                                            onDragLeave: handleDragLeave,\n                                            onDrop: (e)=>handleDrop(e, folder.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                    onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                    onMouseLeave: ()=>setHoveredFolder(null),\n                                                    onClick: ()=>toggleFolder(folder.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 5l7 7-7 7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            style: {\n                                                                                color: getFolderHexColor(folder.color)\n                                                                            },\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 755,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                        children: folder.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 762,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                        children: folder.chats.length\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 765,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 761,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                children: folder.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Editar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleEditFolder(folder.id);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Deletar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleDeleteFolder(folder.id, folder.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-6 mt-2 space-y-1\",\n                                                    children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: ()=>handleDragStart(chat.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(currentChat === chat.id ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(draggedChat === chat.id ? \"opacity-50 scale-95\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-full text-left p-3 flex items-start space-x-3\",\n                                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(currentChat === chat.id ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                                                                            children: [\n                                                                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-2 h-2 text-white\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 3,\n                                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 837,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 836,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 835,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    strokeWidth: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 843,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 842,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"truncate text-sm font-semibold mb-1 \".concat(currentChat === chat.id ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                                                                    children: chat.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 848,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"truncate text-xs leading-relaxed transition-all duration-300 \".concat(currentChat === chat.id ? \"text-blue-200/80\" : \"text-blue-300/70 group-hover:text-blue-200\"),\n                                                                                    children: chat.lastMessage || \"Nenhuma mensagem ainda...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 855,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs mt-1 block \".concat(currentChat === chat.id ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                                                                    children: formatTime(chat.lastMessageTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Editar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"edit\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 884,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Deletar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"delete\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 897,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 896,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, chat.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, folder.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    onDragOver: (e)=>handleDragOver(e, null),\n                                    onDragLeave: handleDragLeave,\n                                    onDrop: (e)=>handleDrop(e, null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Sem Pasta \",\n                                                            dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                            children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/30\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: \"Nenhuma conversa sem pasta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                    chat: chat,\n                                                    isActive: currentChat === chat.id,\n                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                    onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                    onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                    onDragStart: handleDragStart,\n                                                    onDragEnd: handleDragEnd,\n                                                    isDragging: draggedChat === chat.id\n                                                }, chat.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, undefined),\n                                folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/30 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/50 text-sm\",\n                                            children: \"Nenhuma conversa ainda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/30 text-xs mt-1\",\n                                            children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden p-4 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 977,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Fechar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 972,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 987,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 996,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1009,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1017,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1029,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1044,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\")), \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging } = param;\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const handleEdit = (e)=>{\n        e.stopPropagation();\n        onEdit(chat.id, chat.name);\n    };\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        onDelete(chat.id, chat.name);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"relative w-full rounded-lg transition-all duration-200 group cursor-move \".concat(isActive ? \"bg-blue-600/50\" : \"hover:bg-white/5\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left px-3 py-3 text-white/80 hover:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                            children: [\n                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-2 h-2 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 3,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200\",\n                                            children: formatTime(chat.lastMessageTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 line-clamp-2 leading-relaxed\",\n                                    children: chat.lastMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 1111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Configurar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Deletar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1097,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});