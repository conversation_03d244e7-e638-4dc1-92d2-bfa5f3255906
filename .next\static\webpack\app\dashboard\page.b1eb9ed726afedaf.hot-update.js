"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/SettingsModal.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction SettingsModal(param) {\n    let { isOpen, onClose, userData, onUserDataUpdate } = param;\n    _s();\n    const { logout, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"geral\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Estados para cada aba\n    const [generalData, setGeneralData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: userData.username,\n        profileImage: userData.profileImage || \"\",\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [appearanceSettings, setAppearanceSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        fonte: \"Inter\",\n        tamanhoFonte: 14,\n        palavrasPorSessao: 5000\n    });\n    const [aiEndpoints, setAiEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            nome: \"OpenRouter\",\n            url: \"https://openrouter.ai/api/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n            ativo: false\n        },\n        {\n            nome: \"DeepSeek\",\n            url: \"https://api.deepseek.com/v1/chat/completions\",\n            apiKey: \"\",\n            modeloPadrao: \"deepseek-chat\",\n            ativo: false\n        }\n    ]);\n    const [memories, setMemories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [memoryCategories, setMemoryCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAddMemory, setShowAddMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddCategory, setShowAddCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMemory, setNewMemory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        titulo: \"\",\n        conteudo: \"\",\n        cor: \"#3B82F6\",\n        categoria: null,\n        chatId: null,\n        global: true\n    });\n    const [newCategory, setNewCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        descricao: \"\",\n        cor: \"#3B82F6\"\n    });\n    const [showAddEndpoint, setShowAddEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newEndpoint, setNewEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nome: \"\",\n        url: \"\",\n        apiKey: \"\",\n        modeloPadrao: \"\",\n        ativo: false\n    });\n    // Carregar configurações do Firestore\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadConfigurations = async ()=>{\n            if (!userData.username) return;\n            try {\n                console.log(\"Carregando configura\\xe7\\xf5es para:\", userData.username);\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    const config = configDoc.data();\n                    console.log(\"Configura\\xe7\\xf5es carregadas:\", config);\n                    if (config.aparencia) {\n                        setAppearanceSettings(config.aparencia);\n                    }\n                    if (config.endpoints) {\n                        const endpointsArray = Object.values(config.endpoints);\n                        setAiEndpoints(endpointsArray);\n                    } else {\n                        // Manter endpoints padrão se não houver configuração salva\n                        setAiEndpoints([\n                            {\n                                nome: \"OpenRouter\",\n                                url: \"https://openrouter.ai/api/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n                                ativo: false\n                            },\n                            {\n                                nome: \"DeepSeek\",\n                                url: \"https://api.deepseek.com/v1/chat/completions\",\n                                apiKey: \"\",\n                                modeloPadrao: \"deepseek-chat\",\n                                ativo: false\n                            }\n                        ]);\n                    }\n                    if (config.memorias) {\n                        const memoriasArray = Object.values(config.memorias);\n                        setMemories(memoriasArray);\n                    }\n                    if (config.categorias) {\n                        const categoriasArray = Object.values(config.categorias);\n                        setMemoryCategories(categoriasArray);\n                    }\n                } else {\n                    console.log(\"Nenhuma configura\\xe7\\xe3o encontrada, usando padr\\xf5es\");\n                    // Configurações padrão se não existir documento\n                    setAppearanceSettings({\n                        fonte: \"Inter\",\n                        tamanhoFonte: 14,\n                        palavrasPorSessao: 5000\n                    });\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar configura\\xe7\\xf5es:\", error);\n            }\n        };\n        if (isOpen && userData.username) {\n            // Reset do estado do formulário geral quando abrir o modal\n            setGeneralData({\n                username: userData.username,\n                profileImage: userData.profileImage || \"\",\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            loadConfigurations();\n        }\n    }, [\n        isOpen,\n        userData.username,\n        userData.profileImage\n    ]);\n    // Função auxiliar para deletar todos os dados do Storage de um usuário\n    const deleteUserStorageData = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de dados do Storage para:\", username);\n            // Deletar toda a pasta do usuário no Storage\n            const userStorageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.storage, \"usuarios/\".concat(username));\n            const userStorageList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.listAll)(userStorageRef);\n            // Função recursiva para deletar pastas e arquivos\n            const deleteRecursively = async (folderRef)=>{\n                const folderList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.listAll)(folderRef);\n                // Deletar todos os arquivos na pasta atual\n                const fileDeletePromises = folderList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.deleteObject)(item));\n                await Promise.all(fileDeletePromises);\n                // Deletar recursivamente todas as subpastas\n                const folderDeletePromises = folderList.prefixes.map((prefix)=>deleteRecursively(prefix));\n                await Promise.all(folderDeletePromises);\n            };\n            await deleteRecursively(userStorageRef);\n            console.log(\"Todos os dados do Storage deletados para:\", username);\n        } catch (error) {\n            console.log(\"Erro ao deletar dados do Storage ou pasta n\\xe3o encontrada:\", error);\n        }\n    };\n    // Função auxiliar para deletar recursivamente todos os documentos de um usuário\n    const deleteUserDocuments = async (username)=>{\n        try {\n            console.log(\"Iniciando exclus\\xe3o de documentos para:\", username);\n            // Deletar subcoleção de configurações\n            try {\n                const configDoc = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                const configSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configDoc);\n                if (configSnapshot.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(configDoc);\n                    console.log(\"Configura\\xe7\\xf5es deletadas\");\n                }\n            } catch (error) {\n                console.log(\"Erro ao deletar configura\\xe7\\xf5es:\", error);\n            }\n            // Deletar outras subcoleções se existirem (chats, histórico, etc.)\n            try {\n                const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username, \"chats\");\n                const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(chatsCollection);\n                const deletePromises = chatsSnapshot.docs.map((doc)=>(0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(doc.ref));\n                await Promise.all(deletePromises);\n                console.log(\"Chats deletados\");\n            } catch (error) {\n                console.log(\"Erro ao deletar chats:\", error);\n            }\n            // Deletar documento principal do usuário\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)(userDocRef);\n            console.log(\"Documento principal do usu\\xe1rio deletado\");\n        } catch (error) {\n            console.error(\"Erro ao deletar documentos do usu\\xe1rio:\", error);\n            throw error;\n        }\n    };\n    // Atualizar username no documento principal\n    const updateUsername = async function(newUsername) {\n        let showSuccessAlert = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        if (!userData.username || !newUsername || newUsername === userData.username) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio inv\\xe1lido ou igual ao atual.\");\n            return false;\n        }\n        if (newUsername.length < 3) {\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio deve ter pelo menos 3 caracteres.\");\n            return false;\n        }\n        let newUserCreated = false;\n        try {\n            console.log(\"Atualizando username de\", userData.username, \"para\", newUsername);\n            // Verificar se o novo username já existe\n            const newUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername));\n            if (newUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Este nome de usu\\xe1rio j\\xe1 est\\xe1 em uso. Escolha outro.\");\n                return false;\n            }\n            // Buscar o documento atual pelo username antigo\n            const oldUserDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username);\n            const oldUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(oldUserDocRef);\n            if (!oldUserDoc.exists()) {\n                if (showSuccessAlert) alert(\"Usu\\xe1rio n\\xe3o encontrado.\");\n                return false;\n            }\n            const currentData = oldUserDoc.data();\n            // Criar novo documento com o novo username\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername), {\n                ...currentData,\n                username: newUsername,\n                updatedAt: new Date().toISOString()\n            });\n            newUserCreated = true;\n            console.log(\"Novo documento criado para:\", newUsername);\n            // Copiar todas as configurações e subcoleções\n            try {\n                // Copiar configurações principais\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\"));\n                if (configDoc.exists()) {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername, \"configuracoes\", \"settings\"), configDoc.data());\n                    console.log(\"Configura\\xe7\\xf5es copiadas para novo username\");\n                }\n                // Copiar chats se existirem\n                try {\n                    const chatsCollection = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username, \"chats\");\n                    const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(chatsCollection);\n                    for (const chatDoc of chatsSnapshot.docs){\n                        const chatData = chatDoc.data();\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername, \"chats\", chatDoc.id), chatData);\n                    }\n                    if (chatsSnapshot.docs.length > 0) {\n                        console.log(\"\".concat(chatsSnapshot.docs.length, \" chats copiados para novo username\"));\n                    }\n                } catch (chatsError) {\n                    console.log(\"Erro ao copiar chats:\", chatsError);\n                }\n            } catch (configError) {\n                console.log(\"Erro ao copiar dados:\", configError);\n            }\n            // Deletar todos os documentos do usuário antigo\n            await deleteUserDocuments(userData.username);\n            console.log(\"Todos os documentos do usu\\xe1rio antigo foram deletados\");\n            // Atualizar estado local\n            onUserDataUpdate({\n                ...userData,\n                username: newUsername\n            });\n            if (showSuccessAlert) alert(\"Nome de usu\\xe1rio atualizado com sucesso!\");\n            return true;\n        } catch (error) {\n            console.error(\"Erro ao atualizar username:\", error);\n            // Se houve erro e o novo usuário foi criado, tentar fazer rollback\n            if (newUserCreated) {\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", newUsername));\n                    console.log(\"Rollback realizado - novo usu\\xe1rio deletado\");\n                } catch (rollbackError) {\n                    console.error(\"Erro no rollback:\", rollbackError);\n                }\n            }\n            if (showSuccessAlert) alert(\"Erro ao atualizar nome de usu\\xe1rio: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n            return false;\n        }\n    };\n    // Salvar configurações no Firestore\n    const saveConfigurations = async ()=>{\n        if (!userData.username) {\n            alert(\"Erro: usu\\xe1rio n\\xe3o identificado\");\n            return;\n        }\n        try {\n            setLoading(true);\n            // Verificar se o username foi alterado e atualizá-lo primeiro\n            if (generalData.username !== userData.username) {\n                const usernameUpdated = await updateUsername(generalData.username, false);\n                if (!usernameUpdated) {\n                    // Se falhou ao atualizar o username, interromper o processo\n                    return;\n                }\n            }\n            // Determinar qual username usar (o novo se foi alterado)\n            const currentUsername = generalData.username !== userData.username ? generalData.username : userData.username;\n            const configData = {\n                aparencia: {\n                    fonte: appearanceSettings.fonte,\n                    tamanhoFonte: appearanceSettings.tamanhoFonte,\n                    palavrasPorSessao: appearanceSettings.palavrasPorSessao\n                },\n                endpoints: {},\n                memorias: {},\n                categorias: {},\n                updatedAt: new Date().toISOString()\n            };\n            // Converter arrays para objetos\n            aiEndpoints.forEach((endpoint, index)=>{\n                configData.endpoints[endpoint.nome || \"endpoint_\".concat(index)] = endpoint;\n            });\n            memories.forEach((memory, index)=>{\n                configData.memorias[\"memoria_\".concat(index)] = memory;\n            });\n            memoryCategories.forEach((category, index)=>{\n                configData.categorias[category.nome || \"categoria_\".concat(index)] = category;\n            });\n            console.log(\"Salvando configura\\xe7\\xf5es para:\", currentUsername);\n            console.log(\"Dados a serem salvos:\", configData);\n            // Usar setDoc com merge para não sobrescrever outros dados\n            const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", currentUsername, \"configuracoes\", \"settings\");\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)(docRef, configData);\n            console.log(\"Configura\\xe7\\xf5es salvas com sucesso no Firestore\");\n            alert(\"Configura\\xe7\\xf5es salvas com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao salvar configura\\xe7\\xf5es:\", error);\n            alert(\"Erro ao salvar configura\\xe7\\xf5es: \".concat(error instanceof Error ? error.message : \"Erro desconhecido\"));\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    // Funções utilitárias\n    const handleProfileImageUpload = async (file)=>{\n        if (!user) return;\n        try {\n            setLoading(true);\n            const imageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.storage, \"usuarios/\".concat(userData.username, \"/profile.jpg\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.uploadBytes)(imageRef, file);\n            const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.getDownloadURL)(imageRef);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    profileImage: downloadURL\n                }));\n            // Atualizar no Firestore\n            const userDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_6__.db, \"usuarios\", userData.username);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.updateDoc)(userDocRef, {\n                profileImage: downloadURL\n            });\n            onUserDataUpdate({\n                ...userData,\n                profileImage: downloadURL\n            });\n            alert(\"Foto de perfil atualizada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao fazer upload da imagem:\", error);\n            alert(\"Erro ao atualizar foto de perfil.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordChange = async ()=>{\n        if (!user || !generalData.currentPassword || !generalData.newPassword) {\n            alert(\"Preencha todos os campos de senha.\");\n            return;\n        }\n        if (generalData.newPassword !== generalData.confirmPassword) {\n            alert(\"As senhas n\\xe3o coincidem.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const credential = firebase_auth__WEBPACK_IMPORTED_MODULE_5__.EmailAuthProvider.credential(user.email, generalData.currentPassword);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_5__.reauthenticateWithCredential)(user, credential);\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_5__.updatePassword)(user, generalData.newPassword);\n            setGeneralData((prev)=>({\n                    ...prev,\n                    currentPassword: \"\",\n                    newPassword: \"\",\n                    confirmPassword: \"\"\n                }));\n            alert(\"Senha alterada com sucesso!\");\n        } catch (error) {\n            console.error(\"Erro ao alterar senha:\", error);\n            alert(\"Erro ao alterar senha. Verifique a senha atual.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleLogout = async ()=>{\n        if (confirm(\"Tem certeza que deseja sair?\")) {\n            await logout();\n            onClose();\n        }\n    };\n    // Funções para gerenciar endpoints de IA\n    const handleAddEndpoint = ()=>{\n        if (!newEndpoint.nome || !newEndpoint.url || !newEndpoint.apiKey) {\n            alert(\"Preencha todos os campos obrigat\\xf3rios.\");\n            return;\n        }\n        setAiEndpoints((prev)=>[\n                ...prev,\n                {\n                    ...newEndpoint\n                }\n            ]);\n        setNewEndpoint({\n            nome: \"\",\n            url: \"\",\n            apiKey: \"\",\n            modeloPadrao: \"\",\n            ativo: false\n        });\n        setShowAddEndpoint(false);\n        alert(\"Endpoint adicionado com sucesso!\");\n    };\n    const handleToggleEndpoint = (index)=>{\n        setAiEndpoints((prev)=>prev.map((endpoint, i)=>i === index ? {\n                    ...endpoint,\n                    ativo: !endpoint.ativo\n                } : endpoint));\n    };\n    const handleDeleteEndpoint = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar este endpoint?\")) {\n            setAiEndpoints((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleTestEndpoint = async (endpoint)=>{\n        if (!endpoint.apiKey) {\n            alert(\"API Key \\xe9 necess\\xe1ria para testar o endpoint.\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const response = await fetch(endpoint.url, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(endpoint.apiKey)\n                },\n                body: JSON.stringify({\n                    model: endpoint.modeloPadrao || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: \"Test message\"\n                        }\n                    ],\n                    max_tokens: 10\n                })\n            });\n            if (response.ok) {\n                alert(\"✅ Endpoint testado com sucesso!\");\n            } else {\n                alert(\"❌ Erro ao testar endpoint. Verifique as configura\\xe7\\xf5es.\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao testar endpoint:\", error);\n            alert(\"❌ Erro ao conectar com o endpoint.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Funções para gerenciar memórias\n    const handleAddCategory = ()=>{\n        if (!newCategory.nome) {\n            alert(\"Nome da categoria \\xe9 obrigat\\xf3rio.\");\n            return;\n        }\n        setMemoryCategories((prev)=>[\n                ...prev,\n                {\n                    ...newCategory\n                }\n            ]);\n        setNewCategory({\n            nome: \"\",\n            descricao: \"\",\n            cor: \"#3B82F6\"\n        });\n        setShowAddCategory(false);\n        alert(\"Categoria criada com sucesso!\");\n    };\n    const handleAddMemory = ()=>{\n        if (!newMemory.titulo || !newMemory.conteudo) {\n            alert(\"T\\xedtulo e conte\\xfado s\\xe3o obrigat\\xf3rios.\");\n            return;\n        }\n        setMemories((prev)=>[\n                ...prev,\n                {\n                    ...newMemory\n                }\n            ]);\n        setNewMemory({\n            titulo: \"\",\n            conteudo: \"\",\n            cor: \"#3B82F6\",\n            categoria: null,\n            chatId: null,\n            global: true\n        });\n        setShowAddMemory(false);\n        alert(\"Mem\\xf3ria criada com sucesso!\");\n    };\n    const handleDeleteMemory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta mem\\xf3ria?\")) {\n            setMemories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const handleDeleteCategory = (index)=>{\n        if (confirm(\"Tem certeza que deseja deletar esta categoria?\")) {\n            setMemoryCategories((prev)=>prev.filter((_, i)=>i !== index));\n        }\n    };\n    const colors = [\n        \"#3B82F6\",\n        \"#EF4444\",\n        \"#10B981\",\n        \"#F59E0B\",\n        \"#8B5CF6\",\n        \"#EC4899\",\n        \"#06B6D4\",\n        \"#84CC16\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                initial: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1\n                },\n                exit: {\n                    scale: 0.95,\n                    opacity: 0\n                },\n                className: \"bg-gradient-to-br from-blue-900/95 to-blue-800/95 backdrop-blur-sm border border-white/20 rounded-2xl w-full max-w-6xl max-h-[95vh] overflow-hidden mx-4 lg:mx-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 lg:p-6 border-b border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl lg:text-2xl font-bold text-white\",\n                                        children: \"Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-sm lg:hidden mt-1\",\n                                        children: [\n                                            activeTab === \"geral\" && \"Informa\\xe7\\xf5es pessoais e senha\",\n                                            activeTab === \"aparencia\" && \"Personaliza\\xe7\\xe3o da interface\",\n                                            activeTab === \"ia\" && \"Endpoints de intelig\\xeancia artificial\",\n                                            activeTab === \"memoria\" && \"Sistema de mem\\xf3rias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-white/60 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 657,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-64 bg-white/5 border-b lg:border-b-0 lg:border-r border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"p-2 lg:p-4 space-y-1 lg:space-y-2 overflow-x-auto lg:overflow-x-visible\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2 min-w-max lg:min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"geral\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"geral\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Geral\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 684,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"aparencia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"aparencia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"ia\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"ia\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"IA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"memoria\"),\n                                                className: \"w-full lg:w-auto text-left px-3 lg:px-4 py-2 lg:py-3 rounded-lg transition-all duration-200 flex items-center space-x-2 lg:space-x-3 whitespace-nowrap \".concat(activeTab === \"memoria\" ? \"bg-blue-600 text-white shadow-lg\" : \"text-white/70 hover:text-white hover:bg-white/10\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 lg:w-5 h-4 lg:h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-sm lg:text-base\",\n                                                        children: \"Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 717,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 lg:p-6 overflow-y-auto max-h-[calc(95vh-200px)]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: [\n                                        activeTab === \"geral\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es Gerais\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Foto de Perfil\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative\",\n                                                                        children: generalData.profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: generalData.profileImage,\n                                                                            alt: \"Profile\",\n                                                                            className: \"w-20 h-20 rounded-full object-cover border-2 border-white/20\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-white font-bold text-2xl\",\n                                                                                children: userData.username.charAt(0).toUpperCase()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 762,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    var _fileInputRef_current;\n                                                                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                                                                },\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                                children: \"Alterar Foto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 769,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm mt-2\",\n                                                                                children: \"JPG, PNG ou GIF. M\\xe1ximo 5MB.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 768,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 752,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                ref: fileInputRef,\n                                                                type: \"file\",\n                                                                accept: \"image/*\",\n                                                                onChange: (e)=>{\n                                                                    var _e_target_files;\n                                                                    const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                    if (file) handleProfileImageUpload(file);\n                                                                },\n                                                                className: \"hidden\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nome de Usu\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: generalData.username,\n                                                                        onChange: (e)=>setGeneralData((prev)=>({\n                                                                                    ...prev,\n                                                                                    username: e.target.value\n                                                                                })),\n                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                        placeholder: \"Digite seu nome de usu\\xe1rio\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    generalData.username !== userData.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-yellow-300 text-sm\",\n                                                                            children: '⚠️ Nome de usu\\xe1rio alterado. Clique em \"Salvar Configura\\xe7\\xf5es\" para aplicar as mudan\\xe7as.'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 808,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 807,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 796,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Alterar Senha\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 818,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Senha Atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 821,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.currentPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            currentPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua senha atual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 824,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 835,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.newPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            newPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Digite sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 838,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 834,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Confirmar Nova Senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: generalData.confirmPassword,\n                                                                                onChange: (e)=>setGeneralData((prev)=>({\n                                                                                            ...prev,\n                                                                                            confirmPassword: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Confirme sua nova senha\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 852,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 848,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handlePasswordChange,\n                                                                        disabled: loading,\n                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: loading ? \"Alterando...\" : \"Alterar Senha\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 862,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"geral\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"aparencia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Configura\\xe7\\xf5es de Apar\\xeancia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Fonte do Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Fam\\xedlia da Fonte\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 892,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                value: appearanceSettings.fonte,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            fonte: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Inter\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Inter\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 902,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Roboto\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Roboto\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 903,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"JetBrains Mono\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"JetBrains Mono\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 904,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Lato\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Lato\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 905,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Fira Code\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Fira Code\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 906,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Merriweather\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Merriweather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 907,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Open Sans\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Open Sans\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 908,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Source Sans Pro\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Source Sans Pro\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 909,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Poppins\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Poppins\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 910,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: \"Nunito\",\n                                                                                        className: \"bg-gray-800\",\n                                                                                        children: \"Nunito\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 911,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 895,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-lg p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm mb-2\",\n                                                                                children: \"Pr\\xe9-visualiza\\xe7\\xe3o:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 916,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-white p-3 bg-white/5 rounded border border-white/10\",\n                                                                                style: {\n                                                                                    fontFamily: appearanceSettings.fonte,\n                                                                                    fontSize: \"\".concat(appearanceSettings.tamanhoFonte, \"px\")\n                                                                                },\n                                                                                children: \"Esta \\xe9 uma mensagem de exemplo para visualizar a fonte selecionada. Lorem ipsum dolor sit amet, consectetur adipiscing elit.\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 917,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 915,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Tamanho da Fonte\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 930,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                            children: [\n                                                                                \"Tamanho: \",\n                                                                                appearanceSettings.tamanhoFonte,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 933,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"range\",\n                                                                            min: \"10\",\n                                                                            max: \"24\",\n                                                                            value: appearanceSettings.tamanhoFonte,\n                                                                            onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                        ...prev,\n                                                                                        tamanhoFonte: parseInt(e.target.value)\n                                                                                    })),\n                                                                            className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 936,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"10px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"24px\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                    lineNumber: 932,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Sess\\xf5es de Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                        className: \"text-white font-medium\",\n                                                                                        children: \"Divis\\xe3o Autom\\xe1tica\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 958,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white/60 text-sm\",\n                                                                                        children: \"Dividir chats longos em sess\\xf5es baseadas na contagem de palavras\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 959,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 957,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"bg-blue-600 relative inline-flex h-6 w-11 items-center rounded-full transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"translate-x-6 inline-block h-4 w-4 transform rounded-full bg-white transition\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 963,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 956,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: [\n                                                                                    \"Palavras por Sess\\xe3o: \",\n                                                                                    appearanceSettings.palavrasPorSessao.toLocaleString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 971,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"range\",\n                                                                                min: \"1000\",\n                                                                                max: \"20000\",\n                                                                                step: \"500\",\n                                                                                value: appearanceSettings.palavrasPorSessao,\n                                                                                onChange: (e)=>setAppearanceSettings((prev)=>({\n                                                                                            ...prev,\n                                                                                            palavrasPorSessao: parseInt(e.target.value)\n                                                                                        })),\n                                                                                className: \"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 974,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"1.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 984,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"20.000\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 985,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 983,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-blue-300 text-sm\",\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCA1 \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"Dica:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                    lineNumber: 991,\n                                                                                    columnNumber: 36\n                                                                                }, this),\n                                                                                \" Sess\\xf5es menores carregam mais r\\xe1pido, mas podem fragmentar conversas longas. Recomendamos entre 3.000-8.000 palavras para melhor experi\\xeancia.\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 990,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 989,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"aparencia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 877,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"ia\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Intelig\\xeancia Artificial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80\",\n                                                                children: \"Gerencie seus endpoints de IA personalizados\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddEndpoint(!showAddEndpoint),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 4v16m8-8H4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1023,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1022,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Adicionar Endpoint\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1025,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1017,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Novo Endpoint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1037,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1040,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.nome,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Meu Endpoint\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1043,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1039,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"URL do Endpoint *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1054,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"url\",\n                                                                                value: newEndpoint.url,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            url: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"https://api.exemplo.com/v1/chat/completions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1057,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1053,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"API Key *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1068,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"password\",\n                                                                                value: newEndpoint.apiKey,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            apiKey: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"sk-...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1071,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1067,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Modelo Padr\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1082,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newEndpoint.modeloPadrao,\n                                                                                onChange: (e)=>setNewEndpoint((prev)=>({\n                                                                                            ...prev,\n                                                                                            modeloPadrao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"gpt-3.5-turbo\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1085,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1038,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddEndpoint(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddEndpoint,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Adicionar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1103,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: aiEndpoints.map((endpoint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-3 h-3 rounded-full \".concat(endpoint.ativo ? \"bg-green-500\" : \"bg-gray-500\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1120,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-lg font-semibold text-white\",\n                                                                                        children: endpoint.nome\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1121,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                        children: \"Pr\\xe9-configurado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1123,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1119,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 \".concat(endpoint.ativo ? \"bg-green-600 hover:bg-green-700 text-white\" : \"bg-gray-600 hover:bg-gray-700 text-white\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1129,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleTestEndpoint(endpoint),\n                                                                                        disabled: loading || !endpoint.apiKey,\n                                                                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Testar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1139,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    endpoint.nome !== \"OpenRouter\" && endpoint.nome !== \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteEndpoint(index),\n                                                                                        className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Deletar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1148,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1118,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"URL:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1161,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs break-all\",\n                                                                                        children: endpoint.url\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1162,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1160,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Modelo:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1165,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white\",\n                                                                                        children: endpoint.modeloPadrao || \"N\\xe3o especificado\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1166,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1164,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"API Key:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1169,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-white font-mono text-xs\",\n                                                                                        children: endpoint.apiKey ? \"••••••••••••\" + endpoint.apiKey.slice(-4) : \"N\\xe3o configurada\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1170,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1168,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/60\",\n                                                                                        children: \"Status:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1175,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium \".concat(endpoint.ativo ? \"text-green-400\" : \"text-gray-400\"),\n                                                                                        children: endpoint.ativo ? \"Ativo\" : \"Inativo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1176,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1174,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1159,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    (endpoint.nome === \"OpenRouter\" || endpoint.nome === \"DeepSeek\") && !endpoint.apiKey && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-4 pt-4 border-t border-white/10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Configure sua API Key:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1185,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"password\",\n                                                                                        placeholder: \"Cole sua API Key aqui...\",\n                                                                                        className: \"flex-1 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\",\n                                                                                        onChange: (e)=>{\n                                                                                            const newKey = e.target.value;\n                                                                                            setAiEndpoints((prev)=>prev.map((ep, i)=>i === index ? {\n                                                                                                        ...ep,\n                                                                                                        apiKey: newKey\n                                                                                                    } : ep));\n                                                                                        }\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1189,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleToggleEndpoint(index),\n                                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                                                        children: \"Salvar\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1202,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1188,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1184,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1117,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1115,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1009,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"ia\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 21\n                                        }, this),\n                                        activeTab === \"memoria\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                x: -20\n                                            },\n                                            className: \"space-y-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-6\",\n                                                        children: \"Sistema de Mem\\xf3ria\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddCategory(!showAddCategory),\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1238,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1241,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1232,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setShowAddMemory(!showAddMemory),\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1249,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1248,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Nova Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1252,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1243,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1231,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    showAddCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Categoria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1264,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Nome da Categoria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1267,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newCategory.nome,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            nome: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Trabalho, Pessoal, Projetos...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1270,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1266,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Descri\\xe7\\xe3o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1281,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newCategory.descricao,\n                                                                                onChange: (e)=>setNewCategory((prev)=>({\n                                                                                            ...prev,\n                                                                                            descricao: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 3,\n                                                                                placeholder: \"Descreva o prop\\xf3sito desta categoria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1284,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1280,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Cor da Categoria\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1295,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex space-x-2\",\n                                                                                children: colors.map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>setNewCategory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    cor: color\n                                                                                                })),\n                                                                                        className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newCategory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                        style: {\n                                                                                            backgroundColor: color\n                                                                                        }\n                                                                                    }, color, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1300,\n                                                                                        columnNumber: 37\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1298,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1294,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1265,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddCategory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1313,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddCategory,\n                                                                        className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Categoria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1319,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1312,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1258,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    showAddMemory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            height: \"auto\"\n                                                        },\n                                                        exit: {\n                                                            opacity: 0,\n                                                            height: 0\n                                                        },\n                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Nova Mem\\xf3ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"T\\xedtulo da Mem\\xf3ria *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1341,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: newMemory.titulo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            titulo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                placeholder: \"Ex: Informa\\xe7\\xf5es importantes sobre...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1344,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1340,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                children: \"Conte\\xfado *\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1355,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                value: newMemory.conteudo,\n                                                                                onChange: (e)=>setNewMemory((prev)=>({\n                                                                                            ...prev,\n                                                                                            conteudo: e.target.value\n                                                                                        })),\n                                                                                className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                                                                rows: 4,\n                                                                                placeholder: \"Digite o conte\\xfado da mem\\xf3ria...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1358,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Categoria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1370,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: newMemory.categoria || \"\",\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    categoria: e.target.value || null\n                                                                                                })),\n                                                                                        className: \"w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: \"\",\n                                                                                                className: \"bg-gray-800\",\n                                                                                                children: \"Sem categoria\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1380,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: category.nome,\n                                                                                                    className: \"bg-gray-800\",\n                                                                                                    children: category.nome\n                                                                                                }, index, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                    lineNumber: 1382,\n                                                                                                    columnNumber: 39\n                                                                                                }, this))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1373,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-white/80 text-sm font-medium mb-2\",\n                                                                                        children: \"Cor da Mem\\xf3ria\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1389,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex space-x-2\",\n                                                                                        children: colors.slice(0, 4).map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                onClick: ()=>setNewMemory((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            cor: color\n                                                                                                        })),\n                                                                                                className: \"w-8 h-8 rounded-full border-2 transition-all duration-200 \".concat(newMemory.cor === color ? \"border-white scale-110\" : \"border-white/30\"),\n                                                                                                style: {\n                                                                                                    backgroundColor: color\n                                                                                                }\n                                                                                            }, color, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1394,\n                                                                                                columnNumber: 39\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1392,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1388,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"flex items-center space-x-2 cursor-pointer\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: newMemory.global,\n                                                                                        onChange: (e)=>setNewMemory((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    global: e.target.checked\n                                                                                                })),\n                                                                                        className: \"w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500 focus:ring-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1408,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white/80 text-sm\",\n                                                                                        children: \"Mem\\xf3ria Global\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1415,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1407,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/50 text-xs\",\n                                                                                children: \"Mem\\xf3rias globais ficam dispon\\xedveis em todos os chats\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1417,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1339,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end space-x-3 mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowAddMemory(false),\n                                                                        className: \"px-4 py-2 text-white/70 hover:text-white transition-colors\",\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleAddMemory,\n                                                                        className: \"bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                                                        children: \"Criar Mem\\xf3ria\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1429,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1422,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    memoryCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: \"Categorias\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                                children: memoryCategories.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between mb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full\",\n                                                                                                style: {\n                                                                                                    backgroundColor: category.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1449,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"text-white font-medium\",\n                                                                                                children: category.nome\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1453,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1448,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteCategory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1460,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1459,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1455,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1447,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            category.descricao && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/60 text-sm\",\n                                                                                children: category.descricao\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1466,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1446,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1442,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                                children: [\n                                                                    \"Mem\\xf3rias (\",\n                                                                    memories.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1476,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            memories.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-white/5 border border-white/10 rounded-xl p-8 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-12 h-12 text-white/40 mx-auto mb-4\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                            lineNumber: 1482,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1481,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60\",\n                                                                        children: \"Nenhuma mem\\xf3ria criada ainda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1485,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/40 text-sm mt-1\",\n                                                                        children: 'Clique em \"Nova Mem\\xf3ria\" para come\\xe7ar'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1480,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: memories.map((memory, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white/5 border border-white/10 rounded-xl p-6\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-start justify-between mb-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center space-x-3\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"w-4 h-4 rounded-full flex-shrink-0\",\n                                                                                                style: {\n                                                                                                    backgroundColor: memory.cor\n                                                                                                }\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1496,\n                                                                                                columnNumber: 39\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                        className: \"text-white font-semibold\",\n                                                                                                        children: memory.titulo\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1501,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center space-x-2 mt-1\",\n                                                                                                        children: [\n                                                                                                            memory.categoria && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"bg-blue-500/20 text-blue-300 text-xs px-2 py-1 rounded-full\",\n                                                                                                                children: memory.categoria\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1504,\n                                                                                                                columnNumber: 45\n                                                                                                            }, this),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(memory.global ? \"bg-green-500/20 text-green-300\" : \"bg-orange-500/20 text-orange-300\"),\n                                                                                                                children: memory.global ? \"Global\" : \"Chat Espec\\xedfico\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                                lineNumber: 1508,\n                                                                                                                columnNumber: 43\n                                                                                                            }, this)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                        lineNumber: 1502,\n                                                                                                        columnNumber: 41\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1500,\n                                                                                                columnNumber: 39\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1495,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        onClick: ()=>handleDeleteMemory(index),\n                                                                                        className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                            className: \"w-4 h-4\",\n                                                                                            fill: \"none\",\n                                                                                            stroke: \"currentColor\",\n                                                                                            viewBox: \"0 0 24 24\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                                lineNumber: 1523,\n                                                                                                columnNumber: 41\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                            lineNumber: 1522,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                        lineNumber: 1518,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1494,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-white/80 text-sm leading-relaxed\",\n                                                                                children: memory.conteudo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                                lineNumber: 1528,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                        lineNumber: 1493,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                                lineNumber: 1491,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                        lineNumber: 1475,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, \"memoria\", false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                            lineNumber: 1220,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-t border-white/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                    children: \"Sair da Conta\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                    lineNumber: 1546,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1545,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onClose,\n                                        className: \"px-6 py-2 text-white/70 hover:text-white transition-colors font-medium\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1558,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveConfigurations,\n                                        disabled: loading,\n                                        className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-all duration-200 font-medium\",\n                                        children: loading ? \"Salvando...\" : \"Salvar Configura\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                        lineNumber: 1564,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                                lineNumber: 1557,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                        lineNumber: 1544,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n                lineNumber: 637,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n            lineNumber: 631,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\SettingsModal.tsx\",\n        lineNumber: 629,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsModal, \"L2tjI/Brbkl7ZOlVDnhykGhAjvE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SettingsModal;\nvar _c;\n$RefreshReg$(_c, \"SettingsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\n"));

/***/ })

});