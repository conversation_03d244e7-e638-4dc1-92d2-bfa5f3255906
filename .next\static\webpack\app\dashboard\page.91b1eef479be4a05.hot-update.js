"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/openRouterService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/openRouterService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   openRouterService: function() { return /* binding */ openRouterService; }\n/* harmony export */ });\nclass OpenRouterService {\n    async fetchModels() {\n        // Verificar cache\n        if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {\n            return this.cache.models;\n        }\n        try {\n            const response = await fetch(\"https://openrouter.ai/api/v1/models\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(\"your-openrouter-api-key-here\" || 0),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            const models = data.data.map((model)=>({\n                    id: model.id,\n                    name: model.name,\n                    description: model.description || \"\",\n                    context_length: model.context_length,\n                    pricing: {\n                        prompt: model.pricing.prompt,\n                        completion: model.pricing.completion,\n                        image: model.pricing.image\n                    },\n                    architecture: model.architecture,\n                    created: model.created,\n                    isFavorite: false\n                }));\n            // Atualizar cache\n            this.cache = {\n                models,\n                timestamp: Date.now()\n            };\n            return models;\n        } catch (error) {\n            console.error(\"Error fetching OpenRouter models:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.cache) {\n                return this.cache.models;\n            }\n            throw error;\n        }\n    }\n    async fetchCredits(apiKey) {\n        // Verificar cache\n        if (this.creditsCache && Date.now() - this.creditsCache.timestamp < this.CREDITS_CACHE_DURATION) {\n            const balance = this.creditsCache.credits.total_credits - this.creditsCache.credits.total_usage;\n            return {\n                balance\n            };\n        }\n        try {\n            const response = await fetch(\"https://openrouter.ai/api/v1/credits\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(apiKey),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            const credits = data.data;\n            // Atualizar cache\n            this.creditsCache = {\n                credits,\n                timestamp: Date.now()\n            };\n            const balance = credits.total_credits - credits.total_usage;\n            return {\n                balance\n            };\n        } catch (error) {\n            console.error(\"Error fetching OpenRouter credits:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.creditsCache) {\n                const balance = this.creditsCache.credits.total_credits - this.creditsCache.credits.total_usage;\n                return {\n                    balance\n                };\n            }\n            return {\n                balance: 0,\n                error: error instanceof Error ? error.message : \"Erro desconhecido\"\n            };\n        }\n    }\n    filterByCategory(models, category) {\n        switch(category){\n            case \"free\":\n                return models.filter((model)=>this.isFreeModel(model));\n            case \"paid\":\n                return models.filter((model)=>!this.isFreeModel(model));\n            case \"favorites\":\n                return models.filter((model)=>model.isFavorite);\n            default:\n                return models;\n        }\n    }\n    sortModels(models, sortBy) {\n        const sortedModels = [\n            ...models\n        ];\n        switch(sortBy){\n            case \"newest\":\n                return sortedModels.sort((a, b)=>(b.created || 0) - (a.created || 0));\n            case \"price_low\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(a) - this.getTotalPrice(b));\n            case \"price_high\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(b) - this.getTotalPrice(a));\n            case \"context_high\":\n                return sortedModels.sort((a, b)=>b.context_length - a.context_length);\n            default:\n                return sortedModels;\n        }\n    }\n    isFreeModel(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice === 0 && completionPrice === 0;\n    }\n    getTotalPrice(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice + completionPrice;\n    }\n    formatPrice(price) {\n        const numPrice = parseFloat(price) * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens\n        if (numPrice === 0) return \"Gr\\xe1tis\";\n        if (numPrice < 0.001) return \"< $0.001\";\n        return \"$\".concat(numPrice.toFixed(3));\n    }\n    formatContextLength(length) {\n        return length.toLocaleString(); // Mostra o número completo com separadores de milhares\n    }\n    // Busca avançada com fuzzy matching\n    searchModels(models, searchTerm) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n        if (!searchTerm.trim()) {\n            return [];\n        }\n        const term = searchTerm.toLowerCase();\n        const results = [];\n        for (const model of models){\n            let score = 0;\n            const matchedFields = [];\n            let highlightedName = model.name;\n            let highlightedDescription = model.description || \"\";\n            // Busca no nome (peso maior)\n            if (model.name.toLowerCase().includes(term)) {\n                score += 10;\n                matchedFields.push(\"name\");\n                highlightedName = this.highlightText(model.name, term);\n            }\n            // Busca no ID (peso médio)\n            if (model.id.toLowerCase().includes(term)) {\n                score += 7;\n                matchedFields.push(\"id\");\n            }\n            // Busca na descrição (peso menor)\n            if (model.description && model.description.toLowerCase().includes(term)) {\n                score += 3;\n                matchedFields.push(\"description\");\n                highlightedDescription = this.highlightText(model.description, term);\n            }\n            // Boost para favoritos\n            if (boostFavorites && model.isFavorite) {\n                score *= 1.5;\n            }\n            // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n            if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && this.isFreeModel(model)) {\n                score += 5;\n            }\n            // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n            if ((term.includes(\"expensive\") || term.includes(\"caro\")) && this.getTotalPrice(model) > 0.00002) {\n                score += 5;\n            }\n            if (score > 0) {\n                results.push({\n                    model,\n                    score,\n                    matchedFields,\n                    highlightedName,\n                    highlightedDescription\n                });\n            }\n        }\n        // Ordenar por score e limitar resultados\n        return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n    }\n    highlightText(text, term) {\n        const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n        return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n    }\n    // Limpar cache\n    clearCache() {\n        this.cache = null;\n    }\n    constructor(){\n        this.cache = null;\n        this.creditsCache = null;\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.CREDITS_CACHE_DURATION = 30 * 1000 // 30 segundos para créditos\n        ;\n    }\n}\nconst openRouterService = new OpenRouterService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/openRouterService.ts\n"));

/***/ })

});