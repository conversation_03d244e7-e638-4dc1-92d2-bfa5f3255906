"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-parse5";
exports.ids = ["vendor-chunks/hast-util-from-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-parse5/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromParse5: () => (/* binding */ fromParse5)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var vfile_location__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-location */ \"(ssr)/./node_modules/vfile-location/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/**\n * @import {ElementData, Element, Nodes, RootContent, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nfunction fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = (0,vfile_location__WEBPACK_IMPORTED_MODULE_1__.location)(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start, 'expected `start`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces.svg ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? hastscript__WEBPACK_IMPORTED_MODULE_4__.s : hastscript__WEBPACK_IMPORTED_MODULE_4__.h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[(0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\n");

/***/ })

};
;