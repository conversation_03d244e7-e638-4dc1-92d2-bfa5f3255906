"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* harmony import */ var _styles_markdown_advanced_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-advanced.css */ \"(app-pages-browser)/./src/styles/markdown-advanced.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\", hasWebSearch = false } = param;\n    // Função para detectar se o conteúdo contém citações de web search\n    const detectWebSearch = (text)=>{\n        // Detecta links no formato [dominio.com] que são característicos do web search\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern);\n        return matches !== null && matches.length > 0;\n    };\n    // Função para contar e extrair informações sobre as fontes\n    const getWebSearchInfo = (text)=>{\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern) || [];\n        const sourceSet = new Set(matches.map((match)=>match.slice(1, -1))); // Remove [ e ]\n        const uniqueSources = Array.from(sourceSet);\n        return {\n            sourceCount: matches.length,\n            sources: uniqueSources\n        };\n    };\n    const isWebSearchMessage = hasWebSearch || detectWebSearch(content);\n    const webSearchInfo = isWebSearchMessage ? getWebSearchInfo(content) : {\n        sourceCount: 0,\n        sources: []\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: [\n            isWebSearchMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-section mb-4 p-3 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-cyan-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-300 text-sm font-medium\",\n                                children: \"\\uD83C\\uDF10 Busca na Web Ativada\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-200/80 leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-300 font-medium\",\n                                children: [\n                                    webSearchInfo.sourceCount,\n                                    \" cita\\xe7\\xe3o\",\n                                    webSearchInfo.sourceCount !== 1 ? \"\\xf5es\" : \"\",\n                                    \" de \",\n                                    webSearchInfo.sources.length,\n                                    \" fonte\",\n                                    webSearchInfo.sources.length !== 1 ? \"s\" : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined),\n                            webSearchInfo.sources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1 flex flex-wrap gap-1\",\n                                children: webSearchInfo.sources.map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-block px-2 py-0.5 bg-cyan-600/20 text-cyan-200 rounded text-xs border border-cyan-500/30\",\n                                        children: source\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                remarkPlugins: [\n                    remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                ],\n                rehypePlugins: [\n                    rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    [\n                        rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        {\n                            detect: true,\n                            ignoreMissing: true\n                        }\n                    ]\n                ],\n                components: {\n                    // Customizar renderização de código\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || \"\");\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"bg-gray-800 rounded-lg p-4 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de links\n                    a (param) {\n                        let { children, href, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-blue-400 hover:text-blue-300 underline\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de tabelas\n                    table (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse border border-gray-600\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0);\n                    },\n                    th (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    td (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"border border-gray-600 px-4 py-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de blockquotes\n                    blockquote (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de listas\n                    ul (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    ol (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de títulos\n                    h1 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h2 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h3 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de parágrafos\n                    p (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 leading-relaxed text-gray-200\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de linha horizontal\n                    hr (param) {\n                        let { ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"border-gray-600 my-6\",\n                            ...props\n                        }, void 0, false, void 0, void 0);\n                    }\n                },\n                children: mainContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});