"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-math";
exports.ids = ["vendor-chunks/remark-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-math/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/remark-math/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkMath)\n/* harmony export */ });\n/* harmony import */ var mdast_util_math__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-math */ \"(ssr)/./node_modules/mdast-util-math/lib/index.js\");\n/* harmony import */ var micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-extension-math */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\");\n/// <reference types=\"mdast-util-math\" />\n/// <reference types=\"remark-parse\" />\n/// <reference types=\"remark-stringify\" />\n\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-math').ToOptions} Options\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n\n\n\n/** @type {Readonly<Options>} */\nconst emptyOptions = {}\n\n/**\n * Add support for math.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction remarkMath(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push((0,micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__.math)(settings))\n  fromMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathFromMarkdown)())\n  toMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathToMarkdown)(settings))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-math/lib/index.js\n");

/***/ })

};
;