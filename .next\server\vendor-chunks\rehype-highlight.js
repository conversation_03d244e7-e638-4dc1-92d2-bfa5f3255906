"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-highlight";
exports.ids = ["vendor-chunks/rehype-highlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-highlight/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/rehype-highlight/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeHighlight)\n/* harmony export */ });\n/* harmony import */ var hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-text */ \"(ssr)/./node_modules/hast-util-to-text/lib/index.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/common.js\");\n/* harmony import */ var lowlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lowlight */ \"(ssr)/./node_modules/lowlight/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @import {ElementContent, Element, Root} from 'hast'\n * @import {LanguageFn} from 'lowlight'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {Readonly<Record<string, ReadonlyArray<string> | string>> | null | undefined} [aliases={}]\n *   Register more aliases (optional);\n *   passed to `lowlight.registerAlias`.\n * @property {boolean | null | undefined} [detect=false]\n *   Highlight code without language classes by guessing its programming\n *   language (default: `false`).\n * @property {Readonly<Record<string, LanguageFn>> | null | undefined} [languages]\n *   Register languages (default: `common`);\n *   passed to `lowlight.register`.\n * @property {ReadonlyArray<string> | null | undefined} [plainText=[]]\n *   List of language names to not highlight (optional);\n *   note you can also add `no-highlight` classes.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   Names of languages to check when detecting (default: all registered\n *   languages).\n */\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Apply syntax highlighting.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeHighlight(options) {\n  const settings = options || emptyOptions\n  const aliases = settings.aliases\n  const detect = settings.detect || false\n  const languages = settings.languages || lowlight__WEBPACK_IMPORTED_MODULE_0__.grammars\n  const plainText = settings.plainText\n  const prefix = settings.prefix\n  const subset = settings.subset\n  let name = 'hljs'\n\n  const lowlight = (0,lowlight__WEBPACK_IMPORTED_MODULE_1__.createLowlight)(languages)\n\n  if (aliases) {\n    lowlight.registerAlias(aliases)\n  }\n\n  if (prefix) {\n    const pos = prefix.indexOf('-')\n    name = pos === -1 ? prefix : prefix.slice(0, pos)\n  }\n\n  /**\n   * Transform.\n   *\n   * @param {Root} tree\n   *   Tree.\n   * @param {VFile} file\n   *   File.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree, file) {\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(tree, 'element', function (node, _, parent) {\n      if (\n        node.tagName !== 'code' ||\n        !parent ||\n        parent.type !== 'element' ||\n        parent.tagName !== 'pre'\n      ) {\n        return\n      }\n\n      const lang = language(node)\n\n      if (\n        lang === false ||\n        (!lang && !detect) ||\n        (lang && plainText && plainText.includes(lang))\n      ) {\n        return\n      }\n\n      if (!Array.isArray(node.properties.className)) {\n        node.properties.className = []\n      }\n\n      if (!node.properties.className.includes(name)) {\n        node.properties.className.unshift(name)\n      }\n\n      const text = (0,hast_util_to_text__WEBPACK_IMPORTED_MODULE_3__.toText)(node, {whitespace: 'pre'})\n      /** @type {Root} */\n      let result\n\n      try {\n        result = lang\n          ? lowlight.highlight(lang, text, {prefix})\n          : lowlight.highlightAuto(text, {prefix, subset})\n      } catch (error) {\n        const cause = /** @type {Error} */ (error)\n\n        if (lang && /Unknown language/.test(cause.message)) {\n          file.message(\n            'Cannot highlight as `' + lang + '`, it’s not registered',\n            {\n              ancestors: [parent, node],\n              cause,\n              place: node.position,\n              ruleId: 'missing-language',\n              source: 'rehype-highlight'\n            }\n          )\n\n          /* c8 ignore next 5 -- throw arbitrary hljs errors */\n          return\n        }\n\n        throw cause\n      }\n\n      if (!lang && result.data && result.data.language) {\n        node.properties.className.push('language-' + result.data.language)\n      }\n\n      if (result.children.length > 0) {\n        node.children = /** @type {Array<ElementContent>} */ (result.children)\n      }\n    })\n  }\n}\n\n/**\n * Get the programming language of `node`.\n *\n * @param {Element} node\n *   Node.\n * @returns {false | string | undefined}\n *   Language or `undefined`, or `false` when an explikcit `no-highlight` class\n *   is used.\n */\nfunction language(node) {\n  const list = node.properties.className\n  let index = -1\n\n  if (!Array.isArray(list)) {\n    return\n  }\n\n  /** @type {string | undefined} */\n  let name\n\n  while (++index < list.length) {\n    const value = String(list[index])\n\n    if (value === 'no-highlight' || value === 'nohighlight') {\n      return false\n    }\n\n    if (!name && value.slice(0, 5) === 'lang-') {\n      name = value.slice(5)\n    }\n\n    if (!name && value.slice(0, 9) === 'language-') {\n      name = value.slice(9)\n    }\n  }\n\n  return name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-highlight/lib/index.js\n");

/***/ })

};
;