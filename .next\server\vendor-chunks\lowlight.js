"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lowlight";
exports.ids = ["vendor-chunks/lowlight"];
exports.modules = {

/***/ "(ssr)/./node_modules/lowlight/lib/common.js":
/*!*********************************************!*\
  !*** ./node_modules/lowlight/lib/common.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   grammars: () => (/* binding */ grammars)\n/* harmony export */ });\n/* harmony import */ var highlight_js_lib_languages_arduino__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! highlight.js/lib/languages/arduino */ \"(ssr)/./node_modules/highlight.js/es/languages/arduino.js\");\n/* harmony import */ var highlight_js_lib_languages_bash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! highlight.js/lib/languages/bash */ \"(ssr)/./node_modules/highlight.js/es/languages/bash.js\");\n/* harmony import */ var highlight_js_lib_languages_c__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! highlight.js/lib/languages/c */ \"(ssr)/./node_modules/highlight.js/es/languages/c.js\");\n/* harmony import */ var highlight_js_lib_languages_cpp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! highlight.js/lib/languages/cpp */ \"(ssr)/./node_modules/highlight.js/es/languages/cpp.js\");\n/* harmony import */ var highlight_js_lib_languages_csharp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! highlight.js/lib/languages/csharp */ \"(ssr)/./node_modules/highlight.js/es/languages/csharp.js\");\n/* harmony import */ var highlight_js_lib_languages_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! highlight.js/lib/languages/css */ \"(ssr)/./node_modules/highlight.js/es/languages/css.js\");\n/* harmony import */ var highlight_js_lib_languages_diff__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! highlight.js/lib/languages/diff */ \"(ssr)/./node_modules/highlight.js/es/languages/diff.js\");\n/* harmony import */ var highlight_js_lib_languages_go__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! highlight.js/lib/languages/go */ \"(ssr)/./node_modules/highlight.js/es/languages/go.js\");\n/* harmony import */ var highlight_js_lib_languages_graphql__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! highlight.js/lib/languages/graphql */ \"(ssr)/./node_modules/highlight.js/es/languages/graphql.js\");\n/* harmony import */ var highlight_js_lib_languages_ini__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! highlight.js/lib/languages/ini */ \"(ssr)/./node_modules/highlight.js/es/languages/ini.js\");\n/* harmony import */ var highlight_js_lib_languages_java__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! highlight.js/lib/languages/java */ \"(ssr)/./node_modules/highlight.js/es/languages/java.js\");\n/* harmony import */ var highlight_js_lib_languages_javascript__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! highlight.js/lib/languages/javascript */ \"(ssr)/./node_modules/highlight.js/es/languages/javascript.js\");\n/* harmony import */ var highlight_js_lib_languages_json__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! highlight.js/lib/languages/json */ \"(ssr)/./node_modules/highlight.js/es/languages/json.js\");\n/* harmony import */ var highlight_js_lib_languages_kotlin__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! highlight.js/lib/languages/kotlin */ \"(ssr)/./node_modules/highlight.js/es/languages/kotlin.js\");\n/* harmony import */ var highlight_js_lib_languages_less__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! highlight.js/lib/languages/less */ \"(ssr)/./node_modules/highlight.js/es/languages/less.js\");\n/* harmony import */ var highlight_js_lib_languages_lua__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! highlight.js/lib/languages/lua */ \"(ssr)/./node_modules/highlight.js/es/languages/lua.js\");\n/* harmony import */ var highlight_js_lib_languages_makefile__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! highlight.js/lib/languages/makefile */ \"(ssr)/./node_modules/highlight.js/es/languages/makefile.js\");\n/* harmony import */ var highlight_js_lib_languages_markdown__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! highlight.js/lib/languages/markdown */ \"(ssr)/./node_modules/highlight.js/es/languages/markdown.js\");\n/* harmony import */ var highlight_js_lib_languages_objectivec__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! highlight.js/lib/languages/objectivec */ \"(ssr)/./node_modules/highlight.js/es/languages/objectivec.js\");\n/* harmony import */ var highlight_js_lib_languages_perl__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! highlight.js/lib/languages/perl */ \"(ssr)/./node_modules/highlight.js/es/languages/perl.js\");\n/* harmony import */ var highlight_js_lib_languages_php__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! highlight.js/lib/languages/php */ \"(ssr)/./node_modules/highlight.js/es/languages/php.js\");\n/* harmony import */ var highlight_js_lib_languages_php_template__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! highlight.js/lib/languages/php-template */ \"(ssr)/./node_modules/highlight.js/es/languages/php-template.js\");\n/* harmony import */ var highlight_js_lib_languages_plaintext__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! highlight.js/lib/languages/plaintext */ \"(ssr)/./node_modules/highlight.js/es/languages/plaintext.js\");\n/* harmony import */ var highlight_js_lib_languages_python__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! highlight.js/lib/languages/python */ \"(ssr)/./node_modules/highlight.js/es/languages/python.js\");\n/* harmony import */ var highlight_js_lib_languages_python_repl__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! highlight.js/lib/languages/python-repl */ \"(ssr)/./node_modules/highlight.js/es/languages/python-repl.js\");\n/* harmony import */ var highlight_js_lib_languages_r__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! highlight.js/lib/languages/r */ \"(ssr)/./node_modules/highlight.js/es/languages/r.js\");\n/* harmony import */ var highlight_js_lib_languages_ruby__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! highlight.js/lib/languages/ruby */ \"(ssr)/./node_modules/highlight.js/es/languages/ruby.js\");\n/* harmony import */ var highlight_js_lib_languages_rust__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! highlight.js/lib/languages/rust */ \"(ssr)/./node_modules/highlight.js/es/languages/rust.js\");\n/* harmony import */ var highlight_js_lib_languages_scss__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! highlight.js/lib/languages/scss */ \"(ssr)/./node_modules/highlight.js/es/languages/scss.js\");\n/* harmony import */ var highlight_js_lib_languages_shell__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! highlight.js/lib/languages/shell */ \"(ssr)/./node_modules/highlight.js/es/languages/shell.js\");\n/* harmony import */ var highlight_js_lib_languages_sql__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! highlight.js/lib/languages/sql */ \"(ssr)/./node_modules/highlight.js/es/languages/sql.js\");\n/* harmony import */ var highlight_js_lib_languages_swift__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! highlight.js/lib/languages/swift */ \"(ssr)/./node_modules/highlight.js/es/languages/swift.js\");\n/* harmony import */ var highlight_js_lib_languages_typescript__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! highlight.js/lib/languages/typescript */ \"(ssr)/./node_modules/highlight.js/es/languages/typescript.js\");\n/* harmony import */ var highlight_js_lib_languages_vbnet__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! highlight.js/lib/languages/vbnet */ \"(ssr)/./node_modules/highlight.js/es/languages/vbnet.js\");\n/* harmony import */ var highlight_js_lib_languages_wasm__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! highlight.js/lib/languages/wasm */ \"(ssr)/./node_modules/highlight.js/es/languages/wasm.js\");\n/* harmony import */ var highlight_js_lib_languages_xml__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! highlight.js/lib/languages/xml */ \"(ssr)/./node_modules/highlight.js/es/languages/xml.js\");\n/* harmony import */ var highlight_js_lib_languages_yaml__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! highlight.js/lib/languages/yaml */ \"(ssr)/./node_modules/highlight.js/es/languages/yaml.js\");\n/**\n * @import {LanguageFn} from 'highlight.js'\n */\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Map of grammars.\n *\n * @type {Record<string, LanguageFn>}\n */\nconst grammars = {\n  arduino: highlight_js_lib_languages_arduino__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n  bash: highlight_js_lib_languages_bash__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n  c: highlight_js_lib_languages_c__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n  cpp: highlight_js_lib_languages_cpp__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n  csharp: highlight_js_lib_languages_csharp__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n  css: highlight_js_lib_languages_css__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n  diff: highlight_js_lib_languages_diff__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n  go: highlight_js_lib_languages_go__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n  graphql: highlight_js_lib_languages_graphql__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n  ini: highlight_js_lib_languages_ini__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n  java: highlight_js_lib_languages_java__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n  javascript: highlight_js_lib_languages_javascript__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n  json: highlight_js_lib_languages_json__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n  kotlin: highlight_js_lib_languages_kotlin__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n  less: highlight_js_lib_languages_less__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n  lua: highlight_js_lib_languages_lua__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n  makefile: highlight_js_lib_languages_makefile__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n  markdown: highlight_js_lib_languages_markdown__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n  objectivec: highlight_js_lib_languages_objectivec__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n  perl: highlight_js_lib_languages_perl__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n  php: highlight_js_lib_languages_php__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n  'php-template': highlight_js_lib_languages_php_template__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n  plaintext: highlight_js_lib_languages_plaintext__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n  python: highlight_js_lib_languages_python__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n  'python-repl': highlight_js_lib_languages_python_repl__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n  r: highlight_js_lib_languages_r__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n  ruby: highlight_js_lib_languages_ruby__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n  rust: highlight_js_lib_languages_rust__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n  scss: highlight_js_lib_languages_scss__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n  shell: highlight_js_lib_languages_shell__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n  sql: highlight_js_lib_languages_sql__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n  swift: highlight_js_lib_languages_swift__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n  typescript: highlight_js_lib_languages_typescript__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n  vbnet: highlight_js_lib_languages_vbnet__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n  wasm: highlight_js_lib_languages_wasm__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n  xml: highlight_js_lib_languages_xml__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n  yaml: highlight_js_lib_languages_yaml__WEBPACK_IMPORTED_MODULE_36__[\"default\"]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lowlight/lib/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lowlight/lib/index.js":
/*!********************************************!*\
  !*** ./node_modules/lowlight/lib/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLowlight: () => (/* binding */ createLowlight)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! highlight.js/lib/core */ \"(ssr)/./node_modules/highlight.js/es/core.js\");\n/**\n * @import {ElementContent, Element, RootData, Root} from 'hast'\n * @import {Emitter, HLJSOptions as HljsOptions, HighlightResult, LanguageFn} from 'highlight.js'\n */\n\n/**\n * @typedef {Object} ExtraOptions\n *   Extra fields.\n * @property {ReadonlyArray<string> | null | undefined} [subset]\n *   List of allowed languages (default: all registered languages).\n *\n * @typedef Options\n *   Configuration for `highlight`.\n * @property {string | null | undefined} [prefix='hljs-']\n *   Class prefix (default: `'hljs-'`).\n *\n * @typedef {Options & ExtraOptions} AutoOptions\n *   Configuration for `highlightAuto`.\n */\n\n\n\n\n/** @type {AutoOptions} */\nconst emptyOptions = {}\n\nconst defaultPrefix = 'hljs-'\n\n/**\n * Create a `lowlight` instance.\n *\n * @param {Readonly<Record<string, LanguageFn>> | null | undefined} [grammars]\n *   Grammars to add (optional).\n * @returns\n *   Lowlight.\n */\nfunction createLowlight(grammars) {\n  const high = highlight_js_lib_core__WEBPACK_IMPORTED_MODULE_0__[\"default\"].newInstance()\n\n  if (grammars) {\n    register(grammars)\n  }\n\n  return {\n    highlight,\n    highlightAuto,\n    listLanguages,\n    register,\n    registerAlias,\n    registered\n  }\n\n  /**\n   * Highlight `value` (code) as `language` (name).\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlight('css', 'em { color: red }'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'css', relevance: 3}}\n   *   ```\n   *\n   * @param {string} language\n   *   Programming language name.\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<Options> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlight(language, value, options) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof language === 'string', 'expected `string` as `name`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const prefix =\n      typeof settings.prefix === 'string' ? settings.prefix : defaultPrefix\n\n    if (!high.getLanguage(language)) {\n      throw new Error('Unknown language: `' + language + '` is not registered')\n    }\n\n    // See: <https://github.com/highlightjs/highlight.js/issues/3621#issuecomment-1528841888>\n    high.configure({__emitter: HastEmitter, classPrefix: prefix})\n\n    const result = /** @type {HighlightResult & {_emitter: HastEmitter}} */ (\n      high.highlight(value, {ignoreIllegals: true, language})\n    )\n\n    // `highlight.js` seems to use this (currently) for broken grammars, so let’s\n    // keep it in there just to be sure.\n    /* c8 ignore next 5 */\n    if (result.errorRaised) {\n      throw new Error('Could not highlight with `Highlight.js`', {\n        cause: result.errorRaised\n      })\n    }\n\n    const root = result._emitter.root\n\n    // Cast because it is always defined.\n    const data = /** @type {RootData} */ (root.data)\n\n    data.language = result.language\n    data.relevance = result.relevance\n\n    return root\n  }\n\n  /**\n   * Highlight `value` (code) and guess its programming language.\n   *\n   * @example\n   *   ```js\n   *   import {common, createLowlight} from 'lowlight'\n   *\n   *   const lowlight = createLowlight(common)\n   *\n   *   console.log(lowlight.highlightAuto('\"hello, \" + name + \"!\"'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'arduino', relevance: 2}}\n   *   ```\n   *\n   * @param {string} value\n   *   Code to highlight.\n   * @param {Readonly<AutoOptions> | null | undefined} [options={}]\n   *   Configuration (optional).\n   * @returns {Root}\n   *   Tree; with the following `data` fields: `language` (`string`), detected\n   *   programming language name; `relevance` (`number`), how sure lowlight is\n   *   that the given code is in the language.\n   */\n  function highlightAuto(value, options) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(typeof value === 'string', 'expected `string` as `value`')\n    const settings = options || emptyOptions\n    const subset = settings.subset || listLanguages()\n\n    let index = -1\n    let relevance = 0\n    /** @type {Root | undefined} */\n    let result\n\n    while (++index < subset.length) {\n      const name = subset[index]\n\n      if (!high.getLanguage(name)) continue\n\n      const current = highlight(name, value, options)\n\n      if (\n        current.data &&\n        current.data.relevance !== undefined &&\n        current.data.relevance > relevance\n      ) {\n        relevance = current.data.relevance\n        result = current\n      }\n    }\n\n    return (\n      result || {\n        type: 'root',\n        children: [],\n        data: {language: undefined, relevance}\n      }\n    )\n  }\n\n  /**\n   * List registered languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   console.log(lowlight.listLanguages()) // => []\n   *\n   *   lowlight.register({markdown})\n   *\n   *   console.log(lowlight.listLanguages()) // => ['markdown']\n   *   ```\n   *\n   * @returns {Array<string>}\n   *   Names of registered language.\n   */\n  function listLanguages() {\n    return high.listLanguages()\n  }\n\n  /**\n   * Register languages.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import xml from 'highlight.js/lib/languages/xml'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({xml})\n   *\n   *   // Note: `html` is an alias for `xml`.\n   *   console.log(lowlight.highlight('html', '<em>Emphasis</em>'))\n   *   ```\n   *\n   *   Yields:\n   *\n   *   ```js\n   *   {type: 'root', children: [Array], data: {language: 'html', relevance: 2}}\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, LanguageFn>>} grammars\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} name\n   * @param {LanguageFn} grammar\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, LanguageFn>> | string} grammarsOrName\n   *   Grammars or programming language name.\n   * @param {LanguageFn | undefined} [grammar]\n   *   Grammar, if with name.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function register(grammarsOrName, grammar) {\n    if (typeof grammarsOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(grammar !== undefined, 'expected `grammar`')\n      high.registerLanguage(grammarsOrName, grammar)\n    } else {\n      /** @type {string} */\n      let name\n\n      for (name in grammarsOrName) {\n        if (Object.hasOwn(grammarsOrName, name)) {\n          high.registerLanguage(name, grammarsOrName[name])\n        }\n      }\n    }\n  }\n\n  /**\n   * Register aliases.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import markdown from 'highlight.js/lib/languages/markdown'\n   *\n   *   const lowlight = createLowlight()\n   *\n   *   lowlight.register({markdown})\n   *\n   *   // lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ would throw: Error: Unknown language: `mdown` is not registered\n   *\n   *   lowlight.registerAlias({markdown: ['mdown', 'mkdn', 'mdwn', 'ron']})\n   *   lowlight.highlight('mdown', '<em>Emphasis</em>')\n   *   // ^ Works!\n   *   ```\n   *\n   * @overload\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>>} aliases\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {string} language\n   * @param {ReadonlyArray<string> | string} alias\n   * @returns {undefined}\n   *\n   * @param {Readonly<Record<string, ReadonlyArray<string> | string>> | string} aliasesOrName\n   *   Map of programming language names to one or more aliases, or programming\n   *   language name.\n   * @param {ReadonlyArray<string> | string | undefined} [alias]\n   *   One or more aliases for the programming language, if with `name`.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function registerAlias(aliasesOrName, alias) {\n    if (typeof aliasesOrName === 'string') {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(alias !== undefined)\n      high.registerAliases(\n        // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n        typeof alias === 'string' ? alias : [...alias],\n        {languageName: aliasesOrName}\n      )\n    } else {\n      /** @type {string} */\n      let key\n\n      for (key in aliasesOrName) {\n        if (Object.hasOwn(aliasesOrName, key)) {\n          const aliases = aliasesOrName[key]\n          high.registerAliases(\n            // Note: copy needed because hljs doesn’t accept readonly arrays yet.\n            typeof aliases === 'string' ? aliases : [...aliases],\n            {languageName: key}\n          )\n        }\n      }\n    }\n  }\n\n  /**\n   * Check whether an alias or name is registered.\n   *\n   * @example\n   *   ```js\n   *   import {createLowlight} from 'lowlight'\n   *   import javascript from 'highlight.js/lib/languages/javascript'\n   *\n   *   const lowlight = createLowlight({javascript})\n   *\n   *   console.log(lowlight.registered('funkyscript')) // => `false`\n   *\n   *   lowlight.registerAlias({javascript: 'funkyscript'})\n   *   console.log(lowlight.registered('funkyscript')) // => `true`\n   *   ```\n   *\n   * @param {string} aliasOrName\n   *   Name of a language or alias for one.\n   * @returns {boolean}\n   *   Whether `aliasOrName` is registered.\n   */\n  function registered(aliasOrName) {\n    return Boolean(high.getLanguage(aliasOrName))\n  }\n}\n\n/** @type {Emitter} */\nclass HastEmitter {\n  /**\n   * @param {Readonly<HljsOptions>} options\n   *   Configuration.\n   * @returns\n   *   Instance.\n   */\n  constructor(options) {\n    /** @type {HljsOptions} */\n    this.options = options\n    /** @type {Root} */\n    this.root = {\n      type: 'root',\n      children: [],\n      data: {language: undefined, relevance: 0}\n    }\n    /** @type {[Root, ...Array<Element>]} */\n    this.stack = [this.root]\n  }\n\n  /**\n   * @param {string} value\n   *   Text to add.\n   * @returns {undefined}\n   *   Nothing.\n   *\n   */\n  addText(value) {\n    if (value === '') return\n\n    const current = this.stack[this.stack.length - 1]\n    const tail = current.children[current.children.length - 1]\n\n    if (tail && tail.type === 'text') {\n      tail.value += value\n    } else {\n      current.children.push({type: 'text', value})\n    }\n  }\n\n  /**\n   *\n   * @param {unknown} rawName\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  startScope(rawName) {\n    this.openNode(String(rawName))\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  endScope() {\n    this.closeNode()\n  }\n\n  /**\n   * @param {HastEmitter} other\n   *   Other emitter.\n   * @param {string} name\n   *   Name of the sublanguage.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  __addSublanguage(other, name) {\n    const current = this.stack[this.stack.length - 1]\n    // Assume only element content.\n    const results = /** @type {Array<ElementContent>} */ (other.root.children)\n\n    if (name) {\n      current.children.push({\n        type: 'element',\n        tagName: 'span',\n        properties: {className: [name]},\n        children: results\n      })\n    } else {\n      current.children.push(...results)\n    }\n  }\n\n  /**\n   * @param {string} name\n   *   Name to add.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  openNode(name) {\n    const self = this\n    // First “class” gets the prefix. Rest gets a repeated underscore suffix.\n    // See: <https://github.com/highlightjs/highlight.js/commit/51806aa>\n    // See: <https://github.com/wooorm/lowlight/issues/43>\n    const className = name.split('.').map(function (d, i) {\n      return i ? d + '_'.repeat(i) : self.options.classPrefix + d\n    })\n    const current = this.stack[this.stack.length - 1]\n    /** @type {Element} */\n    const child = {\n      type: 'element',\n      tagName: 'span',\n      properties: {className},\n      children: []\n    }\n\n    current.children.push(child)\n    this.stack.push(child)\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  closeNode() {\n    this.stack.pop()\n  }\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  finalize() {}\n\n  /**\n   * @returns {string}\n   *   Nothing.\n   */\n  toHTML() {\n    return ''\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lowlight/lib/index.js\n");

/***/ })

};
;