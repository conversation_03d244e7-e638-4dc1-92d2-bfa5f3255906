// Exemplo de uso do DownloadModal
// Este arquivo demonstra como integrar o modal de download em seus componentes

import { useState } from 'react';
import DownloadModal from './DownloadModal';
import { ChatMessage } from '@/lib/types/chat';

// Exemplo de dados de mensagens
const exampleMessages: ChatMessage[] = [
  {
    id: '1',
    content: 'Olá! Como você pode me ajudar hoje?',
    role: 'user',
    timestamp: '2024-01-15T10:00:00Z',
    isFavorite: false
  },
  {
    id: '2',
    content: 'Olá! Eu sou uma IA assistente e posso ajudá-lo com diversas tarefas como:\n\n- Responder perguntas\n- Explicar conceitos\n- Ajudar com programação\n- Resolver problemas matemáticos\n- E muito mais!\n\nEm que posso ajudá-lo especificamente?',
    role: 'assistant',
    timestamp: '2024-01-15T10:00:30Z',
    isFavorite: true
  },
  {
    id: '3',
    content: 'Preciso de ajuda para criar uma função em JavaScript que calcule a média de um array de números.',
    role: 'user',
    timestamp: '2024-01-15T10:01:00Z',
    isFavorite: false
  },
  {
    id: '4',
    content: 'Claro! Aqui está uma função simples para calcular a média de um array:\n\n```javascript\nfunction calcularMedia(numeros) {\n  if (numeros.length === 0) {\n    return 0; // ou throw new Error("Array vazio")\n  }\n  \n  const soma = numeros.reduce((acc, num) => acc + num, 0);\n  return soma / numeros.length;\n}\n\n// Exemplo de uso:\nconst notas = [8.5, 7.2, 9.1, 6.8, 8.9];\nconst media = calcularMedia(notas);\nconsole.log(`Média: ${media.toFixed(2)}`); // Média: 8.10\n```\n\nEsta função:\n1. Verifica se o array não está vazio\n2. Usa `reduce()` para somar todos os números\n3. Divide a soma pelo número de elementos\n4. Retorna a média\n\nVocê gostaria de alguma variação desta função?',
    role: 'assistant',
    timestamp: '2024-01-15T10:01:45Z',
    isFavorite: false
  }
];

// Componente de exemplo
export default function DownloadModalExample() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="p-8 bg-gradient-to-br from-blue-900 to-blue-800 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">
          Exemplo do DownloadModal
        </h1>
        
        <div className="bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-blue-100 mb-4">
            Como usar o DownloadModal
          </h2>
          
          <div className="space-y-4 text-blue-200">
            <p>
              <strong>1. Importe o componente:</strong>
            </p>
            <pre className="bg-blue-900/50 p-3 rounded-lg text-sm overflow-x-auto">
              <code>{`import DownloadModal from './DownloadModal';
import { ChatMessage } from '@/lib/types/chat';`}</code>
            </pre>
            
            <p>
              <strong>2. Configure o estado:</strong>
            </p>
            <pre className="bg-blue-900/50 p-3 rounded-lg text-sm overflow-x-auto">
              <code>{`const [isModalOpen, setIsModalOpen] = useState(false);
const [messages, setMessages] = useState<ChatMessage[]>([]);`}</code>
            </pre>
            
            <p>
              <strong>3. Use o componente:</strong>
            </p>
            <pre className="bg-blue-900/50 p-3 rounded-lg text-sm overflow-x-auto">
              <code>{`<DownloadModal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  messages={messages}
  chatName="Minha Conversa"
/>`}</code>
            </pre>
          </div>
        </div>

        <div className="bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-blue-100 mb-4">
            Demonstração
          </h2>
          
          <p className="text-blue-200 mb-4">
            Clique no botão abaixo para abrir o modal de download com mensagens de exemplo:
          </p>
          
          <button
            onClick={() => setIsModalOpen(true)}
            className="px-6 py-3 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-semibold shadow-lg hover:shadow-blue-500/30 hover:scale-105 flex items-center"
          >
            <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Abrir Modal de Download
          </button>
        </div>

        <div className="bg-blue-800/30 backdrop-blur-sm border border-blue-600/30 rounded-xl p-6">
          <h2 className="text-xl font-semibold text-blue-100 mb-4">
            Características do Modal
          </h2>
          
          <div className="grid md:grid-cols-2 gap-6 text-blue-200">
            <div>
              <h3 className="font-semibold text-blue-100 mb-2">🎨 Design</h3>
              <ul className="space-y-1 text-sm">
                <li>• Backdrop blur com transparência</li>
                <li>• Gradiente azul consistente</li>
                <li>• Animações suaves</li>
                <li>• Ícones SVG personalizados</li>
                <li>• Design responsivo</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-blue-100 mb-2">⚡ Funcionalidades</h3>
              <ul className="space-y-1 text-sm">
                <li>• 3 tipos de exportação</li>
                <li>• Estado de loading</li>
                <li>• Portal rendering</li>
                <li>• Formatação HTML completa</li>
                <li>• Suporte a LaTeX e código</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Download */}
      <DownloadModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        messages={exampleMessages}
        chatName="Conversa de Exemplo"
      />
    </div>
  );
}
