# Exemplo de Markdown Estilizado

Este é um exemplo demonstrando os novos estilos profissionais do markdown no tema azul do Rafthor.

## Elementos Básicos

### Parágrafos e Texto

Este é um parágrafo normal com texto **em negrito**, *em itálico*, ~~riscado~~ e `código inline`. O texto é justificado e possui espaçamento otimizado para melhor legibilidade.

### Listas

#### Lista com Marcadores
- Primeiro item da lista
- Segundo item com **texto em negrito**
- Terceiro item com *texto em itálico*
  - Sub-item aninhado
  - Outro sub-item
- Quarto item final

#### Lista Numerada
1. Primeiro item numerado
2. Segundo item com `código inline`
3. Terceiro item
   1. Sub-item numerado
   2. Outro sub-item numerado
4. Quarto item final

### Links e Referências

Visite o [site oficial do Rafthor](https://rafthor.com) para mais informações. Os links possuem efeitos de hover elegantes e animações suaves.

### Citações

> "A programação é uma arte que combina lógica, criatividade e persistência. Cada linha de código é uma pincelada na tela digital da inovação."
> 
> — Desenvolvedor Anônimo

### Código

#### Código Inline
Use a função `console.log()` para debug ou `useState()` para gerenciar estado no React.

#### Blocos de Código

```javascript
// Exemplo de código JavaScript com syntax highlighting
function calculateFibonacci(n) {
  if (n <= 1) return n;
  
  let a = 0, b = 1, temp;
  
  for (let i = 2; i <= n; i++) {
    temp = a + b;
    a = b;
    b = temp;
  }
  
  return b;
}

// Uso da função
const result = calculateFibonacci(10);
console.log(`Fibonacci(10) = ${result}`);
```

```python
# Exemplo em Python
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

# Exemplo de uso
numbers = [64, 34, 25, 12, 22, 11, 90]
sorted_numbers = quick_sort(numbers)
print(f"Array ordenado: {sorted_numbers}")
```

### Tabelas

| Linguagem | Paradigma | Dificuldade | Popularidade |
|-----------|-----------|-------------|--------------|
| JavaScript | Multi-paradigma | Média | ⭐⭐⭐⭐⭐ |
| Python | Multi-paradigma | Baixa | ⭐⭐⭐⭐⭐ |
| Rust | Sistemas | Alta | ⭐⭐⭐ |
| Go | Procedural | Média | ⭐⭐⭐⭐ |
| TypeScript | Orientada a Objetos | Média | ⭐⭐⭐⭐ |

### Linha Horizontal

---

### Matemática (LaTeX)

#### Fórmulas Inline
A fórmula da área de um círculo é $A = \pi r^2$, onde $r$ é o raio.

#### Fórmulas em Bloco
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

$$
\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}
$$

### Elementos Especiais

#### Texto Destacado
Este texto possui ==destaque amarelo== para chamar atenção.

#### Elementos Formatados
- **Negrito**: Texto em negrito
- *Itálico*: Texto em itálico  
- ***Negrito e Itálico***: Combinação
- ~~Riscado~~: Texto riscado
- `Código`: Código inline

### Imagens
![Exemplo de Imagem](https://via.placeholder.com/600x300/3b82f6/ffffff?text=Rafthor+Markdown)

---

## Recursos Avançados

### Callouts (Alertas)

<div class="callout info">
💡 **Informação**: Este é um callout informativo com estilo elegante e fundo semi-transparente.
</div>

<div class="callout success">
✅ **Sucesso**: Operação realizada com sucesso! Os estilos foram aplicados corretamente.
</div>

<div class="callout warning">
⚠️ **Atenção**: Este é um aviso importante que requer sua atenção.
</div>

<div class="callout error">
❌ **Erro**: Algo deu errado. Verifique os logs para mais detalhes.
</div>

### Badges

Tecnologias: <span class="badge">React</span> <span class="badge success">Node.js</span> <span class="badge warning">TypeScript</span> <span class="badge error">MongoDB</span>

### Barra de Progresso

<div class="progress">
  <div class="progress-bar" style="width: 75%"></div>
</div>

Progresso do projeto: 75% concluído

---

## Conclusão

Este exemplo demonstra todos os elementos estilizados do markdown com o tema azul profissional do Rafthor. Os estilos incluem:

- ✨ Gradientes elegantes nos headings
- 🎨 Cores harmoniosas seguindo o tema azul
- 📱 Design responsivo para todos os dispositivos
- 🔄 Animações suaves e transições
- 💎 Efeitos visuais profissionais
- 🎯 Foco na legibilidade e experiência do usuário

Todos os elementos foram cuidadosamente projetados para manter a consistência visual com o resto da aplicação Rafthor.
