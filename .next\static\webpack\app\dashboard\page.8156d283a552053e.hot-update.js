"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Tentar deletar arquivo do Storage\n                try {\n                    const storageRef = ref(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(storageRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\", \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-lg\",\n                                            children: userData.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Carregando...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-400 text-sm\",\n                                                title: openRouterBalance.error,\n                                                children: \"$0.00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: [\n                                                    \"$\",\n                                                    openRouterBalance.balance.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onSettingsOpen,\n                                        className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 601,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xc1rea Inicial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 659,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 658,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewFolder,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium\",\n                                            title: \"Nova Pasta\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 639,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Conversas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            onDragOver: (e)=>handleDragOver(e, folder.id),\n                                            onDragLeave: handleDragLeave,\n                                            onDrop: (e)=>handleDrop(e, folder.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                    onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                    onMouseLeave: ()=>setHoveredFolder(null),\n                                                    onClick: ()=>toggleFolder(folder.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 5l7 7-7 7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 721,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 712,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            style: {\n                                                                                color: getFolderHexColor(folder.color)\n                                                                            },\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                        children: folder.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 743,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                        children: folder.chats.length\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 746,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 742,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                children: folder.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 751,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Editar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleEditFolder(folder.id);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 771,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 770,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Deletar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleDeleteFolder(folder.id, folder.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 783,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 782,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-6 mt-2 space-y-1\",\n                                                    children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: ()=>handleDragStart(chat.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(currentChat === chat.id ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(draggedChat === chat.id ? \"opacity-50 scale-95\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-full text-left p-3 flex items-start space-x-3\",\n                                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(currentChat === chat.id ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                                                                            children: [\n                                                                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-2 h-2 text-white\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 3,\n                                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 818,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 817,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 816,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    strokeWidth: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 824,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 823,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 809,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"truncate text-sm font-semibold mb-1 \".concat(currentChat === chat.id ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                                                                    children: chat.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 829,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"truncate text-xs leading-relaxed transition-all duration-300 \".concat(currentChat === chat.id ? \"text-blue-200/80\" : \"text-blue-300/70 group-hover:text-blue-200\"),\n                                                                                    children: chat.lastMessage || \"Nenhuma mensagem ainda...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 836,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs mt-1 block \".concat(currentChat === chat.id ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                                                                    children: formatTime(chat.lastMessageTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 844,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Editar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"edit\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 865,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 857,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Deletar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"delete\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 878,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 877,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 869,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, chat.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, folder.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    onDragOver: (e)=>handleDragOver(e, null),\n                                    onDragLeave: handleDragLeave,\n                                    onDrop: (e)=>handleDrop(e, null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 901,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Sem Pasta \",\n                                                            dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 902,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 898,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                            children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/30\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 910,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: \"Nenhuma conversa sem pasta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 916,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 19\n                                            }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                    chat: chat,\n                                                    isActive: currentChat === chat.id,\n                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                    onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                    onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                    onDragStart: handleDragStart,\n                                                    onDragEnd: handleDragEnd,\n                                                    isDragging: draggedChat === chat.id\n                                                }, chat.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 920,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 13\n                                }, undefined),\n                                folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/30 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 941,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 940,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 939,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/50 text-sm\",\n                                            children: \"Nenhuma conversa ainda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/30 text-xs mt-1\",\n                                            children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 938,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden p-4 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 958,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Fechar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 961,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 953,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 599,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 594,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 968,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 977,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 990,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 998,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1010,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1025,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\")), \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging } = param;\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const handleEdit = (e)=>{\n        e.stopPropagation();\n        onEdit(chat.id, chat.name);\n    };\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        onDelete(chat.id, chat.name);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"relative w-full rounded-lg transition-all duration-200 group cursor-move \".concat(isActive ? \"bg-blue-600/50\" : \"hover:bg-white/5\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left px-3 py-3 text-white/80 hover:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                            children: [\n                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-2 h-2 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 3,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1093,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200\",\n                                            children: formatTime(chat.lastMessageTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 line-clamp-2 leading-relaxed\",\n                                    children: chat.lastMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 1092,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1088,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Configurar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1132,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Deletar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1078,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});