"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Deletar arquivo chat.json do Storage\n                try {\n                    const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(chatJsonRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo chat.json no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Deletar todos os anexos do chat\n                await deleteChatAttachments(deleteConfirmModal.id);\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\", \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-lg\",\n                                            children: userData.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Carregando...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 19\n                                            }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-400 text-sm\",\n                                                title: openRouterBalance.error,\n                                                children: \"$0.00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: [\n                                                    \"$\",\n                                                    openRouterBalance.balance.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onSettingsOpen,\n                                        className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 651,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 621,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xc1rea Inicial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 678,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewFolder,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium\",\n                                            title: \"Nova Pasta\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Conversas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            onDragOver: (e)=>handleDragOver(e, folder.id),\n                                            onDragLeave: handleDragLeave,\n                                            onDrop: (e)=>handleDrop(e, folder.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                    onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                    onMouseLeave: ()=>setHoveredFolder(null),\n                                                    onClick: ()=>toggleFolder(folder.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 5l7 7-7 7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 740,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            style: {\n                                                                                color: getFolderHexColor(folder.color)\n                                                                            },\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 755,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                        children: folder.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 762,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                        children: folder.chats.length\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 765,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 761,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                children: folder.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 770,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 760,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Editar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleEditFolder(folder.id);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 790,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Deletar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleDeleteFolder(folder.id, folder.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 802,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 801,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 793,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-6 mt-2 space-y-1\",\n                                                    children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: ()=>handleDragStart(chat.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(currentChat === chat.id ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(draggedChat === chat.id ? \"opacity-50 scale-95\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-full text-left p-3 flex items-start space-x-3\",\n                                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(currentChat === chat.id ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                                                                            children: [\n                                                                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-2 h-2 text-white\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 3,\n                                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 837,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 836,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 835,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    strokeWidth: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 843,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 842,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"truncate text-sm font-semibold mb-1 \".concat(currentChat === chat.id ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                                                                    children: chat.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 848,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"truncate text-xs leading-relaxed transition-all duration-300 \".concat(currentChat === chat.id ? \"text-blue-200/80\" : \"text-blue-300/70 group-hover:text-blue-200\"),\n                                                                                    children: chat.lastMessage || \"Nenhuma mensagem ainda...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 855,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs mt-1 block \".concat(currentChat === chat.id ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                                                                    children: formatTime(chat.lastMessageTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 847,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Editar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"edit\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 885,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 884,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 876,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Deletar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"delete\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 897,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 896,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 888,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 875,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, chat.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, folder.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    onDragOver: (e)=>handleDragOver(e, null),\n                                    onDragLeave: handleDragLeave,\n                                    onDrop: (e)=>handleDrop(e, null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Sem Pasta \",\n                                                            dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                            children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/30\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: \"Nenhuma conversa sem pasta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 19\n                                            }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                    chat: chat,\n                                                    isActive: currentChat === chat.id,\n                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                    onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                    onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                    onDragStart: handleDragStart,\n                                                    onDragEnd: handleDragEnd,\n                                                    isDragging: draggedChat === chat.id\n                                                }, chat.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 939,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, undefined),\n                                folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/30 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 959,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/50 text-sm\",\n                                            children: \"Nenhuma conversa ainda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/30 text-xs mt-1\",\n                                            children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden p-4 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 977,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Fechar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 980,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 972,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 987,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 996,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1009,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1017,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1029,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1044,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\")), \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging } = param;\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const handleEdit = (e)=>{\n        e.stopPropagation();\n        onEdit(chat.id, chat.name);\n    };\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        onDelete(chat.id, chat.name);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"relative w-full rounded-lg transition-all duration-200 group cursor-move \".concat(isActive ? \"bg-blue-600/50\" : \"hover:bg-white/5\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left px-3 py-3 text-white/80 hover:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                            children: [\n                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-2 h-2 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 3,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200\",\n                                            children: formatTime(chat.lastMessageTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 line-clamp-2 leading-relaxed\",\n                                    children: chat.lastMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 1111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Configurar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Deletar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1163,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1162,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1157,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1097,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});