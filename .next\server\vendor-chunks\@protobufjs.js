"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@protobufjs";
exports.ids = ["vendor-chunks/@protobufjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/@protobufjs/aspromise/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@protobufjs/aspromise/index.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/aspromise/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/base64/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@protobufjs/base64/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/base64/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/codegen/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@protobufjs/codegen/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/codegen/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/eventemitter/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@protobufjs/eventemitter/index.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvZXZlbnRlbWl0dGVyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsVUFBVTtBQUNyQixXQUFXLEdBQUc7QUFDZCxhQUFhLG1CQUFtQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxVQUFVO0FBQ3JCLGFBQWEsbUJBQW1CO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixxQkFBcUI7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLE1BQU07QUFDakIsYUFBYSxtQkFBbUI7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxxQkFBcUI7QUFDcEM7QUFDQSxvQkFBb0IscUJBQXFCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL25vZGVfbW9kdWxlcy9AcHJvdG9idWZqcy9ldmVudGVtaXR0ZXIvaW5kZXguanM/MTVmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxubW9kdWxlLmV4cG9ydHMgPSBFdmVudEVtaXR0ZXI7XHJcblxyXG4vKipcclxuICogQ29uc3RydWN0cyBhIG5ldyBldmVudCBlbWl0dGVyIGluc3RhbmNlLlxyXG4gKiBAY2xhc3NkZXNjIEEgbWluaW1hbCBldmVudCBlbWl0dGVyLlxyXG4gKiBAbWVtYmVyb2YgdXRpbFxyXG4gKiBAY29uc3RydWN0b3JcclxuICovXHJcbmZ1bmN0aW9uIEV2ZW50RW1pdHRlcigpIHtcclxuXHJcbiAgICAvKipcclxuICAgICAqIFJlZ2lzdGVyZWQgbGlzdGVuZXJzLlxyXG4gICAgICogQHR5cGUge09iamVjdC48c3RyaW5nLCo+fVxyXG4gICAgICogQHByaXZhdGVcclxuICAgICAqL1xyXG4gICAgdGhpcy5fbGlzdGVuZXJzID0ge307XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBSZWdpc3RlcnMgYW4gZXZlbnQgbGlzdGVuZXIuXHJcbiAqIEBwYXJhbSB7c3RyaW5nfSBldnQgRXZlbnQgbmFtZVxyXG4gKiBAcGFyYW0ge2Z1bmN0aW9ufSBmbiBMaXN0ZW5lclxyXG4gKiBAcGFyYW0geyp9IFtjdHhdIExpc3RlbmVyIGNvbnRleHRcclxuICogQHJldHVybnMge3V0aWwuRXZlbnRFbWl0dGVyfSBgdGhpc2BcclxuICovXHJcbkV2ZW50RW1pdHRlci5wcm90b3R5cGUub24gPSBmdW5jdGlvbiBvbihldnQsIGZuLCBjdHgpIHtcclxuICAgICh0aGlzLl9saXN0ZW5lcnNbZXZ0XSB8fCAodGhpcy5fbGlzdGVuZXJzW2V2dF0gPSBbXSkpLnB1c2goe1xyXG4gICAgICAgIGZuICA6IGZuLFxyXG4gICAgICAgIGN0eCA6IGN0eCB8fCB0aGlzXHJcbiAgICB9KTtcclxuICAgIHJldHVybiB0aGlzO1xyXG59O1xyXG5cclxuLyoqXHJcbiAqIFJlbW92ZXMgYW4gZXZlbnQgbGlzdGVuZXIgb3IgYW55IG1hdGNoaW5nIGxpc3RlbmVycyBpZiBhcmd1bWVudHMgYXJlIG9taXR0ZWQuXHJcbiAqIEBwYXJhbSB7c3RyaW5nfSBbZXZ0XSBFdmVudCBuYW1lLiBSZW1vdmVzIGFsbCBsaXN0ZW5lcnMgaWYgb21pdHRlZC5cclxuICogQHBhcmFtIHtmdW5jdGlvbn0gW2ZuXSBMaXN0ZW5lciB0byByZW1vdmUuIFJlbW92ZXMgYWxsIGxpc3RlbmVycyBvZiBgZXZ0YCBpZiBvbWl0dGVkLlxyXG4gKiBAcmV0dXJucyB7dXRpbC5FdmVudEVtaXR0ZXJ9IGB0aGlzYFxyXG4gKi9cclxuRXZlbnRFbWl0dGVyLnByb3RvdHlwZS5vZmYgPSBmdW5jdGlvbiBvZmYoZXZ0LCBmbikge1xyXG4gICAgaWYgKGV2dCA9PT0gdW5kZWZpbmVkKVxyXG4gICAgICAgIHRoaXMuX2xpc3RlbmVycyA9IHt9O1xyXG4gICAgZWxzZSB7XHJcbiAgICAgICAgaWYgKGZuID09PSB1bmRlZmluZWQpXHJcbiAgICAgICAgICAgIHRoaXMuX2xpc3RlbmVyc1tldnRdID0gW107XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIHZhciBsaXN0ZW5lcnMgPSB0aGlzLl9saXN0ZW5lcnNbZXZ0XTtcclxuICAgICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaXN0ZW5lcnMubGVuZ3RoOylcclxuICAgICAgICAgICAgICAgIGlmIChsaXN0ZW5lcnNbaV0uZm4gPT09IGZuKVxyXG4gICAgICAgICAgICAgICAgICAgIGxpc3RlbmVycy5zcGxpY2UoaSwgMSk7XHJcbiAgICAgICAgICAgICAgICBlbHNlXHJcbiAgICAgICAgICAgICAgICAgICAgKytpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiB0aGlzO1xyXG59O1xyXG5cclxuLyoqXHJcbiAqIEVtaXRzIGFuIGV2ZW50IGJ5IGNhbGxpbmcgaXRzIGxpc3RlbmVycyB3aXRoIHRoZSBzcGVjaWZpZWQgYXJndW1lbnRzLlxyXG4gKiBAcGFyYW0ge3N0cmluZ30gZXZ0IEV2ZW50IG5hbWVcclxuICogQHBhcmFtIHsuLi4qfSBhcmdzIEFyZ3VtZW50c1xyXG4gKiBAcmV0dXJucyB7dXRpbC5FdmVudEVtaXR0ZXJ9IGB0aGlzYFxyXG4gKi9cclxuRXZlbnRFbWl0dGVyLnByb3RvdHlwZS5lbWl0ID0gZnVuY3Rpb24gZW1pdChldnQpIHtcclxuICAgIHZhciBsaXN0ZW5lcnMgPSB0aGlzLl9saXN0ZW5lcnNbZXZ0XTtcclxuICAgIGlmIChsaXN0ZW5lcnMpIHtcclxuICAgICAgICB2YXIgYXJncyA9IFtdLFxyXG4gICAgICAgICAgICBpID0gMTtcclxuICAgICAgICBmb3IgKDsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7KVxyXG4gICAgICAgICAgICBhcmdzLnB1c2goYXJndW1lbnRzW2krK10pO1xyXG4gICAgICAgIGZvciAoaSA9IDA7IGkgPCBsaXN0ZW5lcnMubGVuZ3RoOylcclxuICAgICAgICAgICAgbGlzdGVuZXJzW2ldLmZuLmFwcGx5KGxpc3RlbmVyc1tpKytdLmN0eCwgYXJncyk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gdGhpcztcclxufTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/eventemitter/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/fetch/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@protobufjs/fetch/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = __webpack_require__(/*! @protobufjs/aspromise */ \"(ssr)/./node_modules/@protobufjs/aspromise/index.js\"),\r\n    inquire   = __webpack_require__(/*! @protobufjs/inquire */ \"(ssr)/./node_modules/@protobufjs/inquire/index.js\");\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/fetch/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/float/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@protobufjs/float/index.js ***!
  \*************************************************/
/***/ ((module) => {

eval("\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/float/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/inquire/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@protobufjs/inquire/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvaW5xdWlyZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLCtEQUErRDtBQUMvRDtBQUNBO0FBQ0EsTUFBTSxhQUFhO0FBQ25CO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yYWZ0aG9yLy4vbm9kZV9tb2R1bGVzL0Bwcm90b2J1ZmpzL2lucXVpcmUvaW5kZXguanM/NGQzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxubW9kdWxlLmV4cG9ydHMgPSBpbnF1aXJlO1xyXG5cclxuLyoqXHJcbiAqIFJlcXVpcmVzIGEgbW9kdWxlIG9ubHkgaWYgYXZhaWxhYmxlLlxyXG4gKiBAbWVtYmVyb2YgdXRpbFxyXG4gKiBAcGFyYW0ge3N0cmluZ30gbW9kdWxlTmFtZSBNb2R1bGUgdG8gcmVxdWlyZVxyXG4gKiBAcmV0dXJucyB7P09iamVjdH0gUmVxdWlyZWQgbW9kdWxlIGlmIGF2YWlsYWJsZSBhbmQgbm90IGVtcHR5LCBvdGhlcndpc2UgYG51bGxgXHJcbiAqL1xyXG5mdW5jdGlvbiBpbnF1aXJlKG1vZHVsZU5hbWUpIHtcclxuICAgIHRyeSB7XHJcbiAgICAgICAgdmFyIG1vZCA9IGV2YWwoXCJxdWlyZVwiLnJlcGxhY2UoL14vLFwicmVcIikpKG1vZHVsZU5hbWUpOyAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIG5vLWV2YWxcclxuICAgICAgICBpZiAobW9kICYmIChtb2QubGVuZ3RoIHx8IE9iamVjdC5rZXlzKG1vZCkubGVuZ3RoKSlcclxuICAgICAgICAgICAgcmV0dXJuIG1vZDtcclxuICAgIH0gY2F0Y2ggKGUpIHt9IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tZW1wdHlcclxuICAgIHJldHVybiBudWxsO1xyXG59XHJcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/inquire/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/path/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/path/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/path/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/pool/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/pool/index.js ***!
  \************************************************/
/***/ ((module) => {

eval("\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/pool/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@protobufjs/utf8/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/utf8/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@protobufjs/utf8/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/aspromise/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@protobufjs/aspromise/index.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\nmodule.exports = asPromise;\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */ /**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */ function asPromise(fn, ctx /*, varargs */ ) {\n    var params = new Array(arguments.length - 1), offset = 0, index = 2, pending = true;\n    while(index < arguments.length)params[offset++] = arguments[index++];\n    return new Promise(function executor(resolve, reject) {\n        params[offset] = function callback(err /*, varargs */ ) {\n            if (pending) {\n                pending = false;\n                if (err) reject(err);\n                else {\n                    var params = new Array(arguments.length - 1), offset = 0;\n                    while(offset < params.length)params[offset++] = arguments[offset];\n                    resolve.apply(null, params);\n                }\n            }\n        };\n        try {\n            fn.apply(ctx || null, params);\n        } catch (err) {\n            if (pending) {\n                pending = false;\n                reject(err);\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/aspromise/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/base64/index.js":
/*!**************************************************!*\
  !*** ./node_modules/@protobufjs/base64/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */ var base64 = exports;\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */ base64.length = function length(string) {\n    var p = string.length;\n    if (!p) return 0;\n    var n = 0;\n    while(--p % 4 > 1 && string.charAt(p) === \"=\")++n;\n    return Math.ceil(string.length * 3) / 4 - n;\n};\n// Base64 encoding table\nvar b64 = new Array(64);\n// Base64 decoding table\nvar s64 = new Array(123);\n// 65..90, 97..122, 48..57, 43, 47\nfor(var i = 0; i < 64;)s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */ base64.encode = function encode(buffer, start, end) {\n    var parts = null, chunk = [];\n    var i = 0, j = 0, t; // temporary\n    while(start < end){\n        var b = buffer[start++];\n        switch(j){\n            case 0:\n                chunk[i++] = b64[b >> 2];\n                t = (b & 3) << 4;\n                j = 1;\n                break;\n            case 1:\n                chunk[i++] = b64[t | b >> 4];\n                t = (b & 15) << 2;\n                j = 2;\n                break;\n            case 2:\n                chunk[i++] = b64[t | b >> 6];\n                chunk[i++] = b64[b & 63];\n                j = 0;\n                break;\n        }\n        if (i > 8191) {\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\n            i = 0;\n        }\n    }\n    if (j) {\n        chunk[i++] = b64[t];\n        chunk[i++] = 61;\n        if (j === 1) chunk[i++] = 61;\n    }\n    if (parts) {\n        if (i) parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\n        return parts.join(\"\");\n    }\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\n};\nvar invalidEncoding = \"invalid encoding\";\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */ base64.decode = function decode(string, buffer, offset) {\n    var start = offset;\n    var j = 0, t; // temporary\n    for(var i = 0; i < string.length;){\n        var c = string.charCodeAt(i++);\n        if (c === 61 && j > 1) break;\n        if ((c = s64[c]) === undefined) throw Error(invalidEncoding);\n        switch(j){\n            case 0:\n                t = c;\n                j = 1;\n                break;\n            case 1:\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\n                t = c;\n                j = 2;\n                break;\n            case 2:\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\n                t = c;\n                j = 3;\n                break;\n            case 3:\n                buffer[offset++] = (t & 3) << 6 | c;\n                j = 0;\n                break;\n        }\n    }\n    if (j === 1) throw Error(invalidEncoding);\n    return offset - start;\n};\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */ base64.test = function test(string) {\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvYmFzZTY0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWI7Ozs7Q0FJQyxHQUNELElBQUlBLFNBQVNDO0FBRWI7Ozs7Q0FJQyxHQUNERCxPQUFPRSxNQUFNLEdBQUcsU0FBU0EsT0FBT0MsTUFBTTtJQUNsQyxJQUFJQyxJQUFJRCxPQUFPRCxNQUFNO0lBQ3JCLElBQUksQ0FBQ0UsR0FDRCxPQUFPO0lBQ1gsSUFBSUMsSUFBSTtJQUNSLE1BQU8sRUFBRUQsSUFBSSxJQUFJLEtBQUtELE9BQU9HLE1BQU0sQ0FBQ0YsT0FBTyxJQUN2QyxFQUFFQztJQUNOLE9BQU9FLEtBQUtDLElBQUksQ0FBQ0wsT0FBT0QsTUFBTSxHQUFHLEtBQUssSUFBSUc7QUFDOUM7QUFFQSx3QkFBd0I7QUFDeEIsSUFBSUksTUFBTSxJQUFJQyxNQUFNO0FBRXBCLHdCQUF3QjtBQUN4QixJQUFJQyxNQUFNLElBQUlELE1BQU07QUFFcEIsa0NBQWtDO0FBQ2xDLElBQUssSUFBSUUsSUFBSSxHQUFHQSxJQUFJLElBQ2hCRCxHQUFHLENBQUNGLEdBQUcsQ0FBQ0csRUFBRSxHQUFHQSxJQUFJLEtBQUtBLElBQUksS0FBS0EsSUFBSSxLQUFLQSxJQUFJLEtBQUtBLElBQUksS0FBS0EsSUFBSSxJQUFJQSxJQUFJLEtBQUssR0FBRyxHQUFHQTtBQUVyRjs7Ozs7O0NBTUMsR0FDRFosT0FBT2EsTUFBTSxHQUFHLFNBQVNBLE9BQU9DLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxHQUFHO0lBQzlDLElBQUlDLFFBQVEsTUFDUkMsUUFBUSxFQUFFO0lBQ2QsSUFBSU4sSUFBSSxHQUNKTyxJQUFJLEdBQ0pDLEdBQU8sWUFBWTtJQUN2QixNQUFPTCxRQUFRQyxJQUFLO1FBQ2hCLElBQUlLLElBQUlQLE1BQU0sQ0FBQ0MsUUFBUTtRQUN2QixPQUFRSTtZQUNKLEtBQUs7Z0JBQ0RELEtBQUssQ0FBQ04sSUFBSSxHQUFHSCxHQUFHLENBQUNZLEtBQUssRUFBRTtnQkFDeEJELElBQUksQ0FBQ0MsSUFBSSxNQUFNO2dCQUNmRixJQUFJO2dCQUNKO1lBQ0osS0FBSztnQkFDREQsS0FBSyxDQUFDTixJQUFJLEdBQUdILEdBQUcsQ0FBQ1csSUFBSUMsS0FBSyxFQUFFO2dCQUM1QkQsSUFBSSxDQUFDQyxJQUFJLEVBQUMsS0FBTTtnQkFDaEJGLElBQUk7Z0JBQ0o7WUFDSixLQUFLO2dCQUNERCxLQUFLLENBQUNOLElBQUksR0FBR0gsR0FBRyxDQUFDVyxJQUFJQyxLQUFLLEVBQUU7Z0JBQzVCSCxLQUFLLENBQUNOLElBQUksR0FBR0gsR0FBRyxDQUFDWSxJQUFJLEdBQUc7Z0JBQ3hCRixJQUFJO2dCQUNKO1FBQ1I7UUFDQSxJQUFJUCxJQUFJLE1BQU07WUFDVEssQ0FBQUEsU0FBVUEsQ0FBQUEsUUFBUSxFQUFFLEdBQUdLLElBQUksQ0FBQ0MsT0FBT0MsWUFBWSxDQUFDQyxLQUFLLENBQUNGLFFBQVFMO1lBQy9ETixJQUFJO1FBQ1I7SUFDSjtJQUNBLElBQUlPLEdBQUc7UUFDSEQsS0FBSyxDQUFDTixJQUFJLEdBQUdILEdBQUcsQ0FBQ1csRUFBRTtRQUNuQkYsS0FBSyxDQUFDTixJQUFJLEdBQUc7UUFDYixJQUFJTyxNQUFNLEdBQ05ELEtBQUssQ0FBQ04sSUFBSSxHQUFHO0lBQ3JCO0lBQ0EsSUFBSUssT0FBTztRQUNQLElBQUlMLEdBQ0FLLE1BQU1LLElBQUksQ0FBQ0MsT0FBT0MsWUFBWSxDQUFDQyxLQUFLLENBQUNGLFFBQVFMLE1BQU1RLEtBQUssQ0FBQyxHQUFHZDtRQUNoRSxPQUFPSyxNQUFNVSxJQUFJLENBQUM7SUFDdEI7SUFDQSxPQUFPSixPQUFPQyxZQUFZLENBQUNDLEtBQUssQ0FBQ0YsUUFBUUwsTUFBTVEsS0FBSyxDQUFDLEdBQUdkO0FBQzVEO0FBRUEsSUFBSWdCLGtCQUFrQjtBQUV0Qjs7Ozs7OztDQU9DLEdBQ0Q1QixPQUFPNkIsTUFBTSxHQUFHLFNBQVNBLE9BQU8xQixNQUFNLEVBQUVXLE1BQU0sRUFBRWdCLE1BQU07SUFDbEQsSUFBSWYsUUFBUWU7SUFDWixJQUFJWCxJQUFJLEdBQ0pDLEdBQU8sWUFBWTtJQUN2QixJQUFLLElBQUlSLElBQUksR0FBR0EsSUFBSVQsT0FBT0QsTUFBTSxFQUFHO1FBQ2hDLElBQUk2QixJQUFJNUIsT0FBTzZCLFVBQVUsQ0FBQ3BCO1FBQzFCLElBQUltQixNQUFNLE1BQU1aLElBQUksR0FDaEI7UUFDSixJQUFJLENBQUNZLElBQUlwQixHQUFHLENBQUNvQixFQUFFLE1BQU1FLFdBQ2pCLE1BQU1DLE1BQU1OO1FBQ2hCLE9BQVFUO1lBQ0osS0FBSztnQkFDREMsSUFBSVc7Z0JBQ0paLElBQUk7Z0JBQ0o7WUFDSixLQUFLO2dCQUNETCxNQUFNLENBQUNnQixTQUFTLEdBQUdWLEtBQUssSUFBSSxDQUFDVyxJQUFJLEVBQUMsS0FBTTtnQkFDeENYLElBQUlXO2dCQUNKWixJQUFJO2dCQUNKO1lBQ0osS0FBSztnQkFDREwsTUFBTSxDQUFDZ0IsU0FBUyxHQUFHLENBQUNWLElBQUksRUFBQyxLQUFNLElBQUksQ0FBQ1csSUFBSSxFQUFDLEtBQU07Z0JBQy9DWCxJQUFJVztnQkFDSlosSUFBSTtnQkFDSjtZQUNKLEtBQUs7Z0JBQ0RMLE1BQU0sQ0FBQ2dCLFNBQVMsR0FBRyxDQUFDVixJQUFJLE1BQU0sSUFBSVc7Z0JBQ2xDWixJQUFJO2dCQUNKO1FBQ1I7SUFDSjtJQUNBLElBQUlBLE1BQU0sR0FDTixNQUFNZSxNQUFNTjtJQUNoQixPQUFPRSxTQUFTZjtBQUNwQjtBQUVBOzs7O0NBSUMsR0FDRGYsT0FBT21DLElBQUksR0FBRyxTQUFTQSxLQUFLaEMsTUFBTTtJQUM5QixPQUFPLG1FQUFtRWdDLElBQUksQ0FBQ2hDO0FBQ25GIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL25vZGVfbW9kdWxlcy9AcHJvdG9idWZqcy9iYXNlNjQvaW5kZXguanM/Njc4NiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuXHJcbi8qKlxyXG4gKiBBIG1pbmltYWwgYmFzZTY0IGltcGxlbWVudGF0aW9uIGZvciBudW1iZXIgYXJyYXlzLlxyXG4gKiBAbWVtYmVyb2YgdXRpbFxyXG4gKiBAbmFtZXNwYWNlXHJcbiAqL1xyXG52YXIgYmFzZTY0ID0gZXhwb3J0cztcclxuXHJcbi8qKlxyXG4gKiBDYWxjdWxhdGVzIHRoZSBieXRlIGxlbmd0aCBvZiBhIGJhc2U2NCBlbmNvZGVkIHN0cmluZy5cclxuICogQHBhcmFtIHtzdHJpbmd9IHN0cmluZyBCYXNlNjQgZW5jb2RlZCBzdHJpbmdcclxuICogQHJldHVybnMge251bWJlcn0gQnl0ZSBsZW5ndGhcclxuICovXHJcbmJhc2U2NC5sZW5ndGggPSBmdW5jdGlvbiBsZW5ndGgoc3RyaW5nKSB7XHJcbiAgICB2YXIgcCA9IHN0cmluZy5sZW5ndGg7XHJcbiAgICBpZiAoIXApXHJcbiAgICAgICAgcmV0dXJuIDA7XHJcbiAgICB2YXIgbiA9IDA7XHJcbiAgICB3aGlsZSAoLS1wICUgNCA+IDEgJiYgc3RyaW5nLmNoYXJBdChwKSA9PT0gXCI9XCIpXHJcbiAgICAgICAgKytuO1xyXG4gICAgcmV0dXJuIE1hdGguY2VpbChzdHJpbmcubGVuZ3RoICogMykgLyA0IC0gbjtcclxufTtcclxuXHJcbi8vIEJhc2U2NCBlbmNvZGluZyB0YWJsZVxyXG52YXIgYjY0ID0gbmV3IEFycmF5KDY0KTtcclxuXHJcbi8vIEJhc2U2NCBkZWNvZGluZyB0YWJsZVxyXG52YXIgczY0ID0gbmV3IEFycmF5KDEyMyk7XHJcblxyXG4vLyA2NS4uOTAsIDk3Li4xMjIsIDQ4Li41NywgNDMsIDQ3XHJcbmZvciAodmFyIGkgPSAwOyBpIDwgNjQ7KVxyXG4gICAgczY0W2I2NFtpXSA9IGkgPCAyNiA/IGkgKyA2NSA6IGkgPCA1MiA/IGkgKyA3MSA6IGkgPCA2MiA/IGkgLSA0IDogaSAtIDU5IHwgNDNdID0gaSsrO1xyXG5cclxuLyoqXHJcbiAqIEVuY29kZXMgYSBidWZmZXIgdG8gYSBiYXNlNjQgZW5jb2RlZCBzdHJpbmcuXHJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gYnVmZmVyIFNvdXJjZSBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IHN0YXJ0IFNvdXJjZSBzdGFydFxyXG4gKiBAcGFyYW0ge251bWJlcn0gZW5kIFNvdXJjZSBlbmRcclxuICogQHJldHVybnMge3N0cmluZ30gQmFzZTY0IGVuY29kZWQgc3RyaW5nXHJcbiAqL1xyXG5iYXNlNjQuZW5jb2RlID0gZnVuY3Rpb24gZW5jb2RlKGJ1ZmZlciwgc3RhcnQsIGVuZCkge1xyXG4gICAgdmFyIHBhcnRzID0gbnVsbCxcclxuICAgICAgICBjaHVuayA9IFtdO1xyXG4gICAgdmFyIGkgPSAwLCAvLyBvdXRwdXQgaW5kZXhcclxuICAgICAgICBqID0gMCwgLy8gZ290byBpbmRleFxyXG4gICAgICAgIHQ7ICAgICAvLyB0ZW1wb3JhcnlcclxuICAgIHdoaWxlIChzdGFydCA8IGVuZCkge1xyXG4gICAgICAgIHZhciBiID0gYnVmZmVyW3N0YXJ0KytdO1xyXG4gICAgICAgIHN3aXRjaCAoaikge1xyXG4gICAgICAgICAgICBjYXNlIDA6XHJcbiAgICAgICAgICAgICAgICBjaHVua1tpKytdID0gYjY0W2IgPj4gMl07XHJcbiAgICAgICAgICAgICAgICB0ID0gKGIgJiAzKSA8PCA0O1xyXG4gICAgICAgICAgICAgICAgaiA9IDE7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgY2FzZSAxOlxyXG4gICAgICAgICAgICAgICAgY2h1bmtbaSsrXSA9IGI2NFt0IHwgYiA+PiA0XTtcclxuICAgICAgICAgICAgICAgIHQgPSAoYiAmIDE1KSA8PCAyO1xyXG4gICAgICAgICAgICAgICAgaiA9IDI7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgY2FzZSAyOlxyXG4gICAgICAgICAgICAgICAgY2h1bmtbaSsrXSA9IGI2NFt0IHwgYiA+PiA2XTtcclxuICAgICAgICAgICAgICAgIGNodW5rW2krK10gPSBiNjRbYiAmIDYzXTtcclxuICAgICAgICAgICAgICAgIGogPSAwO1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChpID4gODE5MSkge1xyXG4gICAgICAgICAgICAocGFydHMgfHwgKHBhcnRzID0gW10pKS5wdXNoKFN0cmluZy5mcm9tQ2hhckNvZGUuYXBwbHkoU3RyaW5nLCBjaHVuaykpO1xyXG4gICAgICAgICAgICBpID0gMDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBpZiAoaikge1xyXG4gICAgICAgIGNodW5rW2krK10gPSBiNjRbdF07XHJcbiAgICAgICAgY2h1bmtbaSsrXSA9IDYxO1xyXG4gICAgICAgIGlmIChqID09PSAxKVxyXG4gICAgICAgICAgICBjaHVua1tpKytdID0gNjE7XHJcbiAgICB9XHJcbiAgICBpZiAocGFydHMpIHtcclxuICAgICAgICBpZiAoaSlcclxuICAgICAgICAgICAgcGFydHMucHVzaChTdHJpbmcuZnJvbUNoYXJDb2RlLmFwcGx5KFN0cmluZywgY2h1bmsuc2xpY2UoMCwgaSkpKTtcclxuICAgICAgICByZXR1cm4gcGFydHMuam9pbihcIlwiKTtcclxuICAgIH1cclxuICAgIHJldHVybiBTdHJpbmcuZnJvbUNoYXJDb2RlLmFwcGx5KFN0cmluZywgY2h1bmsuc2xpY2UoMCwgaSkpO1xyXG59O1xyXG5cclxudmFyIGludmFsaWRFbmNvZGluZyA9IFwiaW52YWxpZCBlbmNvZGluZ1wiO1xyXG5cclxuLyoqXHJcbiAqIERlY29kZXMgYSBiYXNlNjQgZW5jb2RlZCBzdHJpbmcgdG8gYSBidWZmZXIuXHJcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmcgU291cmNlIHN0cmluZ1xyXG4gKiBAcGFyYW0ge1VpbnQ4QXJyYXl9IGJ1ZmZlciBEZXN0aW5hdGlvbiBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IG9mZnNldCBEZXN0aW5hdGlvbiBvZmZzZXRcclxuICogQHJldHVybnMge251bWJlcn0gTnVtYmVyIG9mIGJ5dGVzIHdyaXR0ZW5cclxuICogQHRocm93cyB7RXJyb3J9IElmIGVuY29kaW5nIGlzIGludmFsaWRcclxuICovXHJcbmJhc2U2NC5kZWNvZGUgPSBmdW5jdGlvbiBkZWNvZGUoc3RyaW5nLCBidWZmZXIsIG9mZnNldCkge1xyXG4gICAgdmFyIHN0YXJ0ID0gb2Zmc2V0O1xyXG4gICAgdmFyIGogPSAwLCAvLyBnb3RvIGluZGV4XHJcbiAgICAgICAgdDsgICAgIC8vIHRlbXBvcmFyeVxyXG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBzdHJpbmcubGVuZ3RoOykge1xyXG4gICAgICAgIHZhciBjID0gc3RyaW5nLmNoYXJDb2RlQXQoaSsrKTtcclxuICAgICAgICBpZiAoYyA9PT0gNjEgJiYgaiA+IDEpXHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGlmICgoYyA9IHM2NFtjXSkgPT09IHVuZGVmaW5lZClcclxuICAgICAgICAgICAgdGhyb3cgRXJyb3IoaW52YWxpZEVuY29kaW5nKTtcclxuICAgICAgICBzd2l0Y2ggKGopIHtcclxuICAgICAgICAgICAgY2FzZSAwOlxyXG4gICAgICAgICAgICAgICAgdCA9IGM7XHJcbiAgICAgICAgICAgICAgICBqID0gMTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIDE6XHJcbiAgICAgICAgICAgICAgICBidWZmZXJbb2Zmc2V0KytdID0gdCA8PCAyIHwgKGMgJiA0OCkgPj4gNDtcclxuICAgICAgICAgICAgICAgIHQgPSBjO1xyXG4gICAgICAgICAgICAgICAgaiA9IDI7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgY2FzZSAyOlxyXG4gICAgICAgICAgICAgICAgYnVmZmVyW29mZnNldCsrXSA9ICh0ICYgMTUpIDw8IDQgfCAoYyAmIDYwKSA+PiAyO1xyXG4gICAgICAgICAgICAgICAgdCA9IGM7XHJcbiAgICAgICAgICAgICAgICBqID0gMztcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIDM6XHJcbiAgICAgICAgICAgICAgICBidWZmZXJbb2Zmc2V0KytdID0gKHQgJiAzKSA8PCA2IHwgYztcclxuICAgICAgICAgICAgICAgIGogPSAwO1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgaWYgKGogPT09IDEpXHJcbiAgICAgICAgdGhyb3cgRXJyb3IoaW52YWxpZEVuY29kaW5nKTtcclxuICAgIHJldHVybiBvZmZzZXQgLSBzdGFydDtcclxufTtcclxuXHJcbi8qKlxyXG4gKiBUZXN0cyBpZiB0aGUgc3BlY2lmaWVkIHN0cmluZyBhcHBlYXJzIHRvIGJlIGJhc2U2NCBlbmNvZGVkLlxyXG4gKiBAcGFyYW0ge3N0cmluZ30gc3RyaW5nIFN0cmluZyB0byB0ZXN0XHJcbiAqIEByZXR1cm5zIHtib29sZWFufSBgdHJ1ZWAgaWYgcHJvYmFibHkgYmFzZTY0IGVuY29kZWQsIG90aGVyd2lzZSBmYWxzZVxyXG4gKi9cclxuYmFzZTY0LnRlc3QgPSBmdW5jdGlvbiB0ZXN0KHN0cmluZykge1xyXG4gICAgcmV0dXJuIC9eKD86W0EtWmEtejAtOSsvXXs0fSkqKD86W0EtWmEtejAtOSsvXXsyfT09fFtBLVphLXowLTkrL117M309KT8kLy50ZXN0KHN0cmluZyk7XHJcbn07XHJcbiJdLCJuYW1lcyI6WyJiYXNlNjQiLCJleHBvcnRzIiwibGVuZ3RoIiwic3RyaW5nIiwicCIsIm4iLCJjaGFyQXQiLCJNYXRoIiwiY2VpbCIsImI2NCIsIkFycmF5IiwiczY0IiwiaSIsImVuY29kZSIsImJ1ZmZlciIsInN0YXJ0IiwiZW5kIiwicGFydHMiLCJjaHVuayIsImoiLCJ0IiwiYiIsInB1c2giLCJTdHJpbmciLCJmcm9tQ2hhckNvZGUiLCJhcHBseSIsInNsaWNlIiwiam9pbiIsImludmFsaWRFbmNvZGluZyIsImRlY29kZSIsIm9mZnNldCIsImMiLCJjaGFyQ29kZUF0IiwidW5kZWZpbmVkIiwiRXJyb3IiLCJ0ZXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/base64/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/codegen/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@protobufjs/codegen/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = codegen;\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */ function codegen(functionParams, functionName) {\n    /* istanbul ignore if */ if (typeof functionParams === \"string\") {\n        functionName = functionParams;\n        functionParams = undefined;\n    }\n    var body = [];\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */ function Codegen(formatStringOrScope) {\n        // note that explicit array handling below makes this ~50% faster\n        // finish the function\n        if (typeof formatStringOrScope !== \"string\") {\n            var source = toString();\n            if (codegen.verbose) console.log(\"codegen: \" + source); // eslint-disable-line no-console\n            source = \"return \" + source;\n            if (formatStringOrScope) {\n                var scopeKeys = Object.keys(formatStringOrScope), scopeParams = new Array(scopeKeys.length + 1), scopeValues = new Array(scopeKeys.length), scopeOffset = 0;\n                while(scopeOffset < scopeKeys.length){\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\n                }\n                scopeParams[scopeOffset] = source;\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\n            }\n            return Function(source)(); // eslint-disable-line no-new-func\n        }\n        // otherwise append to body\n        var formatParams = new Array(arguments.length - 1), formatOffset = 0;\n        while(formatOffset < formatParams.length)formatParams[formatOffset] = arguments[++formatOffset];\n        formatOffset = 0;\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\n            var value = formatParams[formatOffset++];\n            switch($1){\n                case \"d\":\n                case \"f\":\n                    return String(Number(value));\n                case \"i\":\n                    return String(Math.floor(value));\n                case \"j\":\n                    return JSON.stringify(value);\n                case \"s\":\n                    return String(value);\n            }\n            return \"%\";\n        });\n        if (formatOffset !== formatParams.length) throw Error(\"parameter count mismatch\");\n        body.push(formatStringOrScope);\n        return Codegen;\n    }\n    function toString(functionNameOverride) {\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\n    }\n    Codegen.toString = toString;\n    return Codegen;\n}\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */ /**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */ codegen.verbose = false;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/codegen/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/eventemitter/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@protobufjs/eventemitter/index.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\nmodule.exports = EventEmitter;\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */ function EventEmitter() {\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */ this._listeners = {};\n}\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */ EventEmitter.prototype.on = function on(evt, fn, ctx) {\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\n        fn: fn,\n        ctx: ctx || this\n    });\n    return this;\n};\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */ EventEmitter.prototype.off = function off(evt, fn) {\n    if (evt === undefined) this._listeners = {};\n    else {\n        if (fn === undefined) this._listeners[evt] = [];\n        else {\n            var listeners = this._listeners[evt];\n            for(var i = 0; i < listeners.length;)if (listeners[i].fn === fn) listeners.splice(i, 1);\n            else ++i;\n        }\n    }\n    return this;\n};\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */ EventEmitter.prototype.emit = function emit(evt) {\n    var listeners = this._listeners[evt];\n    if (listeners) {\n        var args = [], i = 1;\n        for(; i < arguments.length;)args.push(arguments[i++]);\n        for(i = 0; i < listeners.length;)listeners[i].fn.apply(listeners[i++].ctx, args);\n    }\n    return this;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/eventemitter/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/fetch/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@protobufjs/fetch/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = fetch;\nvar asPromise = __webpack_require__(/*! @protobufjs/aspromise */ \"(rsc)/./node_modules/@protobufjs/aspromise/index.js\"), inquire = __webpack_require__(/*! @protobufjs/inquire */ \"(rsc)/./node_modules/@protobufjs/inquire/index.js\");\nvar fs = inquire(\"fs\");\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */ /**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */ /**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */ function fetch(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else if (!options) options = {};\n    if (!callback) return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\n    if (!options.xhr && fs && fs.readFile) return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\n        return err && typeof XMLHttpRequest !== \"undefined\" ? fetch.xhr(filename, options, callback) : err ? callback(err) : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\n    });\n    // use the XHR version otherwise.\n    return fetch.xhr(filename, options, callback);\n}\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */ /**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */ /**/ fetch.xhr = function fetch_xhr(filename, options, callback) {\n    var xhr = new XMLHttpRequest();\n    xhr.onreadystatechange /* works everywhere */  = function fetchOnReadyStateChange() {\n        if (xhr.readyState !== 4) return undefined;\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\n        // reliably distinguished from an actually empty file for security reasons. feel free\n        // to send a pull request if you are aware of a solution.\n        if (xhr.status !== 0 && xhr.status !== 200) return callback(Error(\"status \" + xhr.status));\n        // if binary data is expected, make sure that some sort of array is returned, even if\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\n        if (options.binary) {\n            var buffer = xhr.response;\n            if (!buffer) {\n                buffer = [];\n                for(var i = 0; i < xhr.responseText.length; ++i)buffer.push(xhr.responseText.charCodeAt(i) & 255);\n            }\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\n        }\n        return callback(null, xhr.responseText);\n    };\n    if (options.binary) {\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\n        if (\"overrideMimeType\" in xhr) xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\n        xhr.responseType = \"arraybuffer\";\n    }\n    xhr.open(\"GET\", filename);\n    xhr.send();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/fetch/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/float/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@protobufjs/float/index.js ***!
  \*************************************************/
/***/ ((module) => {

eval("\nmodule.exports = factory(factory);\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */ /**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */ /**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */ /**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */ /**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */ /**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */ /**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */ /**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */ /**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */ // Factory function for the purpose of node-based testing in modified global environments\nfunction factory(exports) {\n    // float: typed array\n    if (typeof Float32Array !== \"undefined\") (function() {\n        var f32 = new Float32Array([\n            -0\n        ]), f8b = new Uint8Array(f32.buffer), le = f8b[3] === 128;\n        function writeFloat_f32_cpy(val, buf, pos) {\n            f32[0] = val;\n            buf[pos] = f8b[0];\n            buf[pos + 1] = f8b[1];\n            buf[pos + 2] = f8b[2];\n            buf[pos + 3] = f8b[3];\n        }\n        function writeFloat_f32_rev(val, buf, pos) {\n            f32[0] = val;\n            buf[pos] = f8b[3];\n            buf[pos + 1] = f8b[2];\n            buf[pos + 2] = f8b[1];\n            buf[pos + 3] = f8b[0];\n        }\n        /* istanbul ignore next */ exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\n        /* istanbul ignore next */ exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\n        function readFloat_f32_cpy(buf, pos) {\n            f8b[0] = buf[pos];\n            f8b[1] = buf[pos + 1];\n            f8b[2] = buf[pos + 2];\n            f8b[3] = buf[pos + 3];\n            return f32[0];\n        }\n        function readFloat_f32_rev(buf, pos) {\n            f8b[3] = buf[pos];\n            f8b[2] = buf[pos + 1];\n            f8b[1] = buf[pos + 2];\n            f8b[0] = buf[pos + 3];\n            return f32[0];\n        }\n        /* istanbul ignore next */ exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\n        /* istanbul ignore next */ exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\n    // float: ieee754\n    })();\n    else (function() {\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\n            var sign = val < 0 ? 1 : 0;\n            if (sign) val = -val;\n            if (val === 0) writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\n            else if (isNaN(val)) writeUint(2143289344, buf, pos);\n            else if (val > 3.4028234663852886e+38) writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\n            else if (val < 1.1754943508222875e-38) writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\n            else {\n                var exponent = Math.floor(Math.log(val) / Math.LN2), mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\n            }\n        }\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\n        function readFloat_ieee754(readUint, buf, pos) {\n            var uint = readUint(buf, pos), sign = (uint >> 31) * 2 + 1, exponent = uint >>> 23 & 255, mantissa = uint & 8388607;\n            return exponent === 255 ? mantissa ? NaN : sign * Infinity : exponent === 0 // denormal\n             ? sign * 1.401298464324817e-45 * mantissa : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\n        }\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\n    })();\n    // double: typed array\n    if (typeof Float64Array !== \"undefined\") (function() {\n        var f64 = new Float64Array([\n            -0\n        ]), f8b = new Uint8Array(f64.buffer), le = f8b[7] === 128;\n        function writeDouble_f64_cpy(val, buf, pos) {\n            f64[0] = val;\n            buf[pos] = f8b[0];\n            buf[pos + 1] = f8b[1];\n            buf[pos + 2] = f8b[2];\n            buf[pos + 3] = f8b[3];\n            buf[pos + 4] = f8b[4];\n            buf[pos + 5] = f8b[5];\n            buf[pos + 6] = f8b[6];\n            buf[pos + 7] = f8b[7];\n        }\n        function writeDouble_f64_rev(val, buf, pos) {\n            f64[0] = val;\n            buf[pos] = f8b[7];\n            buf[pos + 1] = f8b[6];\n            buf[pos + 2] = f8b[5];\n            buf[pos + 3] = f8b[4];\n            buf[pos + 4] = f8b[3];\n            buf[pos + 5] = f8b[2];\n            buf[pos + 6] = f8b[1];\n            buf[pos + 7] = f8b[0];\n        }\n        /* istanbul ignore next */ exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\n        /* istanbul ignore next */ exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\n        function readDouble_f64_cpy(buf, pos) {\n            f8b[0] = buf[pos];\n            f8b[1] = buf[pos + 1];\n            f8b[2] = buf[pos + 2];\n            f8b[3] = buf[pos + 3];\n            f8b[4] = buf[pos + 4];\n            f8b[5] = buf[pos + 5];\n            f8b[6] = buf[pos + 6];\n            f8b[7] = buf[pos + 7];\n            return f64[0];\n        }\n        function readDouble_f64_rev(buf, pos) {\n            f8b[7] = buf[pos];\n            f8b[6] = buf[pos + 1];\n            f8b[5] = buf[pos + 2];\n            f8b[4] = buf[pos + 3];\n            f8b[3] = buf[pos + 4];\n            f8b[2] = buf[pos + 5];\n            f8b[1] = buf[pos + 6];\n            f8b[0] = buf[pos + 7];\n            return f64[0];\n        }\n        /* istanbul ignore next */ exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\n        /* istanbul ignore next */ exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\n    // double: ieee754\n    })();\n    else (function() {\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\n            var sign = val < 0 ? 1 : 0;\n            if (sign) val = -val;\n            if (val === 0) {\n                writeUint(0, buf, pos + off0);\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\n            } else if (isNaN(val)) {\n                writeUint(0, buf, pos + off0);\n                writeUint(2146959360, buf, pos + off1);\n            } else if (val > 1.7976931348623157e+308) {\n                writeUint(0, buf, pos + off0);\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\n            } else {\n                var mantissa;\n                if (val < 2.2250738585072014e-308) {\n                    mantissa = val / 5e-324;\n                    writeUint(mantissa >>> 0, buf, pos + off0);\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\n                } else {\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\n                    if (exponent === 1024) exponent = 1023;\n                    mantissa = val * Math.pow(2, -exponent);\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\n                }\n            }\n        }\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\n            var lo = readUint(buf, pos + off0), hi = readUint(buf, pos + off1);\n            var sign = (hi >> 31) * 2 + 1, exponent = hi >>> 20 & 2047, mantissa = 4294967296 * (hi & 1048575) + lo;\n            return exponent === 2047 ? mantissa ? NaN : sign * Infinity : exponent === 0 // denormal\n             ? sign * 5e-324 * mantissa : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\n        }\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\n    })();\n    return exports;\n}\n// uint helpers\nfunction writeUintLE(val, buf, pos) {\n    buf[pos] = val & 255;\n    buf[pos + 1] = val >>> 8 & 255;\n    buf[pos + 2] = val >>> 16 & 255;\n    buf[pos + 3] = val >>> 24;\n}\nfunction writeUintBE(val, buf, pos) {\n    buf[pos] = val >>> 24;\n    buf[pos + 1] = val >>> 16 & 255;\n    buf[pos + 2] = val >>> 8 & 255;\n    buf[pos + 3] = val & 255;\n}\nfunction readUintLE(buf, pos) {\n    return (buf[pos] | buf[pos + 1] << 8 | buf[pos + 2] << 16 | buf[pos + 3] << 24) >>> 0;\n}\nfunction readUintBE(buf, pos) {\n    return (buf[pos] << 24 | buf[pos + 1] << 16 | buf[pos + 2] << 8 | buf[pos + 3]) >>> 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvZmxvYXQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsT0FBT0MsT0FBTyxHQUFHQyxRQUFRQTtBQUV6Qjs7OztDQUlDLEdBRUQ7Ozs7Ozs7O0NBUUMsR0FFRDs7Ozs7Ozs7Q0FRQyxHQUVEOzs7Ozs7O0NBT0MsR0FFRDs7Ozs7OztDQU9DLEdBRUQ7Ozs7Ozs7O0NBUUMsR0FFRDs7Ozs7Ozs7Q0FRQyxHQUVEOzs7Ozs7O0NBT0MsR0FFRDs7Ozs7OztDQU9DLEdBRUQseUZBQXlGO0FBQ3pGLFNBQVNBLFFBQVFELE9BQU87SUFFcEIscUJBQXFCO0lBQ3JCLElBQUksT0FBT0UsaUJBQWlCLGFBQWEsQ0FBQztRQUV0QyxJQUFJQyxNQUFNLElBQUlELGFBQWE7WUFBRSxDQUFDO1NBQUcsR0FDN0JFLE1BQU0sSUFBSUMsV0FBV0YsSUFBSUcsTUFBTSxHQUMvQkMsS0FBTUgsR0FBRyxDQUFDLEVBQUUsS0FBSztRQUVyQixTQUFTSSxtQkFBbUJDLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxHQUFHO1lBQ3JDUixHQUFHLENBQUMsRUFBRSxHQUFHTTtZQUNUQyxHQUFHLENBQUNDLElBQVEsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7UUFDekI7UUFFQSxTQUFTUSxtQkFBbUJILEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxHQUFHO1lBQ3JDUixHQUFHLENBQUMsRUFBRSxHQUFHTTtZQUNUQyxHQUFHLENBQUNDLElBQVEsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7UUFDekI7UUFFQSx3QkFBd0IsR0FDeEJKLFFBQVFhLFlBQVksR0FBR04sS0FBS0MscUJBQXFCSTtRQUNqRCx3QkFBd0IsR0FDeEJaLFFBQVFjLFlBQVksR0FBR1AsS0FBS0sscUJBQXFCSjtRQUVqRCxTQUFTTyxrQkFBa0JMLEdBQUcsRUFBRUMsR0FBRztZQUMvQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxJQUFRO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCLE9BQU9SLEdBQUcsQ0FBQyxFQUFFO1FBQ2pCO1FBRUEsU0FBU2Esa0JBQWtCTixHQUFHLEVBQUVDLEdBQUc7WUFDL0JQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsSUFBUTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQixPQUFPUixHQUFHLENBQUMsRUFBRTtRQUNqQjtRQUVBLHdCQUF3QixHQUN4QkgsUUFBUWlCLFdBQVcsR0FBR1YsS0FBS1Esb0JBQW9CQztRQUMvQyx3QkFBd0IsR0FDeEJoQixRQUFRa0IsV0FBVyxHQUFHWCxLQUFLUyxvQkFBb0JEO0lBRW5ELGlCQUFpQjtJQUNqQjtTQUFXLENBQUM7UUFFUixTQUFTSSxtQkFBbUJDLFNBQVMsRUFBRVgsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLEdBQUc7WUFDaEQsSUFBSVUsT0FBT1osTUFBTSxJQUFJLElBQUk7WUFDekIsSUFBSVksTUFDQVosTUFBTSxDQUFDQTtZQUNYLElBQUlBLFFBQVEsR0FDUlcsVUFBVSxJQUFJWCxNQUFNLElBQUksWUFBWSxHQUFHLElBQUksY0FBYyxHQUFHLFlBQVlDLEtBQUtDO2lCQUM1RSxJQUFJVyxNQUFNYixNQUNYVyxVQUFVLFlBQVlWLEtBQUtDO2lCQUMxQixJQUFJRixNQUFNLHdCQUNYVyxVQUFVLENBQUNDLFFBQVEsS0FBSyxVQUFTLE1BQU8sR0FBR1gsS0FBS0M7aUJBQy9DLElBQUlGLE1BQU0sd0JBQ1hXLFVBQVUsQ0FBQ0MsUUFBUSxLQUFLRSxLQUFLQyxLQUFLLENBQUNmLE1BQU0sc0JBQXFCLE1BQU8sR0FBR0MsS0FBS0M7aUJBQzVFO2dCQUNELElBQUljLFdBQVdGLEtBQUtHLEtBQUssQ0FBQ0gsS0FBS0ksR0FBRyxDQUFDbEIsT0FBT2MsS0FBS0ssR0FBRyxHQUM5Q0MsV0FBV04sS0FBS0MsS0FBSyxDQUFDZixNQUFNYyxLQUFLTyxHQUFHLENBQUMsR0FBRyxDQUFDTCxZQUFZLFdBQVc7Z0JBQ3BFTCxVQUFVLENBQUNDLFFBQVEsS0FBS0ksV0FBVyxPQUFPLEtBQUtJLFFBQU8sTUFBTyxHQUFHbkIsS0FBS0M7WUFDekU7UUFDSjtRQUVBWCxRQUFRYSxZQUFZLEdBQUdNLG1CQUFtQlksSUFBSSxDQUFDLE1BQU1DO1FBQ3JEaEMsUUFBUWMsWUFBWSxHQUFHSyxtQkFBbUJZLElBQUksQ0FBQyxNQUFNRTtRQUVyRCxTQUFTQyxrQkFBa0JDLFFBQVEsRUFBRXpCLEdBQUcsRUFBRUMsR0FBRztZQUN6QyxJQUFJeUIsT0FBT0QsU0FBU3pCLEtBQUtDLE1BQ3JCVSxPQUFPLENBQUNlLFFBQVEsRUFBQyxJQUFLLElBQUksR0FDMUJYLFdBQVdXLFNBQVMsS0FBSyxLQUN6QlAsV0FBV08sT0FBTztZQUN0QixPQUFPWCxhQUFhLE1BQ2RJLFdBQ0FRLE1BQ0FoQixPQUFPaUIsV0FDUGIsYUFBYSxFQUFFLFdBQVc7ZUFDMUJKLE9BQU8sd0JBQXdCUSxXQUMvQlIsT0FBT0UsS0FBS08sR0FBRyxDQUFDLEdBQUdMLFdBQVcsT0FBUUksQ0FBQUEsV0FBVyxPQUFNO1FBQ2pFO1FBRUE3QixRQUFRaUIsV0FBVyxHQUFHaUIsa0JBQWtCSCxJQUFJLENBQUMsTUFBTVE7UUFDbkR2QyxRQUFRa0IsV0FBVyxHQUFHZ0Isa0JBQWtCSCxJQUFJLENBQUMsTUFBTVM7SUFFdkQ7SUFFQSxzQkFBc0I7SUFDdEIsSUFBSSxPQUFPQyxpQkFBaUIsYUFBYSxDQUFDO1FBRXRDLElBQUlDLE1BQU0sSUFBSUQsYUFBYTtZQUFDLENBQUM7U0FBRSxHQUMzQnJDLE1BQU0sSUFBSUMsV0FBV3FDLElBQUlwQyxNQUFNLEdBQy9CQyxLQUFNSCxHQUFHLENBQUMsRUFBRSxLQUFLO1FBRXJCLFNBQVN1QyxvQkFBb0JsQyxHQUFHLEVBQUVDLEdBQUcsRUFBRUMsR0FBRztZQUN0QytCLEdBQUcsQ0FBQyxFQUFFLEdBQUdqQztZQUNUQyxHQUFHLENBQUNDLElBQVEsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1FBQ3pCO1FBRUEsU0FBU3dDLG9CQUFvQm5DLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxHQUFHO1lBQ3RDK0IsR0FBRyxDQUFDLEVBQUUsR0FBR2pDO1lBQ1RDLEdBQUcsQ0FBQ0MsSUFBUSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7WUFDckJNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUdQLEdBQUcsQ0FBQyxFQUFFO1lBQ3JCTSxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFHUCxHQUFHLENBQUMsRUFBRTtZQUNyQk0sR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBR1AsR0FBRyxDQUFDLEVBQUU7UUFDekI7UUFFQSx3QkFBd0IsR0FDeEJKLFFBQVE2QyxhQUFhLEdBQUd0QyxLQUFLb0Msc0JBQXNCQztRQUNuRCx3QkFBd0IsR0FDeEI1QyxRQUFROEMsYUFBYSxHQUFHdkMsS0FBS3FDLHNCQUFzQkQ7UUFFbkQsU0FBU0ksbUJBQW1CckMsR0FBRyxFQUFFQyxHQUFHO1lBQ2hDUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLElBQVE7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCLE9BQU8rQixHQUFHLENBQUMsRUFBRTtRQUNqQjtRQUVBLFNBQVNNLG1CQUFtQnRDLEdBQUcsRUFBRUMsR0FBRztZQUNoQ1AsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxJQUFRO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQlAsR0FBRyxDQUFDLEVBQUUsR0FBR00sR0FBRyxDQUFDQyxNQUFNLEVBQUU7WUFDckJQLEdBQUcsQ0FBQyxFQUFFLEdBQUdNLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFO1lBQ3JCUCxHQUFHLENBQUMsRUFBRSxHQUFHTSxHQUFHLENBQUNDLE1BQU0sRUFBRTtZQUNyQixPQUFPK0IsR0FBRyxDQUFDLEVBQUU7UUFDakI7UUFFQSx3QkFBd0IsR0FDeEIxQyxRQUFRaUQsWUFBWSxHQUFHMUMsS0FBS3dDLHFCQUFxQkM7UUFDakQsd0JBQXdCLEdBQ3hCaEQsUUFBUWtELFlBQVksR0FBRzNDLEtBQUt5QyxxQkFBcUJEO0lBRXJELGtCQUFrQjtJQUNsQjtTQUFXLENBQUM7UUFFUixTQUFTSSxvQkFBb0IvQixTQUFTLEVBQUVnQyxJQUFJLEVBQUVDLElBQUksRUFBRTVDLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxHQUFHO1lBQzdELElBQUlVLE9BQU9aLE1BQU0sSUFBSSxJQUFJO1lBQ3pCLElBQUlZLE1BQ0FaLE1BQU0sQ0FBQ0E7WUFDWCxJQUFJQSxRQUFRLEdBQUc7Z0JBQ1hXLFVBQVUsR0FBR1YsS0FBS0MsTUFBTXlDO2dCQUN4QmhDLFVBQVUsSUFBSVgsTUFBTSxJQUFJLFlBQVksR0FBRyxJQUFJLGNBQWMsR0FBRyxZQUFZQyxLQUFLQyxNQUFNMEM7WUFDdkYsT0FBTyxJQUFJL0IsTUFBTWIsTUFBTTtnQkFDbkJXLFVBQVUsR0FBR1YsS0FBS0MsTUFBTXlDO2dCQUN4QmhDLFVBQVUsWUFBWVYsS0FBS0MsTUFBTTBDO1lBQ3JDLE9BQU8sSUFBSTVDLE1BQU0seUJBQXlCO2dCQUN0Q1csVUFBVSxHQUFHVixLQUFLQyxNQUFNeUM7Z0JBQ3hCaEMsVUFBVSxDQUFDQyxRQUFRLEtBQUssVUFBUyxNQUFPLEdBQUdYLEtBQUtDLE1BQU0wQztZQUMxRCxPQUFPO2dCQUNILElBQUl4QjtnQkFDSixJQUFJcEIsTUFBTSx5QkFBeUI7b0JBQy9Cb0IsV0FBV3BCLE1BQU07b0JBQ2pCVyxVQUFVUyxhQUFhLEdBQUduQixLQUFLQyxNQUFNeUM7b0JBQ3JDaEMsVUFBVSxDQUFDQyxRQUFRLEtBQUtRLFdBQVcsVUFBUyxNQUFPLEdBQUduQixLQUFLQyxNQUFNMEM7Z0JBQ3JFLE9BQU87b0JBQ0gsSUFBSTVCLFdBQVdGLEtBQUtHLEtBQUssQ0FBQ0gsS0FBS0ksR0FBRyxDQUFDbEIsT0FBT2MsS0FBS0ssR0FBRztvQkFDbEQsSUFBSUgsYUFBYSxNQUNiQSxXQUFXO29CQUNmSSxXQUFXcEIsTUFBTWMsS0FBS08sR0FBRyxDQUFDLEdBQUcsQ0FBQ0w7b0JBQzlCTCxVQUFVUyxXQUFXLHFCQUFxQixHQUFHbkIsS0FBS0MsTUFBTXlDO29CQUN4RGhDLFVBQVUsQ0FBQ0MsUUFBUSxLQUFLSSxXQUFXLFFBQVEsS0FBS0ksV0FBVyxVQUFVLE9BQU0sTUFBTyxHQUFHbkIsS0FBS0MsTUFBTTBDO2dCQUNwRztZQUNKO1FBQ0o7UUFFQXJELFFBQVE2QyxhQUFhLEdBQUdNLG9CQUFvQnBCLElBQUksQ0FBQyxNQUFNQyxhQUFhLEdBQUc7UUFDdkVoQyxRQUFROEMsYUFBYSxHQUFHSyxvQkFBb0JwQixJQUFJLENBQUMsTUFBTUUsYUFBYSxHQUFHO1FBRXZFLFNBQVNxQixtQkFBbUJuQixRQUFRLEVBQUVpQixJQUFJLEVBQUVDLElBQUksRUFBRTNDLEdBQUcsRUFBRUMsR0FBRztZQUN0RCxJQUFJNEMsS0FBS3BCLFNBQVN6QixLQUFLQyxNQUFNeUMsT0FDekJJLEtBQUtyQixTQUFTekIsS0FBS0MsTUFBTTBDO1lBQzdCLElBQUloQyxPQUFPLENBQUNtQyxNQUFNLEVBQUMsSUFBSyxJQUFJLEdBQ3hCL0IsV0FBVytCLE9BQU8sS0FBSyxNQUN2QjNCLFdBQVcsYUFBYzJCLENBQUFBLEtBQUssT0FBTSxJQUFLRDtZQUM3QyxPQUFPOUIsYUFBYSxPQUNkSSxXQUNBUSxNQUNBaEIsT0FBT2lCLFdBQ1BiLGFBQWEsRUFBRSxXQUFXO2VBQzFCSixPQUFPLFNBQVNRLFdBQ2hCUixPQUFPRSxLQUFLTyxHQUFHLENBQUMsR0FBR0wsV0FBVyxRQUFTSSxDQUFBQSxXQUFXLGdCQUFlO1FBQzNFO1FBRUE3QixRQUFRaUQsWUFBWSxHQUFHSyxtQkFBbUJ2QixJQUFJLENBQUMsTUFBTVEsWUFBWSxHQUFHO1FBQ3BFdkMsUUFBUWtELFlBQVksR0FBR0ksbUJBQW1CdkIsSUFBSSxDQUFDLE1BQU1TLFlBQVksR0FBRztJQUV4RTtJQUVBLE9BQU94QztBQUNYO0FBRUEsZUFBZTtBQUVmLFNBQVNnQyxZQUFZdkIsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLEdBQUc7SUFDOUJELEdBQUcsQ0FBQ0MsSUFBUSxHQUFJRixNQUFhO0lBQzdCQyxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFJRixRQUFRLElBQUs7SUFDN0JDLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUlGLFFBQVEsS0FBSztJQUM3QkMsR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBSUYsUUFBUTtBQUM1QjtBQUVBLFNBQVN3QixZQUFZeEIsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLEdBQUc7SUFDOUJELEdBQUcsQ0FBQ0MsSUFBUSxHQUFJRixRQUFRO0lBQ3hCQyxHQUFHLENBQUNDLE1BQU0sRUFBRSxHQUFJRixRQUFRLEtBQUs7SUFDN0JDLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLEdBQUlGLFFBQVEsSUFBSztJQUM3QkMsR0FBRyxDQUFDQyxNQUFNLEVBQUUsR0FBSUYsTUFBYTtBQUNqQztBQUVBLFNBQVM4QixXQUFXN0IsR0FBRyxFQUFFQyxHQUFHO0lBQ3hCLE9BQU8sQ0FBQ0QsR0FBRyxDQUFDQyxJQUFRLEdBQ1pELEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLElBQUksSUFDaEJELEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLElBQUksS0FDaEJELEdBQUcsQ0FBQ0MsTUFBTSxFQUFFLElBQUksRUFBQyxNQUFPO0FBQ3BDO0FBRUEsU0FBUzZCLFdBQVc5QixHQUFHLEVBQUVDLEdBQUc7SUFDeEIsT0FBTyxDQUFDRCxHQUFHLENBQUNDLElBQVEsSUFBSSxLQUNoQkQsR0FBRyxDQUFDQyxNQUFNLEVBQUUsSUFBSSxLQUNoQkQsR0FBRyxDQUFDQyxNQUFNLEVBQUUsSUFBSSxJQUNoQkQsR0FBRyxDQUFDQyxNQUFNLEVBQUUsTUFBTTtBQUM5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZnRob3IvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvZmxvYXQvaW5kZXguanM/ODBkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuXHJcbm1vZHVsZS5leHBvcnRzID0gZmFjdG9yeShmYWN0b3J5KTtcclxuXHJcbi8qKlxyXG4gKiBSZWFkcyAvIHdyaXRlcyBmbG9hdHMgLyBkb3VibGVzIGZyb20gLyB0byBidWZmZXJzLlxyXG4gKiBAbmFtZSB1dGlsLmZsb2F0XHJcbiAqIEBuYW1lc3BhY2VcclxuICovXHJcblxyXG4vKipcclxuICogV3JpdGVzIGEgMzIgYml0IGZsb2F0IHRvIGEgYnVmZmVyIHVzaW5nIGxpdHRsZSBlbmRpYW4gYnl0ZSBvcmRlci5cclxuICogQG5hbWUgdXRpbC5mbG9hdC53cml0ZUZsb2F0TEVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7bnVtYmVyfSB2YWwgVmFsdWUgdG8gd3JpdGVcclxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWYgVGFyZ2V0IGJ1ZmZlclxyXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zIFRhcmdldCBidWZmZXIgb2Zmc2V0XHJcbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIFdyaXRlcyBhIDMyIGJpdCBmbG9hdCB0byBhIGJ1ZmZlciB1c2luZyBiaWcgZW5kaWFuIGJ5dGUgb3JkZXIuXHJcbiAqIEBuYW1lIHV0aWwuZmxvYXQud3JpdGVGbG9hdEJFXHJcbiAqIEBmdW5jdGlvblxyXG4gKiBAcGFyYW0ge251bWJlcn0gdmFsIFZhbHVlIHRvIHdyaXRlXHJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gYnVmIFRhcmdldCBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBUYXJnZXQgYnVmZmVyIG9mZnNldFxyXG4gKiBAcmV0dXJucyB7dW5kZWZpbmVkfVxyXG4gKi9cclxuXHJcbi8qKlxyXG4gKiBSZWFkcyBhIDMyIGJpdCBmbG9hdCBmcm9tIGEgYnVmZmVyIHVzaW5nIGxpdHRsZSBlbmRpYW4gYnl0ZSBvcmRlci5cclxuICogQG5hbWUgdXRpbC5mbG9hdC5yZWFkRmxvYXRMRVxyXG4gKiBAZnVuY3Rpb25cclxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWYgU291cmNlIGJ1ZmZlclxyXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zIFNvdXJjZSBidWZmZXIgb2Zmc2V0XHJcbiAqIEByZXR1cm5zIHtudW1iZXJ9IFZhbHVlIHJlYWRcclxuICovXHJcblxyXG4vKipcclxuICogUmVhZHMgYSAzMiBiaXQgZmxvYXQgZnJvbSBhIGJ1ZmZlciB1c2luZyBiaWcgZW5kaWFuIGJ5dGUgb3JkZXIuXHJcbiAqIEBuYW1lIHV0aWwuZmxvYXQucmVhZEZsb2F0QkVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gYnVmIFNvdXJjZSBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBTb3VyY2UgYnVmZmVyIG9mZnNldFxyXG4gKiBAcmV0dXJucyB7bnVtYmVyfSBWYWx1ZSByZWFkXHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIFdyaXRlcyBhIDY0IGJpdCBkb3VibGUgdG8gYSBidWZmZXIgdXNpbmcgbGl0dGxlIGVuZGlhbiBieXRlIG9yZGVyLlxyXG4gKiBAbmFtZSB1dGlsLmZsb2F0LndyaXRlRG91YmxlTEVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7bnVtYmVyfSB2YWwgVmFsdWUgdG8gd3JpdGVcclxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWYgVGFyZ2V0IGJ1ZmZlclxyXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zIFRhcmdldCBidWZmZXIgb2Zmc2V0XHJcbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIFdyaXRlcyBhIDY0IGJpdCBkb3VibGUgdG8gYSBidWZmZXIgdXNpbmcgYmlnIGVuZGlhbiBieXRlIG9yZGVyLlxyXG4gKiBAbmFtZSB1dGlsLmZsb2F0LndyaXRlRG91YmxlQkVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7bnVtYmVyfSB2YWwgVmFsdWUgdG8gd3JpdGVcclxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWYgVGFyZ2V0IGJ1ZmZlclxyXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zIFRhcmdldCBidWZmZXIgb2Zmc2V0XHJcbiAqIEByZXR1cm5zIHt1bmRlZmluZWR9XHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIFJlYWRzIGEgNjQgYml0IGRvdWJsZSBmcm9tIGEgYnVmZmVyIHVzaW5nIGxpdHRsZSBlbmRpYW4gYnl0ZSBvcmRlci5cclxuICogQG5hbWUgdXRpbC5mbG9hdC5yZWFkRG91YmxlTEVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gYnVmIFNvdXJjZSBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBTb3VyY2UgYnVmZmVyIG9mZnNldFxyXG4gKiBAcmV0dXJucyB7bnVtYmVyfSBWYWx1ZSByZWFkXHJcbiAqL1xyXG5cclxuLyoqXHJcbiAqIFJlYWRzIGEgNjQgYml0IGRvdWJsZSBmcm9tIGEgYnVmZmVyIHVzaW5nIGJpZyBlbmRpYW4gYnl0ZSBvcmRlci5cclxuICogQG5hbWUgdXRpbC5mbG9hdC5yZWFkRG91YmxlQkVcclxuICogQGZ1bmN0aW9uXHJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gYnVmIFNvdXJjZSBidWZmZXJcclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBTb3VyY2UgYnVmZmVyIG9mZnNldFxyXG4gKiBAcmV0dXJucyB7bnVtYmVyfSBWYWx1ZSByZWFkXHJcbiAqL1xyXG5cclxuLy8gRmFjdG9yeSBmdW5jdGlvbiBmb3IgdGhlIHB1cnBvc2Ugb2Ygbm9kZS1iYXNlZCB0ZXN0aW5nIGluIG1vZGlmaWVkIGdsb2JhbCBlbnZpcm9ubWVudHNcclxuZnVuY3Rpb24gZmFjdG9yeShleHBvcnRzKSB7XHJcblxyXG4gICAgLy8gZmxvYXQ6IHR5cGVkIGFycmF5XHJcbiAgICBpZiAodHlwZW9mIEZsb2F0MzJBcnJheSAhPT0gXCJ1bmRlZmluZWRcIikgKGZ1bmN0aW9uKCkge1xyXG5cclxuICAgICAgICB2YXIgZjMyID0gbmV3IEZsb2F0MzJBcnJheShbIC0wIF0pLFxyXG4gICAgICAgICAgICBmOGIgPSBuZXcgVWludDhBcnJheShmMzIuYnVmZmVyKSxcclxuICAgICAgICAgICAgbGUgID0gZjhiWzNdID09PSAxMjg7XHJcblxyXG4gICAgICAgIGZ1bmN0aW9uIHdyaXRlRmxvYXRfZjMyX2NweSh2YWwsIGJ1ZiwgcG9zKSB7XHJcbiAgICAgICAgICAgIGYzMlswXSA9IHZhbDtcclxuICAgICAgICAgICAgYnVmW3BvcyAgICBdID0gZjhiWzBdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgMV0gPSBmOGJbMV07XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyAyXSA9IGY4YlsyXTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDNdID0gZjhiWzNdO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZnVuY3Rpb24gd3JpdGVGbG9hdF9mMzJfcmV2KHZhbCwgYnVmLCBwb3MpIHtcclxuICAgICAgICAgICAgZjMyWzBdID0gdmFsO1xyXG4gICAgICAgICAgICBidWZbcG9zICAgIF0gPSBmOGJbM107XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyAxXSA9IGY4YlsyXTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDJdID0gZjhiWzFdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgM10gPSBmOGJbMF07XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xyXG4gICAgICAgIGV4cG9ydHMud3JpdGVGbG9hdExFID0gbGUgPyB3cml0ZUZsb2F0X2YzMl9jcHkgOiB3cml0ZUZsb2F0X2YzMl9yZXY7XHJcbiAgICAgICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cclxuICAgICAgICBleHBvcnRzLndyaXRlRmxvYXRCRSA9IGxlID8gd3JpdGVGbG9hdF9mMzJfcmV2IDogd3JpdGVGbG9hdF9mMzJfY3B5O1xyXG5cclxuICAgICAgICBmdW5jdGlvbiByZWFkRmxvYXRfZjMyX2NweShidWYsIHBvcykge1xyXG4gICAgICAgICAgICBmOGJbMF0gPSBidWZbcG9zICAgIF07XHJcbiAgICAgICAgICAgIGY4YlsxXSA9IGJ1Zltwb3MgKyAxXTtcclxuICAgICAgICAgICAgZjhiWzJdID0gYnVmW3BvcyArIDJdO1xyXG4gICAgICAgICAgICBmOGJbM10gPSBidWZbcG9zICsgM107XHJcbiAgICAgICAgICAgIHJldHVybiBmMzJbMF07XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBmdW5jdGlvbiByZWFkRmxvYXRfZjMyX3JldihidWYsIHBvcykge1xyXG4gICAgICAgICAgICBmOGJbM10gPSBidWZbcG9zICAgIF07XHJcbiAgICAgICAgICAgIGY4YlsyXSA9IGJ1Zltwb3MgKyAxXTtcclxuICAgICAgICAgICAgZjhiWzFdID0gYnVmW3BvcyArIDJdO1xyXG4gICAgICAgICAgICBmOGJbMF0gPSBidWZbcG9zICsgM107XHJcbiAgICAgICAgICAgIHJldHVybiBmMzJbMF07XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xyXG4gICAgICAgIGV4cG9ydHMucmVhZEZsb2F0TEUgPSBsZSA/IHJlYWRGbG9hdF9mMzJfY3B5IDogcmVhZEZsb2F0X2YzMl9yZXY7XHJcbiAgICAgICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cclxuICAgICAgICBleHBvcnRzLnJlYWRGbG9hdEJFID0gbGUgPyByZWFkRmxvYXRfZjMyX3JldiA6IHJlYWRGbG9hdF9mMzJfY3B5O1xyXG5cclxuICAgIC8vIGZsb2F0OiBpZWVlNzU0XHJcbiAgICB9KSgpOyBlbHNlIChmdW5jdGlvbigpIHtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gd3JpdGVGbG9hdF9pZWVlNzU0KHdyaXRlVWludCwgdmFsLCBidWYsIHBvcykge1xyXG4gICAgICAgICAgICB2YXIgc2lnbiA9IHZhbCA8IDAgPyAxIDogMDtcclxuICAgICAgICAgICAgaWYgKHNpZ24pXHJcbiAgICAgICAgICAgICAgICB2YWwgPSAtdmFsO1xyXG4gICAgICAgICAgICBpZiAodmFsID09PSAwKVxyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KDEgLyB2YWwgPiAwID8gLyogcG9zaXRpdmUgKi8gMCA6IC8qIG5lZ2F0aXZlIDAgKi8gMjE0NzQ4MzY0OCwgYnVmLCBwb3MpO1xyXG4gICAgICAgICAgICBlbHNlIGlmIChpc05hTih2YWwpKVxyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KDIxNDMyODkzNDQsIGJ1ZiwgcG9zKTtcclxuICAgICAgICAgICAgZWxzZSBpZiAodmFsID4gMy40MDI4MjM0NjYzODUyODg2ZSszOCkgLy8gKy1JbmZpbml0eVxyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KChzaWduIDw8IDMxIHwgMjEzOTA5NTA0MCkgPj4+IDAsIGJ1ZiwgcG9zKTtcclxuICAgICAgICAgICAgZWxzZSBpZiAodmFsIDwgMS4xNzU0OTQzNTA4MjIyODc1ZS0zOCkgLy8gZGVub3JtYWxcclxuICAgICAgICAgICAgICAgIHdyaXRlVWludCgoc2lnbiA8PCAzMSB8IE1hdGgucm91bmQodmFsIC8gMS40MDEyOTg0NjQzMjQ4MTdlLTQ1KSkgPj4+IDAsIGJ1ZiwgcG9zKTtcclxuICAgICAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgICAgICB2YXIgZXhwb25lbnQgPSBNYXRoLmZsb29yKE1hdGgubG9nKHZhbCkgLyBNYXRoLkxOMiksXHJcbiAgICAgICAgICAgICAgICAgICAgbWFudGlzc2EgPSBNYXRoLnJvdW5kKHZhbCAqIE1hdGgucG93KDIsIC1leHBvbmVudCkgKiA4Mzg4NjA4KSAmIDgzODg2MDc7XHJcbiAgICAgICAgICAgICAgICB3cml0ZVVpbnQoKHNpZ24gPDwgMzEgfCBleHBvbmVudCArIDEyNyA8PCAyMyB8IG1hbnRpc3NhKSA+Pj4gMCwgYnVmLCBwb3MpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBleHBvcnRzLndyaXRlRmxvYXRMRSA9IHdyaXRlRmxvYXRfaWVlZTc1NC5iaW5kKG51bGwsIHdyaXRlVWludExFKTtcclxuICAgICAgICBleHBvcnRzLndyaXRlRmxvYXRCRSA9IHdyaXRlRmxvYXRfaWVlZTc1NC5iaW5kKG51bGwsIHdyaXRlVWludEJFKTtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gcmVhZEZsb2F0X2llZWU3NTQocmVhZFVpbnQsIGJ1ZiwgcG9zKSB7XHJcbiAgICAgICAgICAgIHZhciB1aW50ID0gcmVhZFVpbnQoYnVmLCBwb3MpLFxyXG4gICAgICAgICAgICAgICAgc2lnbiA9ICh1aW50ID4+IDMxKSAqIDIgKyAxLFxyXG4gICAgICAgICAgICAgICAgZXhwb25lbnQgPSB1aW50ID4+PiAyMyAmIDI1NSxcclxuICAgICAgICAgICAgICAgIG1hbnRpc3NhID0gdWludCAmIDgzODg2MDc7XHJcbiAgICAgICAgICAgIHJldHVybiBleHBvbmVudCA9PT0gMjU1XHJcbiAgICAgICAgICAgICAgICA/IG1hbnRpc3NhXHJcbiAgICAgICAgICAgICAgICA/IE5hTlxyXG4gICAgICAgICAgICAgICAgOiBzaWduICogSW5maW5pdHlcclxuICAgICAgICAgICAgICAgIDogZXhwb25lbnQgPT09IDAgLy8gZGVub3JtYWxcclxuICAgICAgICAgICAgICAgID8gc2lnbiAqIDEuNDAxMjk4NDY0MzI0ODE3ZS00NSAqIG1hbnRpc3NhXHJcbiAgICAgICAgICAgICAgICA6IHNpZ24gKiBNYXRoLnBvdygyLCBleHBvbmVudCAtIDE1MCkgKiAobWFudGlzc2EgKyA4Mzg4NjA4KTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGV4cG9ydHMucmVhZEZsb2F0TEUgPSByZWFkRmxvYXRfaWVlZTc1NC5iaW5kKG51bGwsIHJlYWRVaW50TEUpO1xyXG4gICAgICAgIGV4cG9ydHMucmVhZEZsb2F0QkUgPSByZWFkRmxvYXRfaWVlZTc1NC5iaW5kKG51bGwsIHJlYWRVaW50QkUpO1xyXG5cclxuICAgIH0pKCk7XHJcblxyXG4gICAgLy8gZG91YmxlOiB0eXBlZCBhcnJheVxyXG4gICAgaWYgKHR5cGVvZiBGbG9hdDY0QXJyYXkgIT09IFwidW5kZWZpbmVkXCIpIChmdW5jdGlvbigpIHtcclxuXHJcbiAgICAgICAgdmFyIGY2NCA9IG5ldyBGbG9hdDY0QXJyYXkoWy0wXSksXHJcbiAgICAgICAgICAgIGY4YiA9IG5ldyBVaW50OEFycmF5KGY2NC5idWZmZXIpLFxyXG4gICAgICAgICAgICBsZSAgPSBmOGJbN10gPT09IDEyODtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gd3JpdGVEb3VibGVfZjY0X2NweSh2YWwsIGJ1ZiwgcG9zKSB7XHJcbiAgICAgICAgICAgIGY2NFswXSA9IHZhbDtcclxuICAgICAgICAgICAgYnVmW3BvcyAgICBdID0gZjhiWzBdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgMV0gPSBmOGJbMV07XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyAyXSA9IGY4YlsyXTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDNdID0gZjhiWzNdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgNF0gPSBmOGJbNF07XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyA1XSA9IGY4Yls1XTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDZdID0gZjhiWzZdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgN10gPSBmOGJbN107XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBmdW5jdGlvbiB3cml0ZURvdWJsZV9mNjRfcmV2KHZhbCwgYnVmLCBwb3MpIHtcclxuICAgICAgICAgICAgZjY0WzBdID0gdmFsO1xyXG4gICAgICAgICAgICBidWZbcG9zICAgIF0gPSBmOGJbN107XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyAxXSA9IGY4Yls2XTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDJdID0gZjhiWzVdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgM10gPSBmOGJbNF07XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyA0XSA9IGY4YlszXTtcclxuICAgICAgICAgICAgYnVmW3BvcyArIDVdID0gZjhiWzJdO1xyXG4gICAgICAgICAgICBidWZbcG9zICsgNl0gPSBmOGJbMV07XHJcbiAgICAgICAgICAgIGJ1Zltwb3MgKyA3XSA9IGY4YlswXTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXHJcbiAgICAgICAgZXhwb3J0cy53cml0ZURvdWJsZUxFID0gbGUgPyB3cml0ZURvdWJsZV9mNjRfY3B5IDogd3JpdGVEb3VibGVfZjY0X3JldjtcclxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xyXG4gICAgICAgIGV4cG9ydHMud3JpdGVEb3VibGVCRSA9IGxlID8gd3JpdGVEb3VibGVfZjY0X3JldiA6IHdyaXRlRG91YmxlX2Y2NF9jcHk7XHJcblxyXG4gICAgICAgIGZ1bmN0aW9uIHJlYWREb3VibGVfZjY0X2NweShidWYsIHBvcykge1xyXG4gICAgICAgICAgICBmOGJbMF0gPSBidWZbcG9zICAgIF07XHJcbiAgICAgICAgICAgIGY4YlsxXSA9IGJ1Zltwb3MgKyAxXTtcclxuICAgICAgICAgICAgZjhiWzJdID0gYnVmW3BvcyArIDJdO1xyXG4gICAgICAgICAgICBmOGJbM10gPSBidWZbcG9zICsgM107XHJcbiAgICAgICAgICAgIGY4Yls0XSA9IGJ1Zltwb3MgKyA0XTtcclxuICAgICAgICAgICAgZjhiWzVdID0gYnVmW3BvcyArIDVdO1xyXG4gICAgICAgICAgICBmOGJbNl0gPSBidWZbcG9zICsgNl07XHJcbiAgICAgICAgICAgIGY4Yls3XSA9IGJ1Zltwb3MgKyA3XTtcclxuICAgICAgICAgICAgcmV0dXJuIGY2NFswXTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGZ1bmN0aW9uIHJlYWREb3VibGVfZjY0X3JldihidWYsIHBvcykge1xyXG4gICAgICAgICAgICBmOGJbN10gPSBidWZbcG9zICAgIF07XHJcbiAgICAgICAgICAgIGY4Yls2XSA9IGJ1Zltwb3MgKyAxXTtcclxuICAgICAgICAgICAgZjhiWzVdID0gYnVmW3BvcyArIDJdO1xyXG4gICAgICAgICAgICBmOGJbNF0gPSBidWZbcG9zICsgM107XHJcbiAgICAgICAgICAgIGY4YlszXSA9IGJ1Zltwb3MgKyA0XTtcclxuICAgICAgICAgICAgZjhiWzJdID0gYnVmW3BvcyArIDVdO1xyXG4gICAgICAgICAgICBmOGJbMV0gPSBidWZbcG9zICsgNl07XHJcbiAgICAgICAgICAgIGY4YlswXSA9IGJ1Zltwb3MgKyA3XTtcclxuICAgICAgICAgICAgcmV0dXJuIGY2NFswXTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXHJcbiAgICAgICAgZXhwb3J0cy5yZWFkRG91YmxlTEUgPSBsZSA/IHJlYWREb3VibGVfZjY0X2NweSA6IHJlYWREb3VibGVfZjY0X3JldjtcclxuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xyXG4gICAgICAgIGV4cG9ydHMucmVhZERvdWJsZUJFID0gbGUgPyByZWFkRG91YmxlX2Y2NF9yZXYgOiByZWFkRG91YmxlX2Y2NF9jcHk7XHJcblxyXG4gICAgLy8gZG91YmxlOiBpZWVlNzU0XHJcbiAgICB9KSgpOyBlbHNlIChmdW5jdGlvbigpIHtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gd3JpdGVEb3VibGVfaWVlZTc1NCh3cml0ZVVpbnQsIG9mZjAsIG9mZjEsIHZhbCwgYnVmLCBwb3MpIHtcclxuICAgICAgICAgICAgdmFyIHNpZ24gPSB2YWwgPCAwID8gMSA6IDA7XHJcbiAgICAgICAgICAgIGlmIChzaWduKVxyXG4gICAgICAgICAgICAgICAgdmFsID0gLXZhbDtcclxuICAgICAgICAgICAgaWYgKHZhbCA9PT0gMCkge1xyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KDAsIGJ1ZiwgcG9zICsgb2ZmMCk7XHJcbiAgICAgICAgICAgICAgICB3cml0ZVVpbnQoMSAvIHZhbCA+IDAgPyAvKiBwb3NpdGl2ZSAqLyAwIDogLyogbmVnYXRpdmUgMCAqLyAyMTQ3NDgzNjQ4LCBidWYsIHBvcyArIG9mZjEpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKGlzTmFOKHZhbCkpIHtcclxuICAgICAgICAgICAgICAgIHdyaXRlVWludCgwLCBidWYsIHBvcyArIG9mZjApO1xyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KDIxNDY5NTkzNjAsIGJ1ZiwgcG9zICsgb2ZmMSk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsID4gMS43OTc2OTMxMzQ4NjIzMTU3ZSszMDgpIHsgLy8gKy1JbmZpbml0eVxyXG4gICAgICAgICAgICAgICAgd3JpdGVVaW50KDAsIGJ1ZiwgcG9zICsgb2ZmMCk7XHJcbiAgICAgICAgICAgICAgICB3cml0ZVVpbnQoKHNpZ24gPDwgMzEgfCAyMTQ2NDM1MDcyKSA+Pj4gMCwgYnVmLCBwb3MgKyBvZmYxKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHZhciBtYW50aXNzYTtcclxuICAgICAgICAgICAgICAgIGlmICh2YWwgPCAyLjIyNTA3Mzg1ODUwNzIwMTRlLTMwOCkgeyAvLyBkZW5vcm1hbFxyXG4gICAgICAgICAgICAgICAgICAgIG1hbnRpc3NhID0gdmFsIC8gNWUtMzI0O1xyXG4gICAgICAgICAgICAgICAgICAgIHdyaXRlVWludChtYW50aXNzYSA+Pj4gMCwgYnVmLCBwb3MgKyBvZmYwKTtcclxuICAgICAgICAgICAgICAgICAgICB3cml0ZVVpbnQoKHNpZ24gPDwgMzEgfCBtYW50aXNzYSAvIDQyOTQ5NjcyOTYpID4+PiAwLCBidWYsIHBvcyArIG9mZjEpO1xyXG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICB2YXIgZXhwb25lbnQgPSBNYXRoLmZsb29yKE1hdGgubG9nKHZhbCkgLyBNYXRoLkxOMik7XHJcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV4cG9uZW50ID09PSAxMDI0KVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBleHBvbmVudCA9IDEwMjM7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFudGlzc2EgPSB2YWwgKiBNYXRoLnBvdygyLCAtZXhwb25lbnQpO1xyXG4gICAgICAgICAgICAgICAgICAgIHdyaXRlVWludChtYW50aXNzYSAqIDQ1MDM1OTk2MjczNzA0OTYgPj4+IDAsIGJ1ZiwgcG9zICsgb2ZmMCk7XHJcbiAgICAgICAgICAgICAgICAgICAgd3JpdGVVaW50KChzaWduIDw8IDMxIHwgZXhwb25lbnQgKyAxMDIzIDw8IDIwIHwgbWFudGlzc2EgKiAxMDQ4NTc2ICYgMTA0ODU3NSkgPj4+IDAsIGJ1ZiwgcG9zICsgb2ZmMSk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGV4cG9ydHMud3JpdGVEb3VibGVMRSA9IHdyaXRlRG91YmxlX2llZWU3NTQuYmluZChudWxsLCB3cml0ZVVpbnRMRSwgMCwgNCk7XHJcbiAgICAgICAgZXhwb3J0cy53cml0ZURvdWJsZUJFID0gd3JpdGVEb3VibGVfaWVlZTc1NC5iaW5kKG51bGwsIHdyaXRlVWludEJFLCA0LCAwKTtcclxuXHJcbiAgICAgICAgZnVuY3Rpb24gcmVhZERvdWJsZV9pZWVlNzU0KHJlYWRVaW50LCBvZmYwLCBvZmYxLCBidWYsIHBvcykge1xyXG4gICAgICAgICAgICB2YXIgbG8gPSByZWFkVWludChidWYsIHBvcyArIG9mZjApLFxyXG4gICAgICAgICAgICAgICAgaGkgPSByZWFkVWludChidWYsIHBvcyArIG9mZjEpO1xyXG4gICAgICAgICAgICB2YXIgc2lnbiA9IChoaSA+PiAzMSkgKiAyICsgMSxcclxuICAgICAgICAgICAgICAgIGV4cG9uZW50ID0gaGkgPj4+IDIwICYgMjA0NyxcclxuICAgICAgICAgICAgICAgIG1hbnRpc3NhID0gNDI5NDk2NzI5NiAqIChoaSAmIDEwNDg1NzUpICsgbG87XHJcbiAgICAgICAgICAgIHJldHVybiBleHBvbmVudCA9PT0gMjA0N1xyXG4gICAgICAgICAgICAgICAgPyBtYW50aXNzYVxyXG4gICAgICAgICAgICAgICAgPyBOYU5cclxuICAgICAgICAgICAgICAgIDogc2lnbiAqIEluZmluaXR5XHJcbiAgICAgICAgICAgICAgICA6IGV4cG9uZW50ID09PSAwIC8vIGRlbm9ybWFsXHJcbiAgICAgICAgICAgICAgICA/IHNpZ24gKiA1ZS0zMjQgKiBtYW50aXNzYVxyXG4gICAgICAgICAgICAgICAgOiBzaWduICogTWF0aC5wb3coMiwgZXhwb25lbnQgLSAxMDc1KSAqIChtYW50aXNzYSArIDQ1MDM1OTk2MjczNzA0OTYpO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZXhwb3J0cy5yZWFkRG91YmxlTEUgPSByZWFkRG91YmxlX2llZWU3NTQuYmluZChudWxsLCByZWFkVWludExFLCAwLCA0KTtcclxuICAgICAgICBleHBvcnRzLnJlYWREb3VibGVCRSA9IHJlYWREb3VibGVfaWVlZTc1NC5iaW5kKG51bGwsIHJlYWRVaW50QkUsIDQsIDApO1xyXG5cclxuICAgIH0pKCk7XHJcblxyXG4gICAgcmV0dXJuIGV4cG9ydHM7XHJcbn1cclxuXHJcbi8vIHVpbnQgaGVscGVyc1xyXG5cclxuZnVuY3Rpb24gd3JpdGVVaW50TEUodmFsLCBidWYsIHBvcykge1xyXG4gICAgYnVmW3BvcyAgICBdID0gIHZhbCAgICAgICAgJiAyNTU7XHJcbiAgICBidWZbcG9zICsgMV0gPSAgdmFsID4+PiA4ICAmIDI1NTtcclxuICAgIGJ1Zltwb3MgKyAyXSA9ICB2YWwgPj4+IDE2ICYgMjU1O1xyXG4gICAgYnVmW3BvcyArIDNdID0gIHZhbCA+Pj4gMjQ7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIHdyaXRlVWludEJFKHZhbCwgYnVmLCBwb3MpIHtcclxuICAgIGJ1Zltwb3MgICAgXSA9ICB2YWwgPj4+IDI0O1xyXG4gICAgYnVmW3BvcyArIDFdID0gIHZhbCA+Pj4gMTYgJiAyNTU7XHJcbiAgICBidWZbcG9zICsgMl0gPSAgdmFsID4+PiA4ICAmIDI1NTtcclxuICAgIGJ1Zltwb3MgKyAzXSA9ICB2YWwgICAgICAgICYgMjU1O1xyXG59XHJcblxyXG5mdW5jdGlvbiByZWFkVWludExFKGJ1ZiwgcG9zKSB7XHJcbiAgICByZXR1cm4gKGJ1Zltwb3MgICAgXVxyXG4gICAgICAgICAgfCBidWZbcG9zICsgMV0gPDwgOFxyXG4gICAgICAgICAgfCBidWZbcG9zICsgMl0gPDwgMTZcclxuICAgICAgICAgIHwgYnVmW3BvcyArIDNdIDw8IDI0KSA+Pj4gMDtcclxufVxyXG5cclxuZnVuY3Rpb24gcmVhZFVpbnRCRShidWYsIHBvcykge1xyXG4gICAgcmV0dXJuIChidWZbcG9zICAgIF0gPDwgMjRcclxuICAgICAgICAgIHwgYnVmW3BvcyArIDFdIDw8IDE2XHJcbiAgICAgICAgICB8IGJ1Zltwb3MgKyAyXSA8PCA4XHJcbiAgICAgICAgICB8IGJ1Zltwb3MgKyAzXSkgPj4+IDA7XHJcbn1cclxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJmYWN0b3J5IiwiRmxvYXQzMkFycmF5IiwiZjMyIiwiZjhiIiwiVWludDhBcnJheSIsImJ1ZmZlciIsImxlIiwid3JpdGVGbG9hdF9mMzJfY3B5IiwidmFsIiwiYnVmIiwicG9zIiwid3JpdGVGbG9hdF9mMzJfcmV2Iiwid3JpdGVGbG9hdExFIiwid3JpdGVGbG9hdEJFIiwicmVhZEZsb2F0X2YzMl9jcHkiLCJyZWFkRmxvYXRfZjMyX3JldiIsInJlYWRGbG9hdExFIiwicmVhZEZsb2F0QkUiLCJ3cml0ZUZsb2F0X2llZWU3NTQiLCJ3cml0ZVVpbnQiLCJzaWduIiwiaXNOYU4iLCJNYXRoIiwicm91bmQiLCJleHBvbmVudCIsImZsb29yIiwibG9nIiwiTE4yIiwibWFudGlzc2EiLCJwb3ciLCJiaW5kIiwid3JpdGVVaW50TEUiLCJ3cml0ZVVpbnRCRSIsInJlYWRGbG9hdF9pZWVlNzU0IiwicmVhZFVpbnQiLCJ1aW50IiwiTmFOIiwiSW5maW5pdHkiLCJyZWFkVWludExFIiwicmVhZFVpbnRCRSIsIkZsb2F0NjRBcnJheSIsImY2NCIsIndyaXRlRG91YmxlX2Y2NF9jcHkiLCJ3cml0ZURvdWJsZV9mNjRfcmV2Iiwid3JpdGVEb3VibGVMRSIsIndyaXRlRG91YmxlQkUiLCJyZWFkRG91YmxlX2Y2NF9jcHkiLCJyZWFkRG91YmxlX2Y2NF9yZXYiLCJyZWFkRG91YmxlTEUiLCJyZWFkRG91YmxlQkUiLCJ3cml0ZURvdWJsZV9pZWVlNzU0Iiwib2ZmMCIsIm9mZjEiLCJyZWFkRG91YmxlX2llZWU3NTQiLCJsbyIsImhpIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/float/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/inquire/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@protobufjs/inquire/index.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\nmodule.exports = inquire;\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */ function inquire(moduleName) {\n    try {\n        var mod = eval(\"quire\".replace(/^/, \"re\"))(moduleName); // eslint-disable-line no-eval\n        if (mod && (mod.length || Object.keys(mod).length)) return mod;\n    } catch (e) {} // eslint-disable-line no-empty\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvaW5xdWlyZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSxPQUFPQyxPQUFPLEdBQUdDO0FBRWpCOzs7OztDQUtDLEdBQ0QsU0FBU0EsUUFBUUMsVUFBVTtJQUN2QixJQUFJO1FBQ0EsSUFBSUMsTUFBTUMsS0FBSyxRQUFRQyxPQUFPLENBQUMsS0FBSSxPQUFPSCxhQUFhLDhCQUE4QjtRQUNyRixJQUFJQyxPQUFRQSxDQUFBQSxJQUFJRyxNQUFNLElBQUlDLE9BQU9DLElBQUksQ0FBQ0wsS0FBS0csTUFBTSxHQUM3QyxPQUFPSDtJQUNmLEVBQUUsT0FBT00sR0FBRyxDQUFDLEVBQUUsK0JBQStCO0lBQzlDLE9BQU87QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZnRob3IvLi9ub2RlX21vZHVsZXMvQHByb3RvYnVmanMvaW5xdWlyZS9pbmRleC5qcz80N2JjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG5tb2R1bGUuZXhwb3J0cyA9IGlucXVpcmU7XHJcblxyXG4vKipcclxuICogUmVxdWlyZXMgYSBtb2R1bGUgb25seSBpZiBhdmFpbGFibGUuXHJcbiAqIEBtZW1iZXJvZiB1dGlsXHJcbiAqIEBwYXJhbSB7c3RyaW5nfSBtb2R1bGVOYW1lIE1vZHVsZSB0byByZXF1aXJlXHJcbiAqIEByZXR1cm5zIHs/T2JqZWN0fSBSZXF1aXJlZCBtb2R1bGUgaWYgYXZhaWxhYmxlIGFuZCBub3QgZW1wdHksIG90aGVyd2lzZSBgbnVsbGBcclxuICovXHJcbmZ1bmN0aW9uIGlucXVpcmUobW9kdWxlTmFtZSkge1xyXG4gICAgdHJ5IHtcclxuICAgICAgICB2YXIgbW9kID0gZXZhbChcInF1aXJlXCIucmVwbGFjZSgvXi8sXCJyZVwiKSkobW9kdWxlTmFtZSk7IC8vIGVzbGludC1kaXNhYmxlLWxpbmUgbm8tZXZhbFxyXG4gICAgICAgIGlmIChtb2QgJiYgKG1vZC5sZW5ndGggfHwgT2JqZWN0LmtleXMobW9kKS5sZW5ndGgpKVxyXG4gICAgICAgICAgICByZXR1cm4gbW9kO1xyXG4gICAgfSBjYXRjaCAoZSkge30gLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1lbXB0eVxyXG4gICAgcmV0dXJuIG51bGw7XHJcbn1cclxuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJpbnF1aXJlIiwibW9kdWxlTmFtZSIsIm1vZCIsImV2YWwiLCJyZXBsYWNlIiwibGVuZ3RoIiwiT2JqZWN0Iiwia2V5cyIsImUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/inquire/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/path/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/path/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */ var path = exports;\nvar isAbsolute = /**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */ path.isAbsolute = function isAbsolute(path) {\n    return /^(?:\\/|\\w+:)/.test(path);\n};\nvar normalize = /**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */ path.normalize = function normalize(path) {\n    path = path.replace(/\\\\/g, \"/\").replace(/\\/{2,}/g, \"/\");\n    var parts = path.split(\"/\"), absolute = isAbsolute(path), prefix = \"\";\n    if (absolute) prefix = parts.shift() + \"/\";\n    for(var i = 0; i < parts.length;){\n        if (parts[i] === \"..\") {\n            if (i > 0 && parts[i - 1] !== \"..\") parts.splice(--i, 2);\n            else if (absolute) parts.splice(i, 1);\n            else ++i;\n        } else if (parts[i] === \".\") parts.splice(i, 1);\n        else ++i;\n    }\n    return prefix + parts.join(\"/\");\n};\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */ path.resolve = function resolve(originPath, includePath, alreadyNormalized) {\n    if (!alreadyNormalized) includePath = normalize(includePath);\n    if (isAbsolute(includePath)) return includePath;\n    if (!alreadyNormalized) originPath = normalize(originPath);\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/path/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/pool/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/pool/index.js ***!
  \************************************************/
/***/ ((module) => {

eval("\nmodule.exports = pool;\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */ /**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */ /**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */ function pool(alloc, slice, size) {\n    var SIZE = size || 8192;\n    var MAX = SIZE >>> 1;\n    var slab = null;\n    var offset = SIZE;\n    return function pool_alloc(size) {\n        if (size < 1 || size > MAX) return alloc(size);\n        if (offset + size > SIZE) {\n            slab = alloc(SIZE);\n            offset = 0;\n        }\n        var buf = slice.call(slab, offset, offset += size);\n        if (offset & 7) offset = (offset | 7) + 1;\n        return buf;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/pool/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@protobufjs/utf8/index.js":
/*!************************************************!*\
  !*** ./node_modules/@protobufjs/utf8/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */ var utf8 = exports;\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */ utf8.length = function utf8_length(string) {\n    var len = 0, c = 0;\n    for(var i = 0; i < string.length; ++i){\n        c = string.charCodeAt(i);\n        if (c < 128) len += 1;\n        else if (c < 2048) len += 2;\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\n            ++i;\n            len += 4;\n        } else len += 3;\n    }\n    return len;\n};\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */ utf8.read = function utf8_read(buffer, start, end) {\n    var len = end - start;\n    if (len < 1) return \"\";\n    var parts = null, chunk = [], i = 0, t; // temporary\n    while(start < end){\n        t = buffer[start++];\n        if (t < 128) chunk[i++] = t;\n        else if (t > 191 && t < 224) chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\n        else if (t > 239 && t < 365) {\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\n            chunk[i++] = 0xD800 + (t >> 10);\n            chunk[i++] = 0xDC00 + (t & 1023);\n        } else chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\n        if (i > 8191) {\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\n            i = 0;\n        }\n    }\n    if (parts) {\n        if (i) parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\n        return parts.join(\"\");\n    }\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\n};\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */ utf8.write = function utf8_write(string, buffer, offset) {\n    var start = offset, c1, c2; // character 2\n    for(var i = 0; i < string.length; ++i){\n        c1 = string.charCodeAt(i);\n        if (c1 < 128) {\n            buffer[offset++] = c1;\n        } else if (c1 < 2048) {\n            buffer[offset++] = c1 >> 6 | 192;\n            buffer[offset++] = c1 & 63 | 128;\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\n            ++i;\n            buffer[offset++] = c1 >> 18 | 240;\n            buffer[offset++] = c1 >> 12 & 63 | 128;\n            buffer[offset++] = c1 >> 6 & 63 | 128;\n            buffer[offset++] = c1 & 63 | 128;\n        } else {\n            buffer[offset++] = c1 >> 12 | 224;\n            buffer[offset++] = c1 >> 6 & 63 | 128;\n            buffer[offset++] = c1 & 63 | 128;\n        }\n    }\n    return offset - start;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@protobufjs/utf8/index.js\n");

/***/ })

};
;