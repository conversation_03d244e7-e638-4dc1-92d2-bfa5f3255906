"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatArea.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/ChatArea.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatArea; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _Upperbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Upperbar */ \"(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\");\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./src/components/dashboard/InputBar.tsx\");\n/* harmony import */ var _DownloadModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./DownloadModal */ \"(app-pages-browser)/./src/components/dashboard/DownloadModal.tsx\");\n/* harmony import */ var _ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ModelSelectionModal */ \"(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\");\n/* harmony import */ var _AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./AttachmentsModal */ \"(app-pages-browser)/./src/components/dashboard/AttachmentsModal.tsx\");\n/* harmony import */ var _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/services/aiService */ \"(app-pages-browser)/./src/lib/services/aiService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatArea(param) {\n    let { currentChat, onChatCreated, onUpdateOpenRouterBalance } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"meta-llama/llama-3.1-8b-instruct:free\");\n    const [actualChatId, setActualChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(currentChat);\n    const [isDownloadModalOpen, setIsDownloadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isModelModalOpen, setIsModelModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessageId, setStreamingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [chatName, setChatName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Nova Conversa\");\n    // Debug: rastrear mudanças no chatName\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"=== DEBUG: CHATNAME MUDOU ===\");\n        console.log(\"Novo chatName:\", chatName);\n        console.log(\"Stack trace:\", new Error().stack);\n    }, [\n        chatName\n    ]);\n    const [isLoadingChat, setIsLoadingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentUsername, setCurrentUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const chatInterfaceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Carregar username quando o usuário estiver disponível\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadUsername = async ()=>{\n            if (user === null || user === void 0 ? void 0 : user.email) {\n                const username = await getUsernameFromFirestore();\n                setCurrentUsername(username);\n            }\n        };\n        loadUsername();\n    }, [\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                const userData = userDoc.data();\n                return userData.username || user.email.split(\"@\")[0];\n            }\n            return user.email.split(\"@\")[0]; // fallback\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return user.email.split(\"@\")[0]; // fallback\n        }\n    };\n    // Função para salvar o último modelo usado\n    const saveLastUsedModel = async (modelId)=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            // Verificar se o documento existe\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                // Atualizar documento existente\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                });\n            } else {\n                // Criar novo documento\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(userRef, {\n                    lastUsedModel: modelId,\n                    lastModelUpdateAt: Date.now()\n                }, {\n                    merge: true\n                });\n            }\n            console.log(\"Last used model saved:\", modelId);\n        } catch (error) {\n            console.error(\"Error saving last used model:\", error);\n        }\n    };\n    // Função para carregar o último modelo usado\n    const loadLastUsedModel = async ()=>{\n        if (!user) return;\n        try {\n            const username = await getUsernameFromFirestore();\n            const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(userRef);\n            if (userDoc.exists()) {\n                const data = userDoc.data();\n                if (data.lastUsedModel) {\n                    setSelectedModel(data.lastUsedModel);\n                    console.log(\"Loaded last used model:\", data.lastUsedModel);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error loading last used model:\", error);\n        }\n    };\n    // Função wrapper para setSelectedModel que também salva no Firestore\n    const handleModelChange = (modelId)=>{\n        setSelectedModel(modelId);\n        saveLastUsedModel(modelId);\n    };\n    // Função para criar um chat automaticamente\n    const createAutoChat = async (firstMessage)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return null;\n        try {\n            // Buscar username do usuário\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (querySnapshot.empty) return null;\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            const username = userData.username;\n            // Gerar ID único para o chat\n            const timestamp = Date.now();\n            const random = Math.random().toString(36).substring(2, 8);\n            const chatId = \"chat_\".concat(timestamp, \"_\").concat(random);\n            const now = new Date().toISOString();\n            // Gerar nome do chat baseado na primeira mensagem (primeiras 3-4 palavras)\n            let finalChatName = \"Nova Conversa\";\n            if (firstMessage.trim().length > 0) {\n                const words = firstMessage.trim().split(\" \");\n                const chatName = words.slice(0, Math.min(4, words.length)).join(\" \");\n                finalChatName = chatName.length > 30 ? chatName.substring(0, 30) + \"...\" : chatName;\n            }\n            // Dados para o Firestore\n            const firestoreData = {\n                context: \"\",\n                createdAt: now,\n                folderId: null,\n                frequencyPenalty: 1.0,\n                isFixed: false,\n                lastUpdatedAt: now,\n                lastUsedModel: selectedModel,\n                latexInstructions: false,\n                maxTokens: 2048,\n                name: finalChatName,\n                password: \"\",\n                repetitionPenalty: 1.0,\n                sessionTime: {\n                    lastSessionStart: now,\n                    lastUpdated: now,\n                    totalTime: 0\n                },\n                systemPrompt: \"\",\n                temperature: 1.0,\n                ultimaMensagem: firstMessage || \"Anexo enviado\",\n                ultimaMensagemEm: now,\n                updatedAt: now\n            };\n            // Criar documento no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId), firestoreData);\n            // Criar arquivo chat.json no Storage\n            const chatJsonData = {\n                id: chatId,\n                name: finalChatName,\n                messages: [],\n                createdAt: now,\n                lastUpdated: now\n            };\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatJsonData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const storageRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(username, \"/conversas/\").concat(chatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(storageRef, chatJsonBlob);\n            console.log(\"Chat criado automaticamente:\", chatId);\n            // Definir o nome do chat imediatamente após criação\n            setChatName(finalChatName);\n            return chatId;\n        } catch (error) {\n            console.error(\"Erro ao criar chat automaticamente:\", error);\n            return null;\n        }\n    };\n    const handleSendMessage = async (attachments, webSearchEnabled)=>{\n        console.log(\"=== DEBUG: CHATAREA HANDLE SEND MESSAGE ===\");\n        console.log(\"Mensagem:\", message);\n        console.log(\"Chat Name atual:\", chatName);\n        console.log(\"Actual Chat ID:\", actualChatId);\n        console.log(\"Current Chat (prop):\", currentChat);\n        console.log(\"Anexos recebidos (novos):\", (attachments === null || attachments === void 0 ? void 0 : attachments.length) || 0);\n        console.log(\"Anexos novos detalhes:\", JSON.stringify(attachments, null, 2));\n        console.log(\"Web Search Enabled:\", webSearchEnabled);\n        // Obter anexos históricos ativos\n        const historicalAttachments = getAllChatAttachments().filter((att)=>att.isActive !== false);\n        // Combinar anexos novos com anexos históricos ativos\n        const allAttachmentsToSend = [\n            ...attachments || [],\n            ...historicalAttachments // Anexos históricos ativos\n        ];\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachmentsToSend.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        console.log(\"\\uD83D\\uDE80 RESULTADO FINAL - ANEXOS QUE SER\\xc3O ENVIADOS PARA A IA:\", uniqueAttachments.length);\n        uniqueAttachments.forEach((att)=>{\n            console.log(\"\\uD83D\\uDE80 Enviando para IA: \".concat(att.filename, \" (ID: \").concat(att.id, \", isActive: \").concat(att.isActive, \")\"));\n        });\n        if (uniqueAttachments.length === 0) {\n            console.log(\"❌ NENHUM ANEXO SER\\xc1 ENVIADO PARA A IA\");\n        }\n        // Debug: verificar mensagens atuais no estado\n        console.log(\"=== DEBUG: MENSAGENS ATUAIS NO ESTADO ===\");\n        console.log(\"Total de mensagens no estado:\", messages.length);\n        const messagesWithAttachments = messages.filter((msg)=>msg.attachments && msg.attachments.length > 0);\n        console.log(\"Mensagens com anexos no estado:\", messagesWithAttachments.length);\n        messagesWithAttachments.forEach((msg, index)=>{\n            var _msg_attachments;\n            console.log(\"Mensagem \".concat(index + 1, \" com anexos:\"), {\n                id: msg.id,\n                sender: msg.sender,\n                attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                attachments: msg.attachments\n            });\n        });\n        if (!message.trim() && (!attachments || attachments.length === 0) || isLoading || isStreaming) {\n            console.log(\"=== DEBUG: CONDI\\xc7\\xc3O DE RETORNO ===\");\n            console.log(\"Mensagem vazia:\", !message.trim());\n            console.log(\"Sem anexos:\", !attachments || attachments.length === 0);\n            console.log(\"Loading:\", isLoading);\n            console.log(\"Streaming:\", isStreaming);\n            return;\n        }\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        const userMessage = {\n            id: _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId(),\n            content: message.trim(),\n            sender: \"user\",\n            timestamp: new Date().toISOString(),\n            attachments: attachments || []\n        };\n        // Se não há chat atual, criar um automaticamente\n        let chatIdToUse = actualChatId;\n        if (!chatIdToUse) {\n            const messageForChat = message.trim() || (attachments && attachments.length > 0 ? \"Anexo enviado\" : \"Nova conversa\");\n            chatIdToUse = await createAutoChat(messageForChat);\n            if (chatIdToUse) {\n                setActualChatId(chatIdToUse);\n                // O nome já foi definido na função createAutoChat, mas vamos garantir carregando também\n                loadChatName(chatIdToUse);\n                onChatCreated === null || onChatCreated === void 0 ? void 0 : onChatCreated(chatIdToUse);\n            }\n        }\n        if (!chatIdToUse) {\n            console.error(\"N\\xe3o foi poss\\xedvel criar ou obter chat ID\");\n            return;\n        }\n        // Adicionar mensagem do usuário\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        const currentMessage = message.trim() || \"\"; // Permitir mensagem vazia se houver anexos\n        setMessage(\"\");\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        // Enviar para a IA (incluindo anexos históricos ativos + anexos novos)\n        await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n            username: username,\n            chatId: chatIdToUse,\n            message: currentMessage,\n            model: selectedModel,\n            attachments: uniqueAttachments,\n            webSearchEnabled: webSearchEnabled\n        }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n        (chunk)=>{\n            setMessages((prev)=>{\n                // Verificar se a mensagem da IA já existe\n                const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                if (existingMessageIndex !== -1) {\n                    // Atualizar mensagem existente\n                    return prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: msg.content + chunk\n                        } : msg);\n                } else {\n                    // Criar nova mensagem da IA na primeira chunk\n                    // Remover o indicador de loading assim que a primeira chunk chegar\n                    setIsLoading(false);\n                    const aiMessage = {\n                        id: aiMessageId,\n                        content: chunk,\n                        sender: \"ai\",\n                        timestamp: new Date().toISOString(),\n                        hasWebSearch: webSearchEnabled\n                    };\n                    return [\n                        ...prev,\n                        aiMessage\n                    ];\n                }\n            });\n        }, // onComplete - finalizar streaming\n        (fullResponse)=>{\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: fullResponse\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            // Atualizar saldo do OpenRouter após a resposta\n            if (onUpdateOpenRouterBalance) {\n                onUpdateOpenRouterBalance();\n            }\n        }, // onError - tratar erros\n        (error)=>{\n            console.error(\"Erro na IA:\", error);\n            setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                        ...msg,\n                        content: \"❌ Erro: \".concat(error)\n                    } : msg));\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n        });\n    };\n    // Função para cancelar streaming\n    const handleCancelStreaming = ()=>{\n        _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].cancelRequest();\n        setIsLoading(false);\n        setIsStreaming(false);\n        setStreamingMessageId(null);\n    };\n    // Função para carregar o nome do chat do Firestore\n    const loadChatName = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        try {\n            console.log(\"=== DEBUG: CARREGANDO NOME DO CHAT ===\");\n            console.log(\"Chat ID:\", chatId);\n            const username = await getUsernameFromFirestore();\n            console.log(\"Username:\", username);\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", username, \"conversas\", chatId));\n            if (chatDoc.exists()) {\n                const chatData = chatDoc.data();\n                const chatName = chatData.name || \"Conversa sem nome\";\n                console.log(\"Nome do chat carregado:\", chatName);\n                setChatName(chatName);\n            } else {\n                console.log(\"Chat n\\xe3o encontrado no Firestore\");\n                setChatName(\"Conversa n\\xe3o encontrada\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar nome do chat:\", error);\n            setChatName(\"Erro ao carregar nome\");\n        }\n    };\n    // Função para carregar mensagens existentes do chat\n    const loadChatMessages = async (chatId)=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return;\n        setIsLoadingChat(true);\n        try {\n            const username = await getUsernameFromFirestore();\n            const chatMessages = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].loadChatMessages(username, chatId);\n            // Debug: verificar mensagens carregadas\n            console.log(\"=== DEBUG: MENSAGENS CARREGADAS DO STORAGE ===\");\n            console.log(\"Total de mensagens:\", chatMessages.length);\n            chatMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    role: msg.role,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            const convertedMessages = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].convertFromAIFormat(chatMessages);\n            // Debug: verificar mensagens convertidas\n            console.log(\"=== DEBUG: MENSAGENS CONVERTIDAS ===\");\n            console.log(\"Total de mensagens convertidas:\", convertedMessages.length);\n            convertedMessages.forEach((msg, index)=>{\n                var _msg_attachments;\n                console.log(\"Mensagem convertida \".concat(index + 1, \":\"), {\n                    id: msg.id,\n                    sender: msg.sender,\n                    hasAttachments: !!(msg.attachments && msg.attachments.length > 0),\n                    attachmentsCount: ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) || 0,\n                    attachments: msg.attachments\n                });\n            });\n            setMessages(convertedMessages);\n        } catch (error) {\n            console.error(\"Erro ao carregar mensagens do chat:\", error);\n            setMessages([]);\n        } finally{\n            setIsLoadingChat(false);\n        }\n    };\n    // Carregar último modelo usado quando o componente montar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            loadLastUsedModel();\n        }\n    }, [\n        user\n    ]);\n    // Carregar mensagens quando o chat atual mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"=== DEBUG: USEEFFECT CURRENTCHAT MUDOU ===\");\n        console.log(\"currentChat:\", currentChat);\n        console.log(\"actualChatId:\", actualChatId);\n        if (currentChat && currentChat !== actualChatId) {\n            console.log(\"Carregando chat diferente:\", currentChat);\n            setActualChatId(currentChat);\n            setIsLoadingChat(true);\n            // Limpar mensagens imediatamente para mostrar o estado de carregamento\n            setMessages([]);\n            loadChatMessages(currentChat);\n            loadChatName(currentChat);\n        } else if (!currentChat) {\n            console.log(\"Nenhum chat selecionado, resetando para Nova Conversa\");\n            setActualChatId(null);\n            setMessages([]);\n            setChatName(\"Nova Conversa\");\n            setIsLoadingChat(false);\n        } else {\n            console.log(\"Chat atual n\\xe3o mudou, mantendo estado\");\n        }\n    }, [\n        currentChat,\n        user === null || user === void 0 ? void 0 : user.email\n    ]);\n    // Funções para manipular mensagens\n    const handleDeleteMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Remover visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, messageId);\n            if (!success) {\n                // Se falhou, restaurar a mensagem\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao deletar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar a mensagem\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao deletar mensagem:\", error);\n        }\n    };\n    const handleRegenerateMessage = async (messageId)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        const messageIndex = messages.findIndex((msg)=>msg.id === messageId);\n        if (messageIndex === -1) return;\n        const messageToRegenerate = messages[messageIndex];\n        // Remover apenas as mensagens APÓS a mensagem selecionada (não incluindo ela)\n        const messagesBeforeRegeneration = messages.slice(0, messageIndex + 1);\n        setMessages(messagesBeforeRegeneration);\n        // Preparar o conteúdo da mensagem para regenerar\n        setMessage(messageToRegenerate.content);\n        setIsLoading(true);\n        setIsStreaming(true);\n        // Preparar ID para a nova mensagem da IA que será criada durante o streaming\n        const aiMessageId = _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].generateMessageId();\n        setStreamingMessageId(aiMessageId);\n        // Buscar username correto do usuário\n        const username = await getUsernameFromFirestore();\n        try {\n            // Deletar apenas as mensagens POSTERIORES do Firebase Storage (não a mensagem atual)\n            for(let i = messageIndex + 1; i < messages.length; i++){\n                const msgToDelete = messages[i];\n                await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].deleteMessage(username, actualChatId, msgToDelete.id);\n            }\n            // Enviar para a IA para regenerar\n            await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].sendMessageSafe({\n                username: username,\n                chatId: actualChatId,\n                message: messageToRegenerate.content,\n                model: selectedModel,\n                isRegeneration: true\n            }, // onChunk - criar mensagem na primeira chunk e atualizar com cada chunk\n            (chunk)=>{\n                setMessages((prev)=>{\n                    // Verificar se a mensagem da IA já existe\n                    const existingMessageIndex = prev.findIndex((msg)=>msg.id === aiMessageId);\n                    if (existingMessageIndex !== -1) {\n                        // Atualizar mensagem existente\n                        return prev.map((msg)=>msg.id === aiMessageId ? {\n                                ...msg,\n                                content: msg.content + chunk\n                            } : msg);\n                    } else {\n                        // Criar nova mensagem da IA na primeira chunk\n                        // Remover o indicador de loading assim que a primeira chunk chegar\n                        setIsLoading(false);\n                        const aiMessage = {\n                            id: aiMessageId,\n                            content: chunk,\n                            sender: \"ai\",\n                            timestamp: new Date().toISOString(),\n                            hasWebSearch: false\n                        };\n                        return [\n                            ...prev,\n                            aiMessage\n                        ];\n                    }\n                });\n            }, // onComplete - finalizar streaming\n            (fullResponse)=>{\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: fullResponse\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n                // Atualizar saldo do OpenRouter após a regeneração\n                if (onUpdateOpenRouterBalance) {\n                    onUpdateOpenRouterBalance();\n                }\n            }, // onError - tratar erros\n            (error)=>{\n                console.error(\"Erro na regenera\\xe7\\xe3o:\", error);\n                setMessages((prev)=>prev.map((msg)=>msg.id === aiMessageId ? {\n                            ...msg,\n                            content: \"❌ Erro na regenera\\xe7\\xe3o: \".concat(error)\n                        } : msg));\n                setIsLoading(false);\n                setIsStreaming(false);\n                setStreamingMessageId(null);\n                setMessage(\"\"); // Limpar o campo de input\n            });\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n            setIsLoading(false);\n            setIsStreaming(false);\n            setStreamingMessageId(null);\n            setMessage(\"\"); // Limpar o campo de input\n            // Recarregar mensagens em caso de erro\n            loadChatMessages(actualChatId);\n        }\n    };\n    const handleEditMessage = async (messageId, newContent)=>{\n        if (!actualChatId || !(user === null || user === void 0 ? void 0 : user.email)) return;\n        // Atualizar visualmente primeiro para melhor UX\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n        try {\n            const username = await getUsernameFromFirestore();\n            const success = await _lib_services_aiService__WEBPACK_IMPORTED_MODULE_12__[\"default\"].updateMessage(username, actualChatId, messageId, newContent);\n            if (!success) {\n                // Se falhou, restaurar o conteúdo original\n                loadChatMessages(actualChatId);\n                console.error(\"Falha ao atualizar mensagem no servidor\");\n            }\n        } catch (error) {\n            // Se falhou, restaurar o conteúdo original\n            loadChatMessages(actualChatId);\n            console.error(\"Erro ao atualizar mensagem:\", error);\n        }\n    };\n    const handleCopyMessage = (content)=>{\n        navigator.clipboard.writeText(content).then(()=>{\n            console.log(\"Mensagem copiada para a \\xe1rea de transfer\\xeancia\");\n        });\n    };\n    // Funções de navegação\n    const handleScrollToTop = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    const handleScrollToBottom = ()=>{\n        var _chatInterfaceRef_current;\n        (_chatInterfaceRef_current = chatInterfaceRef.current) === null || _chatInterfaceRef_current === void 0 ? void 0 : _chatInterfaceRef_current.scrollTo({\n            top: chatInterfaceRef.current.scrollHeight,\n            behavior: \"smooth\"\n        });\n    };\n    // Função para converter mensagens para o formato ChatMessage\n    const convertToChatMessages = (messages)=>{\n        return messages.map((msg)=>({\n                id: msg.id,\n                content: msg.content,\n                role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                timestamp: new Date(msg.timestamp).getTime(),\n                isFavorite: msg.isFavorite || false,\n                attachments: msg.attachments || []\n            }));\n    };\n    // Função para abrir o modal de download\n    const handleDownloadModal = ()=>{\n        setIsDownloadModalOpen(true);\n    };\n    // Função para abrir o modal de anexos\n    const handleAttachmentsModal = ()=>{\n        setIsAttachmentsModalOpen(true);\n    };\n    // Função para obter todos os anexos do chat\n    const getAllChatAttachments = ()=>{\n        const allAttachments = [];\n        messages.forEach((message)=>{\n            if (message.attachments && message.attachments.length > 0) {\n                allAttachments.push(...message.attachments);\n            }\n        });\n        // Remover duplicatas baseado no ID\n        const uniqueAttachments = allAttachments.filter((attachment, index, self)=>index === self.findIndex((a)=>a.id === attachment.id));\n        return uniqueAttachments;\n    };\n    // Função para salvar o estado dos anexos no Firebase Storage\n    const saveAttachmentStates = async (updatedMessages)=>{\n        if (!currentUsername || !actualChatId) return;\n        try {\n            // Preparar dados do chat para salvar\n            const chatData = {\n                id: actualChatId,\n                name: chatName || \"Chat\",\n                messages: updatedMessages.map((msg)=>({\n                        id: msg.id,\n                        content: msg.content,\n                        role: msg.sender === \"user\" ? \"user\" : \"assistant\",\n                        timestamp: msg.timestamp,\n                        isFavorite: msg.isFavorite,\n                        attachments: msg.attachments\n                    })),\n                createdAt: new Date().toISOString(),\n                lastUpdated: new Date().toISOString()\n            };\n            // Salvar no Firebase Storage\n            const chatJsonBlob = new Blob([\n                JSON.stringify(chatData, null, 2)\n            ], {\n                type: \"application/json\"\n            });\n            const chatJsonRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(currentUsername, \"/conversas/\").concat(actualChatId, \"/chat.json\"));\n            await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.uploadBytes)(chatJsonRef, chatJsonBlob);\n            console.log(\"✅ Estado dos anexos salvo no Firebase Storage\");\n            console.log(\"\\uD83D\\uDCC1 Dados salvos:\", {\n                chatId: actualChatId,\n                totalMessages: chatData.messages.length,\n                messagesWithAttachments: chatData.messages.filter((msg)=>{\n                    var _msg_attachments;\n                    return ((_msg_attachments = msg.attachments) === null || _msg_attachments === void 0 ? void 0 : _msg_attachments.length) > 0;\n                }).length\n            });\n        } catch (error) {\n            console.error(\"❌ Erro ao salvar estado dos anexos:\", error);\n        }\n    };\n    // Função para alternar o estado ativo de um anexo\n    const handleToggleAttachment = (attachmentId)=>{\n        setMessages((prevMessages)=>{\n            const updatedMessages = prevMessages.map((message)=>{\n                if (message.attachments && message.attachments.length > 0) {\n                    const updatedAttachments = message.attachments.map((attachment)=>{\n                        if (attachment.id === attachmentId) {\n                            // Se isActive não está definido, considerar como true (ativo por padrão)\n                            const currentState = attachment.isActive !== false;\n                            return {\n                                ...attachment,\n                                isActive: !currentState\n                            };\n                        }\n                        return attachment;\n                    });\n                    return {\n                        ...message,\n                        attachments: updatedAttachments\n                    };\n                }\n                return message;\n            });\n            // Salvar o estado atualizado no Firebase Storage\n            saveAttachmentStates(updatedMessages);\n            return updatedMessages;\n        });\n    };\n    // Função para filtrar anexos ativos para envio à IA\n    const getActiveAttachments = (attachments)=>{\n        if (!attachments) return [];\n        // Para anexos novos (sem isActive definido), incluir por padrão\n        // Para anexos existentes, verificar se isActive não é false\n        return attachments.filter((attachment)=>{\n            // Se isActive não está definido (anexo novo), incluir\n            if (attachment.isActive === undefined) return true;\n            // Se isActive está definido, incluir apenas se não for false\n            return attachment.isActive !== false;\n        });\n    };\n    // Sincronizar estado quando currentChat mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Limpar mensagens quando mudar para um chat diferente ou para área inicial\n        if (currentChat !== actualChatId) {\n            setMessages([]);\n        }\n        setActualChatId(currentChat);\n    }, [\n        currentChat\n    ]);\n    // Função para obter IDs dos anexos ativos\n    const getActiveAttachmentIds = ()=>{\n        const allAttachments = getAllChatAttachments();\n        return allAttachments.filter((att)=>att.isActive !== false).map((att)=>att.id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Upperbar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                currentChat: currentChat,\n                chatName: chatName,\n                aiModel: selectedModel,\n                onDownload: handleDownloadModal,\n                onAttachments: handleAttachmentsModal,\n                isLoading: isLoading,\n                attachmentsCount: getAllChatAttachments().length,\n                aiMetadata: {\n                    usedCoT: false\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 847,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: chatInterfaceRef,\n                className: \"flex-1 min-h-0 overflow-hidden\",\n                style: {\n                    height: \"calc(100vh - 200px)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    isLoading: isLoading,\n                    isLoadingChat: isLoadingChat,\n                    onDeleteMessage: handleDeleteMessage,\n                    onRegenerateMessage: handleRegenerateMessage,\n                    onEditMessage: handleEditMessage,\n                    onCopyMessage: handleCopyMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                    lineNumber: 862,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 861,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                message: message,\n                setMessage: setMessage,\n                onSendMessage: handleSendMessage,\n                isLoading: isLoading,\n                selectedModel: selectedModel,\n                onModelChange: handleModelChange,\n                onScrollToTop: handleScrollToTop,\n                onScrollToBottom: handleScrollToBottom,\n                isStreaming: isStreaming,\n                onCancelStreaming: handleCancelStreaming,\n                onOpenModelModal: ()=>setIsModelModalOpen(true),\n                username: currentUsername,\n                chatId: actualChatId || undefined,\n                activeAttachmentsCount: getActiveAttachmentIds().length\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 874,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DownloadModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isDownloadModalOpen,\n                onClose: ()=>setIsDownloadModalOpen(false),\n                messages: convertToChatMessages(messages),\n                chatName: chatName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 892,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModelSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isModelModalOpen,\n                onClose: ()=>setIsModelModalOpen(false),\n                currentModel: selectedModel,\n                onModelSelect: handleModelChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 900,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentsModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAttachmentsModalOpen,\n                onClose: ()=>setIsAttachmentsModalOpen(false),\n                attachments: getAllChatAttachments(),\n                activeAttachments: getActiveAttachmentIds(),\n                onToggleAttachment: handleToggleAttachment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n                lineNumber: 908,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatArea.tsx\",\n        lineNumber: 845,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatArea, \"alH06hEVQYW563u408yVzAENR+0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ChatArea;\nvar _c;\n$RefreshReg$(_c, \"ChatArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9DaGF0QXJlYS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFb0Q7QUFDNEI7QUFDNUI7QUFDUDtBQUNJO0FBQ2Y7QUFDVTtBQUNWO0FBRVU7QUFDWTtBQUNOO0FBRUQ7QUE0QmxDLFNBQVNtQixTQUFTLEtBQXdFO1FBQXhFLEVBQUVDLFdBQVcsRUFBRUMsYUFBYSxFQUFFQyx5QkFBeUIsRUFBaUIsR0FBeEU7O0lBQy9CLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdaLDhEQUFPQTtJQUN4QixNQUFNLENBQUNhLFNBQVNDLFdBQVcsR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzBCLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRCLFVBQVVDLFlBQVksR0FBRzdCLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDOEIsZUFBZUMsaUJBQWlCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUVuRCxNQUFNLENBQUNnQyxjQUFjQyxnQkFBZ0IsR0FBR2pDLCtDQUFRQSxDQUFnQm9CO0lBQ2hFLE1BQU0sQ0FBQ2MscUJBQXFCQyx1QkFBdUIsR0FBR25DLCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ29DLGtCQUFrQkMsb0JBQW9CLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNzQyx3QkFBd0JDLDBCQUEwQixHQUFHdkMsK0NBQVFBLENBQUM7SUFDckUsTUFBTSxDQUFDd0MsYUFBYUMsZUFBZSxHQUFHekMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEMsb0JBQW9CQyxzQkFBc0IsR0FBRzNDLCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUM0QyxVQUFVQyxZQUFZLEdBQUc3QywrQ0FBUUEsQ0FBUztJQUVqRCx1Q0FBdUM7SUFDdkNDLGdEQUFTQSxDQUFDO1FBQ1I2QyxRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQkg7UUFDOUJFLFFBQVFDLEdBQUcsQ0FBQyxnQkFBZ0IsSUFBSUMsUUFBUUMsS0FBSztJQUMvQyxHQUFHO1FBQUNMO0tBQVM7SUFDYixNQUFNLENBQUNNLGVBQWVDLGlCQUFpQixHQUFHbkQsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDb0QsaUJBQWlCQyxtQkFBbUIsR0FBR3JELCtDQUFRQSxDQUFxQnNEO0lBQzNFLE1BQU1DLG1CQUFtQnJELDZDQUFNQSxDQUFpQjtJQUVoRCx3REFBd0Q7SUFDeERELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXVELGVBQWU7WUFDbkIsSUFBSWpDLGlCQUFBQSwyQkFBQUEsS0FBTWtDLEtBQUssRUFBRTtnQkFDZixNQUFNQyxXQUFXLE1BQU1DO2dCQUN2Qk4sbUJBQW1CSztZQUNyQjtRQUNGO1FBQ0FGO0lBQ0YsR0FBRztRQUFDakMsaUJBQUFBLDJCQUFBQSxLQUFNa0MsS0FBSztLQUFDO0lBRWhCLGlEQUFpRDtJQUNqRCxNQUFNRSwyQkFBMkI7UUFDL0IsSUFBSSxFQUFDcEMsaUJBQUFBLDJCQUFBQSxLQUFNa0MsS0FBSyxHQUFFLE9BQU87UUFFekIsSUFBSTtZQUNGLE1BQU0sRUFBRUcsVUFBVSxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFLEdBQUcsTUFBTSw2TEFBTztZQUMzRCxNQUFNQyxjQUFjSixXQUFXbkQsNkNBQUVBLEVBQUU7WUFDbkMsTUFBTXdELElBQUlKLE1BQU1HLGFBQWFGLE1BQU0sU0FBUyxNQUFNdkMsS0FBS2tDLEtBQUs7WUFDNUQsTUFBTVMsZ0JBQWdCLE1BQU1ILFFBQVFFO1lBRXBDLElBQUksQ0FBQ0MsY0FBY0MsS0FBSyxFQUFFO2dCQUN4QixNQUFNQyxVQUFVRixjQUFjRyxJQUFJLENBQUMsRUFBRTtnQkFDckMsTUFBTUMsV0FBV0YsUUFBUUcsSUFBSTtnQkFDN0IsT0FBT0QsU0FBU1osUUFBUSxJQUFJbkMsS0FBS2tDLEtBQUssQ0FBQ2UsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1lBQ3REO1lBRUEsT0FBT2pELEtBQUtrQyxLQUFLLENBQUNlLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLFdBQVc7UUFDOUMsRUFBRSxPQUFPQyxPQUFPO1lBQ2QzQixRQUFRMkIsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsT0FBT2xELEtBQUtrQyxLQUFLLENBQUNlLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFFLFdBQVc7UUFDOUM7SUFDRjtJQUVBLDJDQUEyQztJQUMzQyxNQUFNRSxvQkFBb0IsT0FBT0M7UUFDL0IsSUFBSSxDQUFDcEQsTUFBTTtRQUVYLElBQUk7WUFDRixNQUFNbUMsV0FBVyxNQUFNQztZQUN2QixNQUFNaUIsVUFBVXpFLHVEQUFHQSxDQUFDTSw2Q0FBRUEsRUFBRSxZQUFZaUQsVUFBVSxpQkFBaUI7WUFFL0Qsa0NBQWtDO1lBQ2xDLE1BQU1VLFVBQVUsTUFBTS9ELDBEQUFNQSxDQUFDdUU7WUFFN0IsSUFBSVIsUUFBUVMsTUFBTSxJQUFJO2dCQUNwQixnQ0FBZ0M7Z0JBQ2hDLE1BQU12RSw2REFBU0EsQ0FBQ3NFLFNBQVM7b0JBQ3ZCRSxlQUFlSDtvQkFDZkksbUJBQW1CQyxLQUFLQyxHQUFHO2dCQUM3QjtZQUNGLE9BQU87Z0JBQ0wsdUJBQXVCO2dCQUN2QixNQUFNN0UsMERBQU1BLENBQUN3RSxTQUFTO29CQUNwQkUsZUFBZUg7b0JBQ2ZJLG1CQUFtQkMsS0FBS0MsR0FBRztnQkFDN0IsR0FBRztvQkFBRUMsT0FBTztnQkFBSztZQUNuQjtZQUVBcEMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQjRCO1FBQ3hDLEVBQUUsT0FBT0YsT0FBTztZQUNkM0IsUUFBUTJCLEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pEO0lBQ0Y7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTVUsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQzVELE1BQU07UUFFWCxJQUFJO1lBQ0YsTUFBTW1DLFdBQVcsTUFBTUM7WUFDdkIsTUFBTWlCLFVBQVV6RSx1REFBR0EsQ0FBQ00sNkNBQUVBLEVBQUUsWUFBWWlELFVBQVUsaUJBQWlCO1lBQy9ELE1BQU1VLFVBQVUsTUFBTS9ELDBEQUFNQSxDQUFDdUU7WUFFN0IsSUFBSVIsUUFBUVMsTUFBTSxJQUFJO2dCQUNwQixNQUFNTixPQUFPSCxRQUFRRyxJQUFJO2dCQUN6QixJQUFJQSxLQUFLTyxhQUFhLEVBQUU7b0JBQ3RCL0MsaUJBQWlCd0MsS0FBS08sYUFBYTtvQkFDbkNoQyxRQUFRQyxHQUFHLENBQUMsMkJBQTJCd0IsS0FBS08sYUFBYTtnQkFDM0Q7WUFDRjtRQUNGLEVBQUUsT0FBT0wsT0FBTztZQUNkM0IsUUFBUTJCLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2xEO0lBQ0Y7SUFFQSxxRUFBcUU7SUFDckUsTUFBTVcsb0JBQW9CLENBQUNUO1FBQ3pCNUMsaUJBQWlCNEM7UUFDakJELGtCQUFrQkM7SUFDcEI7SUFFQSw0Q0FBNEM7SUFDNUMsTUFBTVUsaUJBQWlCLE9BQU9DO1FBQzVCLElBQUksRUFBQy9ELGlCQUFBQSwyQkFBQUEsS0FBTWtDLEtBQUssR0FBRSxPQUFPO1FBRXpCLElBQUk7WUFDRiw2QkFBNkI7WUFDN0IsTUFBTSxFQUFFRyxVQUFVLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FBRyxNQUFNLDZMQUFPO1lBQzNELE1BQU1DLGNBQWNKLFdBQVduRCw2Q0FBRUEsRUFBRTtZQUNuQyxNQUFNd0QsSUFBSUosTUFBTUcsYUFBYUYsTUFBTSxTQUFTLE1BQU12QyxLQUFLa0MsS0FBSztZQUM1RCxNQUFNUyxnQkFBZ0IsTUFBTUgsUUFBUUU7WUFFcEMsSUFBSUMsY0FBY0MsS0FBSyxFQUFFLE9BQU87WUFFaEMsTUFBTUMsVUFBVUYsY0FBY0csSUFBSSxDQUFDLEVBQUU7WUFDckMsTUFBTUMsV0FBV0YsUUFBUUcsSUFBSTtZQUM3QixNQUFNYixXQUFXWSxTQUFTWixRQUFRO1lBRWxDLDZCQUE2QjtZQUM3QixNQUFNNkIsWUFBWVAsS0FBS0MsR0FBRztZQUMxQixNQUFNTyxTQUFTQyxLQUFLRCxNQUFNLEdBQUdFLFFBQVEsQ0FBQyxJQUFJQyxTQUFTLENBQUMsR0FBRztZQUN2RCxNQUFNQyxTQUFTLFFBQXFCSixPQUFiRCxXQUFVLEtBQVUsT0FBUEM7WUFDcEMsTUFBTVAsTUFBTSxJQUFJRCxPQUFPYSxXQUFXO1lBRWxDLDJFQUEyRTtZQUMzRSxJQUFJQyxnQkFBZ0I7WUFDcEIsSUFBSVIsYUFBYVMsSUFBSSxHQUFHQyxNQUFNLEdBQUcsR0FBRztnQkFDbEMsTUFBTUMsUUFBUVgsYUFBYVMsSUFBSSxHQUFHdkIsS0FBSyxDQUFDO2dCQUN4QyxNQUFNNUIsV0FBV3FELE1BQU1DLEtBQUssQ0FBQyxHQUFHVCxLQUFLVSxHQUFHLENBQUMsR0FBR0YsTUFBTUQsTUFBTSxHQUFHSSxJQUFJLENBQUM7Z0JBQ2hFTixnQkFBZ0JsRCxTQUFTb0QsTUFBTSxHQUFHLEtBQUtwRCxTQUFTK0MsU0FBUyxDQUFDLEdBQUcsTUFBTSxRQUFRL0M7WUFDN0U7WUFFQSx5QkFBeUI7WUFDekIsTUFBTXlELGdCQUFnQjtnQkFDcEJDLFNBQVM7Z0JBQ1RDLFdBQVd0QjtnQkFDWHVCLFVBQVU7Z0JBQ1ZDLGtCQUFrQjtnQkFDbEJDLFNBQVM7Z0JBQ1RDLGVBQWUxQjtnQkFDZkgsZUFBZWhEO2dCQUNmOEUsbUJBQW1CO2dCQUNuQkMsV0FBVztnQkFDWEMsTUFBTWhCO2dCQUNOaUIsVUFBVTtnQkFDVkMsbUJBQW1CO2dCQUNuQkMsYUFBYTtvQkFDWEMsa0JBQWtCakM7b0JBQ2xCa0MsYUFBYWxDO29CQUNibUMsV0FBVztnQkFDYjtnQkFDQUMsY0FBYztnQkFDZEMsYUFBYTtnQkFDYkMsZ0JBQWdCakMsZ0JBQWdCO2dCQUNoQ2tDLGtCQUFrQnZDO2dCQUNsQndDLFdBQVd4QztZQUNiO1lBRUEsK0JBQStCO1lBQy9CLE1BQU03RSwwREFBTUEsQ0FBQ0QsdURBQUdBLENBQUNNLDZDQUFFQSxFQUFFLFlBQVlpRCxVQUFVLGFBQWFrQyxTQUFTUztZQUVqRSxxQ0FBcUM7WUFDckMsTUFBTXFCLGVBQWU7Z0JBQ25CQyxJQUFJL0I7Z0JBQ0prQixNQUFNaEI7Z0JBQ05sRSxVQUFVLEVBQUU7Z0JBQ1oyRSxXQUFXdEI7Z0JBQ1hrQyxhQUFhbEM7WUFDZjtZQUVBLE1BQU0yQyxlQUFlLElBQUlDLEtBQUs7Z0JBQUNDLEtBQUtDLFNBQVMsQ0FBQ0wsY0FBYyxNQUFNO2FBQUcsRUFBRTtnQkFDckVNLE1BQU07WUFDUjtZQUVBLE1BQU1DLGFBQWExSCxxREFBR0EsQ0FBQ0csa0RBQU9BLEVBQUUsWUFBa0NrRixPQUF0QmxDLFVBQVMsZUFBb0IsT0FBUGtDLFFBQU87WUFDekUsTUFBTXBGLDZEQUFXQSxDQUFDeUgsWUFBWUw7WUFFOUI5RSxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDNkM7WUFDNUMsb0RBQW9EO1lBQ3BEL0MsWUFBWWlEO1lBQ1osT0FBT0Y7UUFFVCxFQUFFLE9BQU9uQixPQUFPO1lBQ2QzQixRQUFRMkIsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckQsT0FBTztRQUNUO0lBQ0Y7SUFFQSxNQUFNeUQsb0JBQW9CLE9BQU9DLGFBQStEQztRQUM5RnRGLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsYUFBYXZCO1FBQ3pCc0IsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQkg7UUFDaENFLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJmO1FBQy9CYyxRQUFRQyxHQUFHLENBQUMsd0JBQXdCM0I7UUFDcEMwQixRQUFRQyxHQUFHLENBQUMsNkJBQTZCb0YsQ0FBQUEsd0JBQUFBLGtDQUFBQSxZQUFhbkMsTUFBTSxLQUFJO1FBQ2hFbEQsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQitFLEtBQUtDLFNBQVMsQ0FBQ0ksYUFBYSxNQUFNO1FBQ3hFckYsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QnFGO1FBRW5DLGlDQUFpQztRQUNqQyxNQUFNQyx3QkFBd0JDLHdCQUF3QkMsTUFBTSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxRQUFRLEtBQUs7UUFFckYscURBQXFEO1FBQ3JELE1BQU1DLHVCQUF1QjtlQUN2QlAsZUFBZSxFQUFFO2VBQ2xCRSxzQkFBc0IsMkJBQTJCO1NBQ3JEO1FBRUQsbUNBQW1DO1FBQ25DLE1BQU1NLG9CQUFvQkQscUJBQXFCSCxNQUFNLENBQUMsQ0FBQ0ssWUFBWUMsT0FBT0MsT0FDeEVELFVBQVVDLEtBQUtDLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJCLEVBQUUsS0FBS2lCLFdBQVdqQixFQUFFO1FBR3REN0UsUUFBUUMsR0FBRyxDQUFDLDBFQUE2RDRGLGtCQUFrQjNDLE1BQU07UUFDakcyQyxrQkFBa0JNLE9BQU8sQ0FBQ1QsQ0FBQUE7WUFDeEIxRixRQUFRQyxHQUFHLENBQUMsa0NBQTZDeUYsT0FBckJBLElBQUlVLFFBQVEsRUFBQyxVQUE2QlYsT0FBckJBLElBQUliLEVBQUUsRUFBQyxnQkFBMkIsT0FBYmEsSUFBSUMsUUFBUSxFQUFDO1FBQzdGO1FBRUEsSUFBSUUsa0JBQWtCM0MsTUFBTSxLQUFLLEdBQUc7WUFDbENsRCxRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLDhDQUE4QztRQUM5Q0QsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNuQixTQUFTb0UsTUFBTTtRQUM1RCxNQUFNbUQsMEJBQTBCdkgsU0FBUzJHLE1BQU0sQ0FBQ2EsQ0FBQUEsTUFBT0EsSUFBSWpCLFdBQVcsSUFBSWlCLElBQUlqQixXQUFXLENBQUNuQyxNQUFNLEdBQUc7UUFDbkdsRCxRQUFRQyxHQUFHLENBQUMsbUNBQW1Db0csd0JBQXdCbkQsTUFBTTtRQUM3RW1ELHdCQUF3QkYsT0FBTyxDQUFDLENBQUNHLEtBQUtQO2dCQUloQk87WUFIcEJ0RyxRQUFRQyxHQUFHLENBQUMsWUFBc0IsT0FBVjhGLFFBQVEsR0FBRSxpQkFBZTtnQkFDL0NsQixJQUFJeUIsSUFBSXpCLEVBQUU7Z0JBQ1YwQixRQUFRRCxJQUFJQyxNQUFNO2dCQUNsQkMsa0JBQWtCRixFQUFBQSxtQkFBQUEsSUFBSWpCLFdBQVcsY0FBZmlCLHVDQUFBQSxpQkFBaUJwRCxNQUFNLEtBQUk7Z0JBQzdDbUMsYUFBYWlCLElBQUlqQixXQUFXO1lBQzlCO1FBQ0Y7UUFFQSxJQUFJLENBQUUzRyxRQUFRdUUsSUFBSSxNQUFPLEVBQUNvQyxlQUFlQSxZQUFZbkMsTUFBTSxLQUFLLE1BQU90RSxhQUFhYyxhQUFhO1lBQy9GTSxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQixDQUFDdkIsUUFBUXVFLElBQUk7WUFDNUNqRCxRQUFRQyxHQUFHLENBQUMsZUFBZSxDQUFDb0YsZUFBZUEsWUFBWW5DLE1BQU0sS0FBSztZQUNsRWxELFFBQVFDLEdBQUcsQ0FBQyxZQUFZckI7WUFDeEJvQixRQUFRQyxHQUFHLENBQUMsY0FBY1A7WUFDMUI7UUFDRjtRQUNBLElBQUksRUFBQ2pCLGlCQUFBQSwyQkFBQUEsS0FBTWtDLEtBQUssR0FBRTtRQUVsQixNQUFNOEYsY0FBdUI7WUFDM0I1QixJQUFJekcsZ0VBQVNBLENBQUNzSSxpQkFBaUI7WUFDL0JDLFNBQVNqSSxRQUFRdUUsSUFBSTtZQUNyQnNELFFBQVE7WUFDUjlELFdBQVcsSUFBSVAsT0FBT2EsV0FBVztZQUNqQ3NDLGFBQWFBLGVBQWUsRUFBRTtRQUNoQztRQUVBLGlEQUFpRDtRQUNqRCxJQUFJdUIsY0FBYzFIO1FBQ2xCLElBQUksQ0FBQzBILGFBQWE7WUFDaEIsTUFBTUMsaUJBQWlCbkksUUFBUXVFLElBQUksTUFBT29DLENBQUFBLGVBQWVBLFlBQVluQyxNQUFNLEdBQUcsSUFBSSxrQkFBa0IsZUFBYztZQUNsSDBELGNBQWMsTUFBTXJFLGVBQWVzRTtZQUNuQyxJQUFJRCxhQUFhO2dCQUNmekgsZ0JBQWdCeUg7Z0JBQ2hCLHdGQUF3RjtnQkFDeEZFLGFBQWFGO2dCQUNickksMEJBQUFBLG9DQUFBQSxjQUFnQnFJO1lBQ2xCO1FBQ0Y7UUFFQSxJQUFJLENBQUNBLGFBQWE7WUFDaEI1RyxRQUFRMkIsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLGdDQUFnQztRQUNoQzVDLFlBQVlnSSxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTU47YUFBWTtRQUMxQyxNQUFNTyxpQkFBaUJ0SSxRQUFRdUUsSUFBSSxNQUFNLElBQUksMkNBQTJDO1FBQ3hGdEUsV0FBVztRQUNYRSxhQUFhO1FBQ2JjLGVBQWU7UUFFZix3RUFBd0U7UUFDeEUsTUFBTXNILGNBQWM3SSxnRUFBU0EsQ0FBQ3NJLGlCQUFpQjtRQUMvQzdHLHNCQUFzQm9IO1FBRXRCLHFDQUFxQztRQUNyQyxNQUFNckcsV0FBVyxNQUFNQztRQUV2Qix1RUFBdUU7UUFDdkUsTUFBTXpDLGdFQUFTQSxDQUFDOEksZUFBZSxDQUM3QjtZQUNFdEcsVUFBVUE7WUFDVmtDLFFBQVE4RDtZQUNSbEksU0FBU3NJO1lBQ1RHLE9BQU9uSTtZQUNQcUcsYUFBYVE7WUFDYlAsa0JBQWtCQTtRQUNwQixHQUNBLHdFQUF3RTtRQUN4RSxDQUFDOEI7WUFDQ3JJLFlBQVlnSSxDQUFBQTtnQkFDViwwQ0FBMEM7Z0JBQzFDLE1BQU1NLHVCQUF1Qk4sS0FBS2QsU0FBUyxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJekIsRUFBRSxLQUFLb0M7Z0JBRTlELElBQUlJLHlCQUF5QixDQUFDLEdBQUc7b0JBQy9CLCtCQUErQjtvQkFDL0IsT0FBT04sS0FBS08sR0FBRyxDQUFDaEIsQ0FBQUEsTUFDZEEsSUFBSXpCLEVBQUUsS0FBS29DLGNBQ1A7NEJBQUUsR0FBR1gsR0FBRzs0QkFBRUssU0FBU0wsSUFBSUssT0FBTyxHQUFHUzt3QkFBTSxJQUN2Q2Q7Z0JBRVIsT0FBTztvQkFDTCw4Q0FBOEM7b0JBQzlDLG1FQUFtRTtvQkFDbkV6SCxhQUFhO29CQUViLE1BQU0wSSxZQUFxQjt3QkFDekIxQyxJQUFJb0M7d0JBQ0pOLFNBQVNTO3dCQUNUYixRQUFRO3dCQUNSOUQsV0FBVyxJQUFJUCxPQUFPYSxXQUFXO3dCQUNqQ3lFLGNBQWNsQztvQkFDaEI7b0JBQ0EsT0FBTzsyQkFBSXlCO3dCQUFNUTtxQkFBVTtnQkFDN0I7WUFDRjtRQUNGLEdBQ0EsbUNBQW1DO1FBQ25DLENBQUNFO1lBQ0MxSSxZQUFZZ0ksQ0FBQUEsT0FBUUEsS0FBS08sR0FBRyxDQUFDaEIsQ0FBQUEsTUFDM0JBLElBQUl6QixFQUFFLEtBQUtvQyxjQUNQO3dCQUFFLEdBQUdYLEdBQUc7d0JBQUVLLFNBQVNjO29CQUFhLElBQ2hDbkI7WUFFTnpILGFBQWE7WUFDYmMsZUFBZTtZQUNmRSxzQkFBc0I7WUFFdEIsZ0RBQWdEO1lBQ2hELElBQUlyQiwyQkFBMkI7Z0JBQzdCQTtZQUNGO1FBQ0YsR0FDQSx5QkFBeUI7UUFDekIsQ0FBQ21EO1lBQ0MzQixRQUFRMkIsS0FBSyxDQUFDLGVBQWVBO1lBQzdCNUMsWUFBWWdJLENBQUFBLE9BQVFBLEtBQUtPLEdBQUcsQ0FBQ2hCLENBQUFBLE1BQzNCQSxJQUFJekIsRUFBRSxLQUFLb0MsY0FDUDt3QkFBRSxHQUFHWCxHQUFHO3dCQUFFSyxTQUFTLFdBQWlCLE9BQU5oRjtvQkFBUSxJQUN0QzJFO1lBRU56SCxhQUFhO1lBQ2JjLGVBQWU7WUFDZkUsc0JBQXNCO1FBQ3hCO0lBRUo7SUFFQSxpQ0FBaUM7SUFDakMsTUFBTTZILHdCQUF3QjtRQUM1QnRKLGdFQUFTQSxDQUFDdUosYUFBYTtRQUN2QjlJLGFBQWE7UUFDYmMsZUFBZTtRQUNmRSxzQkFBc0I7SUFDeEI7SUFFQSxtREFBbUQ7SUFDbkQsTUFBTWlILGVBQWUsT0FBT2hFO1FBQzFCLElBQUksRUFBQ3JFLGlCQUFBQSwyQkFBQUEsS0FBTWtDLEtBQUssR0FBRTtRQUVsQixJQUFJO1lBQ0ZYLFFBQVFDLEdBQUcsQ0FBQztZQUNaRCxRQUFRQyxHQUFHLENBQUMsWUFBWTZDO1lBRXhCLE1BQU1sQyxXQUFXLE1BQU1DO1lBQ3ZCYixRQUFRQyxHQUFHLENBQUMsYUFBYVc7WUFFekIsTUFBTWdILFVBQVUsTUFBTXJLLDBEQUFNQSxDQUFDRix1REFBR0EsQ0FBQ00sNkNBQUVBLEVBQUUsWUFBWWlELFVBQVUsYUFBYWtDO1lBRXhFLElBQUk4RSxRQUFRN0YsTUFBTSxJQUFJO2dCQUNwQixNQUFNOEYsV0FBV0QsUUFBUW5HLElBQUk7Z0JBQzdCLE1BQU0zQixXQUFXK0gsU0FBUzdELElBQUksSUFBSTtnQkFDbENoRSxRQUFRQyxHQUFHLENBQUMsMkJBQTJCSDtnQkFDdkNDLFlBQVlEO1lBQ2QsT0FBTztnQkFDTEUsUUFBUUMsR0FBRyxDQUFDO2dCQUNaRixZQUFZO1lBQ2Q7UUFDRixFQUFFLE9BQU80QixPQUFPO1lBQ2QzQixRQUFRMkIsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaEQ1QixZQUFZO1FBQ2Q7SUFDRjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNK0gsbUJBQW1CLE9BQU9oRjtRQUM5QixJQUFJLEVBQUNyRSxpQkFBQUEsMkJBQUFBLEtBQU1rQyxLQUFLLEdBQUU7UUFFbEJOLGlCQUFpQjtRQUNqQixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNQztZQUN2QixNQUFNa0gsZUFBZSxNQUFNM0osZ0VBQVNBLENBQUMwSixnQkFBZ0IsQ0FBQ2xILFVBQVVrQztZQUVoRSx3Q0FBd0M7WUFDeEM5QyxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjhILGFBQWE3RSxNQUFNO1lBQ3RENkUsYUFBYTVCLE9BQU8sQ0FBQyxDQUFDRyxLQUFLUDtvQkFLTE87Z0JBSnBCdEcsUUFBUUMsR0FBRyxDQUFDLFlBQXNCLE9BQVY4RixRQUFRLEdBQUUsTUFBSTtvQkFDcENsQixJQUFJeUIsSUFBSXpCLEVBQUU7b0JBQ1ZtRCxNQUFNMUIsSUFBSTBCLElBQUk7b0JBQ2RDLGdCQUFnQixDQUFDLENBQUUzQixDQUFBQSxJQUFJakIsV0FBVyxJQUFJaUIsSUFBSWpCLFdBQVcsQ0FBQ25DLE1BQU0sR0FBRztvQkFDL0RzRCxrQkFBa0JGLEVBQUFBLG1CQUFBQSxJQUFJakIsV0FBVyxjQUFmaUIsdUNBQUFBLGlCQUFpQnBELE1BQU0sS0FBSTtvQkFDN0NtQyxhQUFhaUIsSUFBSWpCLFdBQVc7Z0JBQzlCO1lBQ0Y7WUFFQSxNQUFNNkMsb0JBQW9COUosZ0VBQVNBLENBQUMrSixtQkFBbUIsQ0FBQ0o7WUFFeEQseUNBQXlDO1lBQ3pDL0gsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNpSSxrQkFBa0JoRixNQUFNO1lBQ3ZFZ0Ysa0JBQWtCL0IsT0FBTyxDQUFDLENBQUNHLEtBQUtQO29CQUtWTztnQkFKcEJ0RyxRQUFRQyxHQUFHLENBQUMsdUJBQWlDLE9BQVY4RixRQUFRLEdBQUUsTUFBSTtvQkFDL0NsQixJQUFJeUIsSUFBSXpCLEVBQUU7b0JBQ1YwQixRQUFRRCxJQUFJQyxNQUFNO29CQUNsQjBCLGdCQUFnQixDQUFDLENBQUUzQixDQUFBQSxJQUFJakIsV0FBVyxJQUFJaUIsSUFBSWpCLFdBQVcsQ0FBQ25DLE1BQU0sR0FBRztvQkFDL0RzRCxrQkFBa0JGLEVBQUFBLG1CQUFBQSxJQUFJakIsV0FBVyxjQUFmaUIsdUNBQUFBLGlCQUFpQnBELE1BQU0sS0FBSTtvQkFDN0NtQyxhQUFhaUIsSUFBSWpCLFdBQVc7Z0JBQzlCO1lBQ0Y7WUFFQXRHLFlBQVltSjtRQUNkLEVBQUUsT0FBT3ZHLE9BQU87WUFDZDNCLFFBQVEyQixLQUFLLENBQUMsdUNBQXVDQTtZQUNyRDVDLFlBQVksRUFBRTtRQUNoQixTQUFVO1lBQ1JzQixpQkFBaUI7UUFDbkI7SUFDRjtJQUVBLDBEQUEwRDtJQUMxRGxELGdEQUFTQSxDQUFDO1FBQ1IsSUFBSXNCLE1BQU07WUFDUjREO1FBQ0Y7SUFDRixHQUFHO1FBQUM1RDtLQUFLO0lBRVQsK0NBQStDO0lBQy9DdEIsZ0RBQVNBLENBQUM7UUFDUjZDLFFBQVFDLEdBQUcsQ0FBQztRQUNaRCxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCM0I7UUFDNUIwQixRQUFRQyxHQUFHLENBQUMsaUJBQWlCZjtRQUU3QixJQUFJWixlQUFlQSxnQkFBZ0JZLGNBQWM7WUFDL0NjLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEIzQjtZQUMxQ2EsZ0JBQWdCYjtZQUNoQitCLGlCQUFpQjtZQUNqQix1RUFBdUU7WUFDdkV0QixZQUFZLEVBQUU7WUFDZCtJLGlCQUFpQnhKO1lBQ2pCd0ksYUFBYXhJO1FBQ2YsT0FBTyxJQUFJLENBQUNBLGFBQWE7WUFDdkIwQixRQUFRQyxHQUFHLENBQUM7WUFDWmQsZ0JBQWdCO1lBQ2hCSixZQUFZLEVBQUU7WUFDZGdCLFlBQVk7WUFDWk0saUJBQWlCO1FBQ25CLE9BQU87WUFDTEwsUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUMzQjtRQUFhRyxpQkFBQUEsMkJBQUFBLEtBQU1rQyxLQUFLO0tBQUM7SUFFN0IsbUNBQW1DO0lBQ25DLE1BQU15SCxzQkFBc0IsT0FBT0M7UUFDakMsSUFBSSxDQUFDbkosZ0JBQWdCLEVBQUNULGlCQUFBQSwyQkFBQUEsS0FBTWtDLEtBQUssR0FBRTtRQUVuQyw4Q0FBOEM7UUFDOUM1QixZQUFZZ0ksQ0FBQUEsT0FBUUEsS0FBS3RCLE1BQU0sQ0FBQ2EsQ0FBQUEsTUFBT0EsSUFBSXpCLEVBQUUsS0FBS3dEO1FBRWxELElBQUk7WUFDRixNQUFNekgsV0FBVyxNQUFNQztZQUN2QixNQUFNeUgsVUFBVSxNQUFNbEssZ0VBQVNBLENBQUNtSyxhQUFhLENBQUMzSCxVQUFVMUIsY0FBY21KO1lBRXRFLElBQUksQ0FBQ0MsU0FBUztnQkFDWixrQ0FBa0M7Z0JBQ2xDUixpQkFBaUI1STtnQkFDakJjLFFBQVEyQixLQUFLLENBQUM7WUFDaEI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZCxrQ0FBa0M7WUFDbENtRyxpQkFBaUI1STtZQUNqQmMsUUFBUTJCLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzdDO0lBQ0Y7SUFFQSxNQUFNNkcsMEJBQTBCLE9BQU9IO1FBQ3JDLElBQUksQ0FBQ25KLGdCQUFnQixFQUFDVCxpQkFBQUEsMkJBQUFBLEtBQU1rQyxLQUFLLEdBQUU7UUFFbkMsTUFBTThILGVBQWUzSixTQUFTbUgsU0FBUyxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJekIsRUFBRSxLQUFLd0Q7UUFDMUQsSUFBSUksaUJBQWlCLENBQUMsR0FBRztRQUV6QixNQUFNQyxzQkFBc0I1SixRQUFRLENBQUMySixhQUFhO1FBRWxELDhFQUE4RTtRQUM5RSxNQUFNRSw2QkFBNkI3SixTQUFTc0UsS0FBSyxDQUFDLEdBQUdxRixlQUFlO1FBQ3BFMUosWUFBWTRKO1FBRVosaURBQWlEO1FBQ2pEaEssV0FBVytKLG9CQUFvQi9CLE9BQU87UUFDdEM5SCxhQUFhO1FBQ2JjLGVBQWU7UUFFZiw2RUFBNkU7UUFDN0UsTUFBTXNILGNBQWM3SSxnRUFBU0EsQ0FBQ3NJLGlCQUFpQjtRQUMvQzdHLHNCQUFzQm9IO1FBRXRCLHFDQUFxQztRQUNyQyxNQUFNckcsV0FBVyxNQUFNQztRQUV2QixJQUFJO1lBQ0YscUZBQXFGO1lBQ3JGLElBQUssSUFBSStILElBQUlILGVBQWUsR0FBR0csSUFBSTlKLFNBQVNvRSxNQUFNLEVBQUUwRixJQUFLO2dCQUN2RCxNQUFNQyxjQUFjL0osUUFBUSxDQUFDOEosRUFBRTtnQkFDL0IsTUFBTXhLLGdFQUFTQSxDQUFDbUssYUFBYSxDQUFDM0gsVUFBVTFCLGNBQWMySixZQUFZaEUsRUFBRTtZQUN0RTtZQUVBLGtDQUFrQztZQUNsQyxNQUFNekcsZ0VBQVNBLENBQUM4SSxlQUFlLENBQzdCO2dCQUNFdEcsVUFBVUE7Z0JBQ1ZrQyxRQUFRNUQ7Z0JBQ1JSLFNBQVNnSyxvQkFBb0IvQixPQUFPO2dCQUNwQ1EsT0FBT25JO2dCQUNQOEosZ0JBQWdCO1lBQ2xCLEdBQ0Esd0VBQXdFO1lBQ3hFLENBQUMxQjtnQkFDQ3JJLFlBQVlnSSxDQUFBQTtvQkFDViwwQ0FBMEM7b0JBQzFDLE1BQU1NLHVCQUF1Qk4sS0FBS2QsU0FBUyxDQUFDSyxDQUFBQSxNQUFPQSxJQUFJekIsRUFBRSxLQUFLb0M7b0JBRTlELElBQUlJLHlCQUF5QixDQUFDLEdBQUc7d0JBQy9CLCtCQUErQjt3QkFDL0IsT0FBT04sS0FBS08sR0FBRyxDQUFDaEIsQ0FBQUEsTUFDZEEsSUFBSXpCLEVBQUUsS0FBS29DLGNBQ1A7Z0NBQUUsR0FBR1gsR0FBRztnQ0FBRUssU0FBU0wsSUFBSUssT0FBTyxHQUFHUzs0QkFBTSxJQUN2Q2Q7b0JBRVIsT0FBTzt3QkFDTCw4Q0FBOEM7d0JBQzlDLG1FQUFtRTt3QkFDbkV6SCxhQUFhO3dCQUViLE1BQU0wSSxZQUFxQjs0QkFDekIxQyxJQUFJb0M7NEJBQ0pOLFNBQVNTOzRCQUNUYixRQUFROzRCQUNSOUQsV0FBVyxJQUFJUCxPQUFPYSxXQUFXOzRCQUNqQ3lFLGNBQWM7d0JBQ2hCO3dCQUNBLE9BQU87K0JBQUlUOzRCQUFNUTt5QkFBVTtvQkFDN0I7Z0JBQ0Y7WUFDRixHQUNBLG1DQUFtQztZQUNuQyxDQUFDRTtnQkFDQzFJLFlBQVlnSSxDQUFBQSxPQUFRQSxLQUFLTyxHQUFHLENBQUNoQixDQUFBQSxNQUMzQkEsSUFBSXpCLEVBQUUsS0FBS29DLGNBQ1A7NEJBQUUsR0FBR1gsR0FBRzs0QkFBRUssU0FBU2M7d0JBQWEsSUFDaENuQjtnQkFFTnpILGFBQWE7Z0JBQ2JjLGVBQWU7Z0JBQ2ZFLHNCQUFzQjtnQkFDdEJsQixXQUFXLEtBQUssMEJBQTBCO2dCQUUxQyxtREFBbUQ7Z0JBQ25ELElBQUlILDJCQUEyQjtvQkFDN0JBO2dCQUNGO1lBQ0YsR0FDQSx5QkFBeUI7WUFDekIsQ0FBQ21EO2dCQUNDM0IsUUFBUTJCLEtBQUssQ0FBQyw4QkFBd0JBO2dCQUN0QzVDLFlBQVlnSSxDQUFBQSxPQUFRQSxLQUFLTyxHQUFHLENBQUNoQixDQUFBQSxNQUMzQkEsSUFBSXpCLEVBQUUsS0FBS29DLGNBQ1A7NEJBQUUsR0FBR1gsR0FBRzs0QkFBRUssU0FBUyxnQ0FBZ0MsT0FBTmhGO3dCQUFRLElBQ3JEMkU7Z0JBRU56SCxhQUFhO2dCQUNiYyxlQUFlO2dCQUNmRSxzQkFBc0I7Z0JBQ3RCbEIsV0FBVyxLQUFLLDBCQUEwQjtZQUM1QztRQUVKLEVBQUUsT0FBT2dELE9BQU87WUFDZDNCLFFBQVEyQixLQUFLLENBQUMsK0JBQStCQTtZQUM3QzlDLGFBQWE7WUFDYmMsZUFBZTtZQUNmRSxzQkFBc0I7WUFDdEJsQixXQUFXLEtBQUssMEJBQTBCO1lBRTFDLHVDQUF1QztZQUN2Q21KLGlCQUFpQjVJO1FBQ25CO0lBQ0Y7SUFJQSxNQUFNNkosb0JBQW9CLE9BQU9WLFdBQW1CVztRQUNsRCxJQUFJLENBQUM5SixnQkFBZ0IsRUFBQ1QsaUJBQUFBLDJCQUFBQSxLQUFNa0MsS0FBSyxHQUFFO1FBRW5DLGdEQUFnRDtRQUNoRDVCLFlBQVlnSSxDQUFBQSxPQUFRQSxLQUFLTyxHQUFHLENBQUNoQixDQUFBQSxNQUMzQkEsSUFBSXpCLEVBQUUsS0FBS3dELFlBQVk7b0JBQUUsR0FBRy9CLEdBQUc7b0JBQUVLLFNBQVNxQztnQkFBVyxJQUFJMUM7UUFHM0QsSUFBSTtZQUNGLE1BQU0xRixXQUFXLE1BQU1DO1lBQ3ZCLE1BQU15SCxVQUFVLE1BQU1sSyxnRUFBU0EsQ0FBQzZLLGFBQWEsQ0FBQ3JJLFVBQVUxQixjQUFjbUosV0FBV1c7WUFFakYsSUFBSSxDQUFDVixTQUFTO2dCQUNaLDJDQUEyQztnQkFDM0NSLGlCQUFpQjVJO2dCQUNqQmMsUUFBUTJCLEtBQUssQ0FBQztZQUNoQjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkLDJDQUEyQztZQUMzQ21HLGlCQUFpQjVJO1lBQ2pCYyxRQUFRMkIsS0FBSyxDQUFDLCtCQUErQkE7UUFDL0M7SUFDRjtJQUVBLE1BQU11SCxvQkFBb0IsQ0FBQ3ZDO1FBQ3pCd0MsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUMxQyxTQUFTMkMsSUFBSSxDQUFDO1lBQzFDdEosUUFBUUMsR0FBRyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNc0osb0JBQW9CO1lBQ3hCOUk7U0FBQUEsNEJBQUFBLGlCQUFpQitJLE9BQU8sY0FBeEIvSSxnREFBQUEsMEJBQTBCZ0osUUFBUSxDQUFDO1lBQUVDLEtBQUs7WUFBR0MsVUFBVTtRQUFTO0lBQ2xFO0lBRUEsTUFBTUMsdUJBQXVCO1lBQzNCbko7U0FBQUEsNEJBQUFBLGlCQUFpQitJLE9BQU8sY0FBeEIvSSxnREFBQUEsMEJBQTBCZ0osUUFBUSxDQUFDO1lBQ2pDQyxLQUFLakosaUJBQWlCK0ksT0FBTyxDQUFDSyxZQUFZO1lBQzFDRixVQUFVO1FBQ1o7SUFDRjtJQUlBLDZEQUE2RDtJQUM3RCxNQUFNRyx3QkFBd0IsQ0FBQ2hMO1FBQzdCLE9BQU9BLFNBQVN3SSxHQUFHLENBQUNoQixDQUFBQSxNQUFRO2dCQUMxQnpCLElBQUl5QixJQUFJekIsRUFBRTtnQkFDVjhCLFNBQVNMLElBQUlLLE9BQU87Z0JBQ3BCcUIsTUFBTTFCLElBQUlDLE1BQU0sS0FBSyxTQUFTLFNBQVM7Z0JBQ3ZDOUQsV0FBVyxJQUFJUCxLQUFLb0UsSUFBSTdELFNBQVMsRUFBRXNILE9BQU87Z0JBQzFDQyxZQUFZMUQsSUFBSTBELFVBQVUsSUFBSTtnQkFDOUIzRSxhQUFhaUIsSUFBSWpCLFdBQVcsSUFBSSxFQUFFO1lBQ3BDO0lBQ0Y7SUFFQSx3Q0FBd0M7SUFDeEMsTUFBTTRFLHNCQUFzQjtRQUMxQjVLLHVCQUF1QjtJQUN6QjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNNksseUJBQXlCO1FBQzdCekssMEJBQTBCO0lBQzVCO0lBRUEsNENBQTRDO0lBQzVDLE1BQU0rRix3QkFBd0I7UUFDNUIsTUFBTTJFLGlCQUFrRSxFQUFFO1FBRTFFckwsU0FBU3FILE9BQU8sQ0FBQ3pILENBQUFBO1lBQ2YsSUFBSUEsUUFBUTJHLFdBQVcsSUFBSTNHLFFBQVEyRyxXQUFXLENBQUNuQyxNQUFNLEdBQUcsR0FBRztnQkFDekRpSCxlQUFlQyxJQUFJLElBQUkxTCxRQUFRMkcsV0FBVztZQUM1QztRQUNGO1FBRUEsbUNBQW1DO1FBQ25DLE1BQU1RLG9CQUFvQnNFLGVBQWUxRSxNQUFNLENBQUMsQ0FBQ0ssWUFBWUMsT0FBT0MsT0FDbEVELFVBQVVDLEtBQUtDLFNBQVMsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJCLEVBQUUsS0FBS2lCLFdBQVdqQixFQUFFO1FBR3RELE9BQU9nQjtJQUNUO0lBRUEsNkRBQTZEO0lBQzdELE1BQU13RSx1QkFBdUIsT0FBT0M7UUFDbEMsSUFBSSxDQUFDaEssbUJBQW1CLENBQUNwQixjQUFjO1FBRXZDLElBQUk7WUFDRixxQ0FBcUM7WUFDckMsTUFBTTJJLFdBQVc7Z0JBQ2ZoRCxJQUFJM0Y7Z0JBQ0o4RSxNQUFNbEUsWUFBWTtnQkFDbEJoQixVQUFVd0wsZ0JBQWdCaEQsR0FBRyxDQUFDaEIsQ0FBQUEsTUFBUTt3QkFDcEN6QixJQUFJeUIsSUFBSXpCLEVBQUU7d0JBQ1Y4QixTQUFTTCxJQUFJSyxPQUFPO3dCQUNwQnFCLE1BQU0xQixJQUFJQyxNQUFNLEtBQUssU0FBUyxTQUFTO3dCQUN2QzlELFdBQVc2RCxJQUFJN0QsU0FBUzt3QkFDeEJ1SCxZQUFZMUQsSUFBSTBELFVBQVU7d0JBQzFCM0UsYUFBYWlCLElBQUlqQixXQUFXO29CQUM5QjtnQkFDQTVCLFdBQVcsSUFBSXZCLE9BQU9hLFdBQVc7Z0JBQ2pDc0IsYUFBYSxJQUFJbkMsT0FBT2EsV0FBVztZQUNyQztZQUVBLDZCQUE2QjtZQUM3QixNQUFNK0IsZUFBZSxJQUFJQyxLQUFLO2dCQUFDQyxLQUFLQyxTQUFTLENBQUM0QyxVQUFVLE1BQU07YUFBRyxFQUFFO2dCQUNqRTNDLE1BQU07WUFDUjtZQUVBLE1BQU1xRixjQUFjOU0scURBQUdBLENBQUNHLGtEQUFPQSxFQUFFLFlBQXlDc0IsT0FBN0JvQixpQkFBZ0IsZUFBMEIsT0FBYnBCLGNBQWE7WUFDdkYsTUFBTXhCLDZEQUFXQSxDQUFDNk0sYUFBYXpGO1lBRS9COUUsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyw4QkFBb0I7Z0JBQzlCNkMsUUFBUTVEO2dCQUNSc0wsZUFBZTNDLFNBQVMvSSxRQUFRLENBQUNvRSxNQUFNO2dCQUN2Q21ELHlCQUF5QndCLFNBQVMvSSxRQUFRLENBQUMyRyxNQUFNLENBQUNhLENBQUFBO3dCQUFPQTsyQkFBQUEsRUFBQUEsbUJBQUFBLElBQUlqQixXQUFXLGNBQWZpQix1Q0FBQUEsaUJBQWlCcEQsTUFBTSxJQUFHO21CQUFHQSxNQUFNO1lBQzlGO1FBQ0YsRUFBRSxPQUFPdkIsT0FBTztZQUNkM0IsUUFBUTJCLEtBQUssQ0FBQyx1Q0FBdUNBO1FBQ3ZEO0lBQ0Y7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTThJLHlCQUF5QixDQUFDQztRQUM5QjNMLFlBQVk0TCxDQUFBQTtZQUNWLE1BQU1MLGtCQUFrQkssYUFBYXJELEdBQUcsQ0FBQzVJLENBQUFBO2dCQUN2QyxJQUFJQSxRQUFRMkcsV0FBVyxJQUFJM0csUUFBUTJHLFdBQVcsQ0FBQ25DLE1BQU0sR0FBRyxHQUFHO29CQUN6RCxNQUFNMEgscUJBQXFCbE0sUUFBUTJHLFdBQVcsQ0FBQ2lDLEdBQUcsQ0FBQ3hCLENBQUFBO3dCQUNqRCxJQUFJQSxXQUFXakIsRUFBRSxLQUFLNkYsY0FBYzs0QkFDbEMseUVBQXlFOzRCQUN6RSxNQUFNRyxlQUFlL0UsV0FBV0gsUUFBUSxLQUFLOzRCQUM3QyxPQUFPO2dDQUFFLEdBQUdHLFVBQVU7Z0NBQUVILFVBQVUsQ0FBQ2tGOzRCQUFhO3dCQUNsRDt3QkFDQSxPQUFPL0U7b0JBQ1Q7b0JBQ0EsT0FBTzt3QkFBRSxHQUFHcEgsT0FBTzt3QkFBRTJHLGFBQWF1RjtvQkFBbUI7Z0JBQ3ZEO2dCQUNBLE9BQU9sTTtZQUNUO1lBRUEsaURBQWlEO1lBQ2pEMkwscUJBQXFCQztZQUVyQixPQUFPQTtRQUNUO0lBQ0Y7SUFFQSxvREFBb0Q7SUFDcEQsTUFBTVEsdUJBQXVCLENBQUN6RjtRQUM1QixJQUFJLENBQUNBLGFBQWEsT0FBTyxFQUFFO1FBRTNCLGdFQUFnRTtRQUNoRSw0REFBNEQ7UUFDNUQsT0FBT0EsWUFBWUksTUFBTSxDQUFDSyxDQUFBQTtZQUN4QixzREFBc0Q7WUFDdEQsSUFBSUEsV0FBV0gsUUFBUSxLQUFLbkYsV0FBVyxPQUFPO1lBQzlDLDZEQUE2RDtZQUM3RCxPQUFPc0YsV0FBV0gsUUFBUSxLQUFLO1FBQ2pDO0lBQ0Y7SUFFQSw4Q0FBOEM7SUFDOUN4SSxnREFBU0EsQ0FBQztRQUNSLDRFQUE0RTtRQUM1RSxJQUFJbUIsZ0JBQWdCWSxjQUFjO1lBQ2hDSCxZQUFZLEVBQUU7UUFDaEI7UUFDQUksZ0JBQWdCYjtJQUNsQixHQUFHO1FBQUNBO0tBQVk7SUFFaEIsMENBQTBDO0lBQzFDLE1BQU15TSx5QkFBeUI7UUFDN0IsTUFBTVosaUJBQWlCM0U7UUFDdkIsT0FBTzJFLGVBQWUxRSxNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLFFBQVEsS0FBSyxPQUFPMkIsR0FBRyxDQUFDNUIsQ0FBQUEsTUFBT0EsSUFBSWIsRUFBRTtJQUMvRTtJQUVBLHFCQUNFLDhEQUFDbUc7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNuTixpREFBUUE7Z0JBQ1BRLGFBQWFBO2dCQUNid0IsVUFBVUE7Z0JBQ1ZvTCxTQUFTbE07Z0JBQ1RtTSxZQUFZbEI7Z0JBQ1ptQixlQUFlbEI7Z0JBQ2Z0TCxXQUFXQTtnQkFDWDRILGtCQUFrQmhCLHdCQUF3QnRDLE1BQU07Z0JBQ2hEbUksWUFBWTtvQkFDVkMsU0FBUztnQkFDWDs7Ozs7OzBCQUlGLDhEQUFDTjtnQkFBSXZOLEtBQUtnRDtnQkFBa0J3SyxXQUFVO2dCQUFpQ00sT0FBTztvQkFBRUMsUUFBUTtnQkFBc0I7MEJBQzVHLDRFQUFDek4sc0RBQWFBO29CQUNaZSxVQUFVQTtvQkFDVkYsV0FBV0E7b0JBQ1h3QixlQUFlQTtvQkFDZnFMLGlCQUFpQnJEO29CQUNqQnNELHFCQUFxQmxEO29CQUNyQm1ELGVBQWU1QztvQkFDZjZDLGVBQWUxQzs7Ozs7Ozs7Ozs7MEJBS25CLDhEQUFDbEwsaURBQVFBO2dCQUNQVSxTQUFTQTtnQkFDVEMsWUFBWUE7Z0JBQ1prTixlQUFlekc7Z0JBQ2Z4RyxXQUFXQTtnQkFDWEksZUFBZUE7Z0JBQ2Y4TSxlQUFleEo7Z0JBQ2Z5SixlQUFleEM7Z0JBQ2Z5QyxrQkFBa0JwQztnQkFDbEJsSyxhQUFhQTtnQkFDYnVNLG1CQUFtQnZFO2dCQUNuQndFLGtCQUFrQixJQUFNM00sb0JBQW9CO2dCQUM1Q3FCLFVBQVVOO2dCQUNWd0MsUUFBUTVELGdCQUFnQnNCO2dCQUN4QjJMLHdCQUF3QnBCLHlCQUF5QjdILE1BQU07Ozs7OzswQkFJekQsOERBQUNqRixzREFBYUE7Z0JBQ1ptTyxRQUFRaE47Z0JBQ1JpTixTQUFTLElBQU1oTix1QkFBdUI7Z0JBQ3RDUCxVQUFVZ0wsc0JBQXNCaEw7Z0JBQ2hDZ0IsVUFBVUE7Ozs7OzswQkFJWiw4REFBQzVCLDZEQUFtQkE7Z0JBQ2xCa08sUUFBUTlNO2dCQUNSK00sU0FBUyxJQUFNOU0sb0JBQW9CO2dCQUNuQytNLGNBQWN0TjtnQkFDZHVOLGVBQWVqSzs7Ozs7OzBCQUlqQiw4REFBQ25FLDBEQUFnQkE7Z0JBQ2ZpTyxRQUFRNU07Z0JBQ1I2TSxTQUFTLElBQU01TSwwQkFBMEI7Z0JBQ3pDNEYsYUFBYUc7Z0JBQ2JnSCxtQkFBbUJ6QjtnQkFDbkIwQixvQkFBb0JoQzs7Ozs7Ozs7Ozs7O0FBSTVCO0dBejJCd0JwTTs7UUFDTFIsMERBQU9BOzs7S0FERlEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL0NoYXRBcmVhLnRzeD9jNjZkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZG9jLCBzZXREb2MsIGNvbGxlY3Rpb24sIGdldERvYywgdXBkYXRlRG9jIH0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcbmltcG9ydCB7IHJlZiwgdXBsb2FkQnl0ZXMgfSBmcm9tICdmaXJlYmFzZS9zdG9yYWdlJztcbmltcG9ydCB7IGRiLCBzdG9yYWdlIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IFVwcGVyYmFyIGZyb20gJy4vVXBwZXJiYXInO1xuaW1wb3J0IENoYXRJbnRlcmZhY2UgZnJvbSAnLi9DaGF0SW50ZXJmYWNlJztcbmltcG9ydCBJbnB1dEJhciBmcm9tICcuL0lucHV0QmFyJztcbmltcG9ydCBBdHRhY2htZW50RGlzcGxheSBmcm9tICcuL0F0dGFjaG1lbnREaXNwbGF5JztcbmltcG9ydCBEb3dubG9hZE1vZGFsIGZyb20gJy4vRG93bmxvYWRNb2RhbCc7XG5pbXBvcnQgTW9kZWxTZWxlY3Rpb25Nb2RhbCBmcm9tICcuL01vZGVsU2VsZWN0aW9uTW9kYWwnO1xuaW1wb3J0IEF0dGFjaG1lbnRzTW9kYWwgZnJvbSAnLi9BdHRhY2htZW50c01vZGFsJztcbmltcG9ydCB7IENoYXRNZXNzYWdlIH0gZnJvbSAnQC9saWIvdHlwZXMvY2hhdCc7XG5pbXBvcnQgYWlTZXJ2aWNlIGZyb20gJ0AvbGliL3NlcnZpY2VzL2FpU2VydmljZSc7XG5cbmludGVyZmFjZSBXZWJTZWFyY2hBbm5vdGF0aW9uIHtcbiAgdHlwZTogXCJ1cmxfY2l0YXRpb25cIjtcbiAgdXJsOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGNvbnRlbnQ/OiBzdHJpbmc7XG4gIHN0YXJ0X2luZGV4OiBudW1iZXI7XG4gIGVuZF9pbmRleDogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgTWVzc2FnZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgc2VuZGVyOiAndXNlcicgfCAnYWknO1xuICB0aW1lc3RhbXA6IHN0cmluZztcbiAgaXNGYXZvcml0ZT86IGJvb2xlYW47XG4gIGF0dGFjaG1lbnRzPzogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW107XG4gIGhhc1dlYlNlYXJjaD86IGJvb2xlYW47XG4gIHdlYlNlYXJjaEFubm90YXRpb25zPzogV2ViU2VhcmNoQW5ub3RhdGlvbltdO1xufVxuXG5pbnRlcmZhY2UgQ2hhdEFyZWFQcm9wcyB7XG4gIGN1cnJlbnRDaGF0OiBzdHJpbmcgfCBudWxsO1xuICBvbkNoYXRDcmVhdGVkPzogKGNoYXRJZDogc3RyaW5nKSA9PiB2b2lkO1xuICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlPzogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2hhdEFyZWEoeyBjdXJyZW50Q2hhdCwgb25DaGF0Q3JlYXRlZCwgb25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZSB9OiBDaGF0QXJlYVByb3BzKSB7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbbWVzc2FnZSwgc2V0TWVzc2FnZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZE1vZGVsLCBzZXRTZWxlY3RlZE1vZGVsXSA9IHVzZVN0YXRlKCdtZXRhLWxsYW1hL2xsYW1hLTMuMS04Yi1pbnN0cnVjdDpmcmVlJyk7XG5cbiAgY29uc3QgW2FjdHVhbENoYXRJZCwgc2V0QWN0dWFsQ2hhdElkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KGN1cnJlbnRDaGF0KTtcbiAgY29uc3QgW2lzRG93bmxvYWRNb2RhbE9wZW4sIHNldElzRG93bmxvYWRNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNNb2RlbE1vZGFsT3Blbiwgc2V0SXNNb2RlbE1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0F0dGFjaG1lbnRzTW9kYWxPcGVuLCBzZXRJc0F0dGFjaG1lbnRzTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzU3RyZWFtaW5nLCBzZXRJc1N0cmVhbWluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzdHJlYW1pbmdNZXNzYWdlSWQsIHNldFN0cmVhbWluZ01lc3NhZ2VJZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2NoYXROYW1lLCBzZXRDaGF0TmFtZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KCdOb3ZhIENvbnZlcnNhJyk7XG5cbiAgLy8gRGVidWc6IHJhc3RyZWFyIG11ZGFuw6dhcyBubyBjaGF0TmFtZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUc6IENIQVROQU1FIE1VRE9VID09PScpO1xuICAgIGNvbnNvbGUubG9nKCdOb3ZvIGNoYXROYW1lOicsIGNoYXROYW1lKTtcbiAgICBjb25zb2xlLmxvZygnU3RhY2sgdHJhY2U6JywgbmV3IEVycm9yKCkuc3RhY2spO1xuICB9LCBbY2hhdE5hbWVdKTtcbiAgY29uc3QgW2lzTG9hZGluZ0NoYXQsIHNldElzTG9hZGluZ0NoYXRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFVzZXJuYW1lLCBzZXRDdXJyZW50VXNlcm5hbWVdID0gdXNlU3RhdGU8c3RyaW5nIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICBjb25zdCBjaGF0SW50ZXJmYWNlUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICAvLyBDYXJyZWdhciB1c2VybmFtZSBxdWFuZG8gbyB1c3XDoXJpbyBlc3RpdmVyIGRpc3BvbsOtdmVsXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbG9hZFVzZXJuYW1lID0gYXN5bmMgKCkgPT4ge1xuICAgICAgaWYgKHVzZXI/LmVtYWlsKSB7XG4gICAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICAgIHNldEN1cnJlbnRVc2VybmFtZSh1c2VybmFtZSk7XG4gICAgICB9XG4gICAgfTtcbiAgICBsb2FkVXNlcm5hbWUoKTtcbiAgfSwgW3VzZXI/LmVtYWlsXSk7XG5cbiAgLy8gRnVuw6fDo28gdXRpbGl0w6FyaWEgcGFyYSBidXNjYXIgdXNlcm5hbWUgY29ycmV0b1xuICBjb25zdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgICBpZiAoIXVzZXI/LmVtYWlsKSByZXR1cm4gJ3Vua25vd24nO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgY29sbGVjdGlvbiwgcXVlcnksIHdoZXJlLCBnZXREb2NzIH0gPSBhd2FpdCBpbXBvcnQoJ2ZpcmViYXNlL2ZpcmVzdG9yZScpO1xuICAgICAgY29uc3QgdXN1YXJpb3NSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnKTtcbiAgICAgIGNvbnN0IHEgPSBxdWVyeSh1c3Vhcmlvc1JlZiwgd2hlcmUoJ2VtYWlsJywgJz09JywgdXNlci5lbWFpbCkpO1xuICAgICAgY29uc3QgcXVlcnlTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG5cbiAgICAgIGlmICghcXVlcnlTbmFwc2hvdC5lbXB0eSkge1xuICAgICAgICBjb25zdCB1c2VyRG9jID0gcXVlcnlTbmFwc2hvdC5kb2NzWzBdO1xuICAgICAgICBjb25zdCB1c2VyRGF0YSA9IHVzZXJEb2MuZGF0YSgpO1xuICAgICAgICByZXR1cm4gdXNlckRhdGEudXNlcm5hbWUgfHwgdXNlci5lbWFpbC5zcGxpdCgnQCcpWzBdO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gdXNlci5lbWFpbC5zcGxpdCgnQCcpWzBdOyAvLyBmYWxsYmFja1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGJ1c2NhciB1c2VybmFtZTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gdXNlci5lbWFpbC5zcGxpdCgnQCcpWzBdOyAvLyBmYWxsYmFja1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIHNhbHZhciBvIMO6bHRpbW8gbW9kZWxvIHVzYWRvXG4gIGNvbnN0IHNhdmVMYXN0VXNlZE1vZGVsID0gYXN5bmMgKG1vZGVsSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCB1c2VyUmVmID0gZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbmZpZ3VyYWNvZXMnLCAnc2V0dGluZ3MnKTtcblxuICAgICAgLy8gVmVyaWZpY2FyIHNlIG8gZG9jdW1lbnRvIGV4aXN0ZVxuICAgICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyh1c2VyUmVmKTtcblxuICAgICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgLy8gQXR1YWxpemFyIGRvY3VtZW50byBleGlzdGVudGVcbiAgICAgICAgYXdhaXQgdXBkYXRlRG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICBsYXN0VXNlZE1vZGVsOiBtb2RlbElkLFxuICAgICAgICAgIGxhc3RNb2RlbFVwZGF0ZUF0OiBEYXRlLm5vdygpXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQ3JpYXIgbm92byBkb2N1bWVudG9cbiAgICAgICAgYXdhaXQgc2V0RG9jKHVzZXJSZWYsIHtcbiAgICAgICAgICBsYXN0VXNlZE1vZGVsOiBtb2RlbElkLFxuICAgICAgICAgIGxhc3RNb2RlbFVwZGF0ZUF0OiBEYXRlLm5vdygpXG4gICAgICAgIH0sIHsgbWVyZ2U6IHRydWUgfSk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdMYXN0IHVzZWQgbW9kZWwgc2F2ZWQ6JywgbW9kZWxJZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBsYXN0IHVzZWQgbW9kZWw6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG8gw7psdGltbyBtb2RlbG8gdXNhZG9cbiAgY29uc3QgbG9hZExhc3RVc2VkTW9kZWwgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IHVzZXJSZWYgPSBkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJuYW1lLCAnY29uZmlndXJhY29lcycsICdzZXR0aW5ncycpO1xuICAgICAgY29uc3QgdXNlckRvYyA9IGF3YWl0IGdldERvYyh1c2VyUmVmKTtcblxuICAgICAgaWYgKHVzZXJEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHVzZXJEb2MuZGF0YSgpO1xuICAgICAgICBpZiAoZGF0YS5sYXN0VXNlZE1vZGVsKSB7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRNb2RlbChkYXRhLmxhc3RVc2VkTW9kZWwpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgbGFzdCB1c2VkIG1vZGVsOicsIGRhdGEubGFzdFVzZWRNb2RlbCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBsYXN0IHVzZWQgbW9kZWw6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyB3cmFwcGVyIHBhcmEgc2V0U2VsZWN0ZWRNb2RlbCBxdWUgdGFtYsOpbSBzYWx2YSBubyBGaXJlc3RvcmVcbiAgY29uc3QgaGFuZGxlTW9kZWxDaGFuZ2UgPSAobW9kZWxJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRNb2RlbChtb2RlbElkKTtcbiAgICBzYXZlTGFzdFVzZWRNb2RlbChtb2RlbElkKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNyaWFyIHVtIGNoYXQgYXV0b21hdGljYW1lbnRlXG4gIGNvbnN0IGNyZWF0ZUF1dG9DaGF0ID0gYXN5bmMgKGZpcnN0TWVzc2FnZTogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuIG51bGw7XG5cbiAgICB0cnkge1xuICAgICAgLy8gQnVzY2FyIHVzZXJuYW1lIGRvIHVzdcOhcmlvXG4gICAgICBjb25zdCB7IGNvbGxlY3Rpb24sIHF1ZXJ5LCB3aGVyZSwgZ2V0RG9jcyB9ID0gYXdhaXQgaW1wb3J0KCdmaXJlYmFzZS9maXJlc3RvcmUnKTtcbiAgICAgIGNvbnN0IHVzdWFyaW9zUmVmID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJyk7XG4gICAgICBjb25zdCBxID0gcXVlcnkodXN1YXJpb3NSZWYsIHdoZXJlKCdlbWFpbCcsICc9PScsIHVzZXIuZW1haWwpKTtcbiAgICAgIGNvbnN0IHF1ZXJ5U25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpO1xuXG4gICAgICBpZiAocXVlcnlTbmFwc2hvdC5lbXB0eSkgcmV0dXJuIG51bGw7XG5cbiAgICAgIGNvbnN0IHVzZXJEb2MgPSBxdWVyeVNuYXBzaG90LmRvY3NbMF07XG4gICAgICBjb25zdCB1c2VyRGF0YSA9IHVzZXJEb2MuZGF0YSgpO1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSB1c2VyRGF0YS51c2VybmFtZTtcblxuICAgICAgLy8gR2VyYXIgSUQgw7puaWNvIHBhcmEgbyBjaGF0XG4gICAgICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpO1xuICAgICAgY29uc3QgcmFuZG9tID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDIsIDgpO1xuICAgICAgY29uc3QgY2hhdElkID0gYGNoYXRfJHt0aW1lc3RhbXB9XyR7cmFuZG9tfWA7XG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCk7XG5cbiAgICAgIC8vIEdlcmFyIG5vbWUgZG8gY2hhdCBiYXNlYWRvIG5hIHByaW1laXJhIG1lbnNhZ2VtIChwcmltZWlyYXMgMy00IHBhbGF2cmFzKVxuICAgICAgbGV0IGZpbmFsQ2hhdE5hbWUgPSAnTm92YSBDb252ZXJzYSc7XG4gICAgICBpZiAoZmlyc3RNZXNzYWdlLnRyaW0oKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IHdvcmRzID0gZmlyc3RNZXNzYWdlLnRyaW0oKS5zcGxpdCgnICcpO1xuICAgICAgICBjb25zdCBjaGF0TmFtZSA9IHdvcmRzLnNsaWNlKDAsIE1hdGgubWluKDQsIHdvcmRzLmxlbmd0aCkpLmpvaW4oJyAnKTtcbiAgICAgICAgZmluYWxDaGF0TmFtZSA9IGNoYXROYW1lLmxlbmd0aCA+IDMwID8gY2hhdE5hbWUuc3Vic3RyaW5nKDAsIDMwKSArICcuLi4nIDogY2hhdE5hbWU7XG4gICAgICB9XG5cbiAgICAgIC8vIERhZG9zIHBhcmEgbyBGaXJlc3RvcmVcbiAgICAgIGNvbnN0IGZpcmVzdG9yZURhdGEgPSB7XG4gICAgICAgIGNvbnRleHQ6ICcnLFxuICAgICAgICBjcmVhdGVkQXQ6IG5vdyxcbiAgICAgICAgZm9sZGVySWQ6IG51bGwsXG4gICAgICAgIGZyZXF1ZW5jeVBlbmFsdHk6IDEuMCxcbiAgICAgICAgaXNGaXhlZDogZmFsc2UsXG4gICAgICAgIGxhc3RVcGRhdGVkQXQ6IG5vdyxcbiAgICAgICAgbGFzdFVzZWRNb2RlbDogc2VsZWN0ZWRNb2RlbCxcbiAgICAgICAgbGF0ZXhJbnN0cnVjdGlvbnM6IGZhbHNlLFxuICAgICAgICBtYXhUb2tlbnM6IDIwNDgsXG4gICAgICAgIG5hbWU6IGZpbmFsQ2hhdE5hbWUsXG4gICAgICAgIHBhc3N3b3JkOiAnJyxcbiAgICAgICAgcmVwZXRpdGlvblBlbmFsdHk6IDEuMCxcbiAgICAgICAgc2Vzc2lvblRpbWU6IHtcbiAgICAgICAgICBsYXN0U2Vzc2lvblN0YXJ0OiBub3csXG4gICAgICAgICAgbGFzdFVwZGF0ZWQ6IG5vdyxcbiAgICAgICAgICB0b3RhbFRpbWU6IDBcbiAgICAgICAgfSxcbiAgICAgICAgc3lzdGVtUHJvbXB0OiAnJyxcbiAgICAgICAgdGVtcGVyYXR1cmU6IDEuMCxcbiAgICAgICAgdWx0aW1hTWVuc2FnZW06IGZpcnN0TWVzc2FnZSB8fCAnQW5leG8gZW52aWFkbycsXG4gICAgICAgIHVsdGltYU1lbnNhZ2VtRW06IG5vdyxcbiAgICAgICAgdXBkYXRlZEF0OiBub3dcbiAgICAgIH07XG5cbiAgICAgIC8vIENyaWFyIGRvY3VtZW50byBubyBGaXJlc3RvcmVcbiAgICAgIGF3YWl0IHNldERvYyhkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJuYW1lLCAnY29udmVyc2FzJywgY2hhdElkKSwgZmlyZXN0b3JlRGF0YSk7XG5cbiAgICAgIC8vIENyaWFyIGFycXVpdm8gY2hhdC5qc29uIG5vIFN0b3JhZ2VcbiAgICAgIGNvbnN0IGNoYXRKc29uRGF0YSA9IHtcbiAgICAgICAgaWQ6IGNoYXRJZCxcbiAgICAgICAgbmFtZTogZmluYWxDaGF0TmFtZSxcbiAgICAgICAgbWVzc2FnZXM6IFtdLFxuICAgICAgICBjcmVhdGVkQXQ6IG5vdyxcbiAgICAgICAgbGFzdFVwZGF0ZWQ6IG5vd1xuICAgICAgfTtcblxuICAgICAgY29uc3QgY2hhdEpzb25CbG9iID0gbmV3IEJsb2IoW0pTT04uc3RyaW5naWZ5KGNoYXRKc29uRGF0YSwgbnVsbCwgMildLCB7XG4gICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9qc29uJ1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHN0b3JhZ2VSZWYgPSByZWYoc3RvcmFnZSwgYHVzdWFyaW9zLyR7dXNlcm5hbWV9L2NvbnZlcnNhcy8ke2NoYXRJZH0vY2hhdC5qc29uYCk7XG4gICAgICBhd2FpdCB1cGxvYWRCeXRlcyhzdG9yYWdlUmVmLCBjaGF0SnNvbkJsb2IpO1xuXG4gICAgICBjb25zb2xlLmxvZygnQ2hhdCBjcmlhZG8gYXV0b21hdGljYW1lbnRlOicsIGNoYXRJZCk7XG4gICAgICAvLyBEZWZpbmlyIG8gbm9tZSBkbyBjaGF0IGltZWRpYXRhbWVudGUgYXDDs3MgY3JpYcOnw6NvXG4gICAgICBzZXRDaGF0TmFtZShmaW5hbENoYXROYW1lKTtcbiAgICAgIHJldHVybiBjaGF0SWQ7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjcmlhciBjaGF0IGF1dG9tYXRpY2FtZW50ZTonLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSBhc3luYyAoYXR0YWNobWVudHM/OiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSwgd2ViU2VhcmNoRW5hYmxlZD86IGJvb2xlYW4pID0+IHtcbiAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBDSEFUQVJFQSBIQU5ETEUgU0VORCBNRVNTQUdFID09PScpO1xuICAgIGNvbnNvbGUubG9nKCdNZW5zYWdlbTonLCBtZXNzYWdlKTtcbiAgICBjb25zb2xlLmxvZygnQ2hhdCBOYW1lIGF0dWFsOicsIGNoYXROYW1lKTtcbiAgICBjb25zb2xlLmxvZygnQWN0dWFsIENoYXQgSUQ6JywgYWN0dWFsQ2hhdElkKTtcbiAgICBjb25zb2xlLmxvZygnQ3VycmVudCBDaGF0IChwcm9wKTonLCBjdXJyZW50Q2hhdCk7XG4gICAgY29uc29sZS5sb2coJ0FuZXhvcyByZWNlYmlkb3MgKG5vdm9zKTonLCBhdHRhY2htZW50cz8ubGVuZ3RoIHx8IDApO1xuICAgIGNvbnNvbGUubG9nKCdBbmV4b3Mgbm92b3MgZGV0YWxoZXM6JywgSlNPTi5zdHJpbmdpZnkoYXR0YWNobWVudHMsIG51bGwsIDIpKTtcbiAgICBjb25zb2xlLmxvZygnV2ViIFNlYXJjaCBFbmFibGVkOicsIHdlYlNlYXJjaEVuYWJsZWQpO1xuXG4gICAgLy8gT2J0ZXIgYW5leG9zIGhpc3TDs3JpY29zIGF0aXZvc1xuICAgIGNvbnN0IGhpc3RvcmljYWxBdHRhY2htZW50cyA9IGdldEFsbENoYXRBdHRhY2htZW50cygpLmZpbHRlcihhdHQgPT4gYXR0LmlzQWN0aXZlICE9PSBmYWxzZSk7XG5cbiAgICAvLyBDb21iaW5hciBhbmV4b3Mgbm92b3MgY29tIGFuZXhvcyBoaXN0w7NyaWNvcyBhdGl2b3NcbiAgICBjb25zdCBhbGxBdHRhY2htZW50c1RvU2VuZCA9IFtcbiAgICAgIC4uLihhdHRhY2htZW50cyB8fCBbXSksIC8vIEFuZXhvcyBub3ZvcyAoc2VtcHJlIGluY2x1w61kb3MpXG4gICAgICAuLi5oaXN0b3JpY2FsQXR0YWNobWVudHMgLy8gQW5leG9zIGhpc3TDs3JpY29zIGF0aXZvc1xuICAgIF07XG5cbiAgICAvLyBSZW1vdmVyIGR1cGxpY2F0YXMgYmFzZWFkbyBubyBJRFxuICAgIGNvbnN0IHVuaXF1ZUF0dGFjaG1lbnRzID0gYWxsQXR0YWNobWVudHNUb1NlbmQuZmlsdGVyKChhdHRhY2htZW50LCBpbmRleCwgc2VsZikgPT5cbiAgICAgIGluZGV4ID09PSBzZWxmLmZpbmRJbmRleChhID0+IGEuaWQgPT09IGF0dGFjaG1lbnQuaWQpXG4gICAgKTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5qAIFJFU1VMVEFETyBGSU5BTCAtIEFORVhPUyBRVUUgU0VSw4NPIEVOVklBRE9TIFBBUkEgQSBJQTonLCB1bmlxdWVBdHRhY2htZW50cy5sZW5ndGgpO1xuICAgIHVuaXF1ZUF0dGFjaG1lbnRzLmZvckVhY2goYXR0ID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5qAIEVudmlhbmRvIHBhcmEgSUE6ICR7YXR0LmZpbGVuYW1lfSAoSUQ6ICR7YXR0LmlkfSwgaXNBY3RpdmU6ICR7YXR0LmlzQWN0aXZlfSlgKTtcbiAgICB9KTtcblxuICAgIGlmICh1bmlxdWVBdHRhY2htZW50cy5sZW5ndGggPT09IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKCfinYwgTkVOSFVNIEFORVhPIFNFUsOBIEVOVklBRE8gUEFSQSBBIElBJyk7XG4gICAgfVxuXG4gICAgLy8gRGVidWc6IHZlcmlmaWNhciBtZW5zYWdlbnMgYXR1YWlzIG5vIGVzdGFkb1xuICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUc6IE1FTlNBR0VOUyBBVFVBSVMgTk8gRVNUQURPID09PScpO1xuICAgIGNvbnNvbGUubG9nKCdUb3RhbCBkZSBtZW5zYWdlbnMgbm8gZXN0YWRvOicsIG1lc3NhZ2VzLmxlbmd0aCk7XG4gICAgY29uc3QgbWVzc2FnZXNXaXRoQXR0YWNobWVudHMgPSBtZXNzYWdlcy5maWx0ZXIobXNnID0+IG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoID4gMCk7XG4gICAgY29uc29sZS5sb2coJ01lbnNhZ2VucyBjb20gYW5leG9zIG5vIGVzdGFkbzonLCBtZXNzYWdlc1dpdGhBdHRhY2htZW50cy5sZW5ndGgpO1xuICAgIG1lc3NhZ2VzV2l0aEF0dGFjaG1lbnRzLmZvckVhY2goKG1zZywgaW5kZXgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKGBNZW5zYWdlbSAke2luZGV4ICsgMX0gY29tIGFuZXhvczpgLCB7XG4gICAgICAgIGlkOiBtc2cuaWQsXG4gICAgICAgIHNlbmRlcjogbXNnLnNlbmRlcixcbiAgICAgICAgYXR0YWNobWVudHNDb3VudDogbXNnLmF0dGFjaG1lbnRzPy5sZW5ndGggfHwgMCxcbiAgICAgICAgYXR0YWNobWVudHM6IG1zZy5hdHRhY2htZW50c1xuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICBpZiAoKCFtZXNzYWdlLnRyaW0oKSAmJiAoIWF0dGFjaG1lbnRzIHx8IGF0dGFjaG1lbnRzLmxlbmd0aCA9PT0gMCkpIHx8IGlzTG9hZGluZyB8fCBpc1N0cmVhbWluZykge1xuICAgICAgY29uc29sZS5sb2coJz09PSBERUJVRzogQ09OREnDh8ODTyBERSBSRVRPUk5PID09PScpO1xuICAgICAgY29uc29sZS5sb2coJ01lbnNhZ2VtIHZhemlhOicsICFtZXNzYWdlLnRyaW0oKSk7XG4gICAgICBjb25zb2xlLmxvZygnU2VtIGFuZXhvczonLCAhYXR0YWNobWVudHMgfHwgYXR0YWNobWVudHMubGVuZ3RoID09PSAwKTtcbiAgICAgIGNvbnNvbGUubG9nKCdMb2FkaW5nOicsIGlzTG9hZGluZyk7XG4gICAgICBjb25zb2xlLmxvZygnU3RyZWFtaW5nOicsIGlzU3RyZWFtaW5nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgY29uc3QgdXNlck1lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICBpZDogYWlTZXJ2aWNlLmdlbmVyYXRlTWVzc2FnZUlkKCksXG4gICAgICBjb250ZW50OiBtZXNzYWdlLnRyaW0oKSxcbiAgICAgIHNlbmRlcjogJ3VzZXInLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBhdHRhY2htZW50czogYXR0YWNobWVudHMgfHwgW10sIC8vIFNhbHZhciBhcGVuYXMgYW5leG9zIG5vdm9zIG5hIG1lbnNhZ2VtXG4gICAgfTtcblxuICAgIC8vIFNlIG7Do28gaMOhIGNoYXQgYXR1YWwsIGNyaWFyIHVtIGF1dG9tYXRpY2FtZW50ZVxuICAgIGxldCBjaGF0SWRUb1VzZSA9IGFjdHVhbENoYXRJZDtcbiAgICBpZiAoIWNoYXRJZFRvVXNlKSB7XG4gICAgICBjb25zdCBtZXNzYWdlRm9yQ2hhdCA9IG1lc3NhZ2UudHJpbSgpIHx8IChhdHRhY2htZW50cyAmJiBhdHRhY2htZW50cy5sZW5ndGggPiAwID8gJ0FuZXhvIGVudmlhZG8nIDogJ05vdmEgY29udmVyc2EnKTtcbiAgICAgIGNoYXRJZFRvVXNlID0gYXdhaXQgY3JlYXRlQXV0b0NoYXQobWVzc2FnZUZvckNoYXQpO1xuICAgICAgaWYgKGNoYXRJZFRvVXNlKSB7XG4gICAgICAgIHNldEFjdHVhbENoYXRJZChjaGF0SWRUb1VzZSk7XG4gICAgICAgIC8vIE8gbm9tZSBqw6EgZm9pIGRlZmluaWRvIG5hIGZ1bsOnw6NvIGNyZWF0ZUF1dG9DaGF0LCBtYXMgdmFtb3MgZ2FyYW50aXIgY2FycmVnYW5kbyB0YW1iw6ltXG4gICAgICAgIGxvYWRDaGF0TmFtZShjaGF0SWRUb1VzZSk7XG4gICAgICAgIG9uQ2hhdENyZWF0ZWQ/LihjaGF0SWRUb1VzZSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKCFjaGF0SWRUb1VzZSkge1xuICAgICAgY29uc29sZS5lcnJvcignTsOjbyBmb2kgcG9zc8OtdmVsIGNyaWFyIG91IG9idGVyIGNoYXQgSUQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBBZGljaW9uYXIgbWVuc2FnZW0gZG8gdXN1w6FyaW9cbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IFsuLi5wcmV2LCB1c2VyTWVzc2FnZV0pO1xuICAgIGNvbnN0IGN1cnJlbnRNZXNzYWdlID0gbWVzc2FnZS50cmltKCkgfHwgXCJcIjsgLy8gUGVybWl0aXIgbWVuc2FnZW0gdmF6aWEgc2UgaG91dmVyIGFuZXhvc1xuICAgIHNldE1lc3NhZ2UoJycpO1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRJc1N0cmVhbWluZyh0cnVlKTtcblxuICAgIC8vIFByZXBhcmFyIElEIHBhcmEgYSBtZW5zYWdlbSBkYSBJQSBxdWUgc2Vyw6EgY3JpYWRhIGR1cmFudGUgbyBzdHJlYW1pbmdcbiAgICBjb25zdCBhaU1lc3NhZ2VJZCA9IGFpU2VydmljZS5nZW5lcmF0ZU1lc3NhZ2VJZCgpO1xuICAgIHNldFN0cmVhbWluZ01lc3NhZ2VJZChhaU1lc3NhZ2VJZCk7XG5cbiAgICAvLyBCdXNjYXIgdXNlcm5hbWUgY29ycmV0byBkbyB1c3XDoXJpb1xuICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG5cbiAgICAvLyBFbnZpYXIgcGFyYSBhIElBIChpbmNsdWluZG8gYW5leG9zIGhpc3TDs3JpY29zIGF0aXZvcyArIGFuZXhvcyBub3ZvcylcbiAgICBhd2FpdCBhaVNlcnZpY2Uuc2VuZE1lc3NhZ2VTYWZlKFxuICAgICAge1xuICAgICAgICB1c2VybmFtZTogdXNlcm5hbWUsXG4gICAgICAgIGNoYXRJZDogY2hhdElkVG9Vc2UsXG4gICAgICAgIG1lc3NhZ2U6IGN1cnJlbnRNZXNzYWdlLFxuICAgICAgICBtb2RlbDogc2VsZWN0ZWRNb2RlbCxcbiAgICAgICAgYXR0YWNobWVudHM6IHVuaXF1ZUF0dGFjaG1lbnRzLFxuICAgICAgICB3ZWJTZWFyY2hFbmFibGVkOiB3ZWJTZWFyY2hFbmFibGVkLFxuICAgICAgfSxcbiAgICAgIC8vIG9uQ2h1bmsgLSBjcmlhciBtZW5zYWdlbSBuYSBwcmltZWlyYSBjaHVuayBlIGF0dWFsaXphciBjb20gY2FkYSBjaHVua1xuICAgICAgKGNodW5rOiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiB7XG4gICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgbWVuc2FnZW0gZGEgSUEgasOhIGV4aXN0ZVxuICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVzc2FnZUluZGV4ID0gcHJldi5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gYWlNZXNzYWdlSWQpO1xuXG4gICAgICAgICAgaWYgKGV4aXN0aW5nTWVzc2FnZUluZGV4ICE9PSAtMSkge1xuICAgICAgICAgICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VtIGV4aXN0ZW50ZVxuICAgICAgICAgICAgcmV0dXJuIHByZXYubWFwKG1zZyA9PlxuICAgICAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogbXNnLmNvbnRlbnQgKyBjaHVuayB9XG4gICAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIENyaWFyIG5vdmEgbWVuc2FnZW0gZGEgSUEgbmEgcHJpbWVpcmEgY2h1bmtcbiAgICAgICAgICAgIC8vIFJlbW92ZXIgbyBpbmRpY2Fkb3IgZGUgbG9hZGluZyBhc3NpbSBxdWUgYSBwcmltZWlyYSBjaHVuayBjaGVnYXJcbiAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG5cbiAgICAgICAgICAgIGNvbnN0IGFpTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgICAgICAgaWQ6IGFpTWVzc2FnZUlkLFxuICAgICAgICAgICAgICBjb250ZW50OiBjaHVuayxcbiAgICAgICAgICAgICAgc2VuZGVyOiAnYWknLFxuICAgICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgICAgaGFzV2ViU2VhcmNoOiB3ZWJTZWFyY2hFbmFibGVkLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiBbLi4ucHJldiwgYWlNZXNzYWdlXTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfSxcbiAgICAgIC8vIG9uQ29tcGxldGUgLSBmaW5hbGl6YXIgc3RyZWFtaW5nXG4gICAgICAoZnVsbFJlc3BvbnNlOiBzdHJpbmcpID0+IHtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBmdWxsUmVzcG9uc2UgfVxuICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgKSk7XG4gICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuXG4gICAgICAgIC8vIEF0dWFsaXphciBzYWxkbyBkbyBPcGVuUm91dGVyIGFww7NzIGEgcmVzcG9zdGFcbiAgICAgICAgaWYgKG9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UpIHtcbiAgICAgICAgICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlKCk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAvLyBvbkVycm9yIC0gdHJhdGFyIGVycm9zXG4gICAgICAoZXJyb3I6IHN0cmluZykgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvIG5hIElBOicsIGVycm9yKTtcbiAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICBtc2cuaWQgPT09IGFpTWVzc2FnZUlkXG4gICAgICAgICAgICA/IHsgLi4ubXNnLCBjb250ZW50OiBg4p2MIEVycm86ICR7ZXJyb3J9YCB9XG4gICAgICAgICAgICA6IG1zZ1xuICAgICAgICApKTtcbiAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gICAgICB9XG4gICAgKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhbmNlbGFyIHN0cmVhbWluZ1xuICBjb25zdCBoYW5kbGVDYW5jZWxTdHJlYW1pbmcgPSAoKSA9PiB7XG4gICAgYWlTZXJ2aWNlLmNhbmNlbFJlcXVlc3QoKTtcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBjYXJyZWdhciBvIG5vbWUgZG8gY2hhdCBkbyBGaXJlc3RvcmVcbiAgY29uc3QgbG9hZENoYXROYW1lID0gYXN5bmMgKGNoYXRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUc6IENBUlJFR0FORE8gTk9NRSBETyBDSEFUID09PScpO1xuICAgICAgY29uc29sZS5sb2coJ0NoYXQgSUQ6JywgY2hhdElkKTtcblxuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnNvbGUubG9nKCdVc2VybmFtZTonLCB1c2VybmFtZSk7XG5cbiAgICAgIGNvbnN0IGNoYXREb2MgPSBhd2FpdCBnZXREb2MoZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VybmFtZSwgJ2NvbnZlcnNhcycsIGNoYXRJZCkpO1xuXG4gICAgICBpZiAoY2hhdERvYy5leGlzdHMoKSkge1xuICAgICAgICBjb25zdCBjaGF0RGF0YSA9IGNoYXREb2MuZGF0YSgpO1xuICAgICAgICBjb25zdCBjaGF0TmFtZSA9IGNoYXREYXRhLm5hbWUgfHwgJ0NvbnZlcnNhIHNlbSBub21lJztcbiAgICAgICAgY29uc29sZS5sb2coJ05vbWUgZG8gY2hhdCBjYXJyZWdhZG86JywgY2hhdE5hbWUpO1xuICAgICAgICBzZXRDaGF0TmFtZShjaGF0TmFtZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBuw6NvIGVuY29udHJhZG8gbm8gRmlyZXN0b3JlJyk7XG4gICAgICAgIHNldENoYXROYW1lKCdDb252ZXJzYSBuw6NvIGVuY29udHJhZGEnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjYXJyZWdhciBub21lIGRvIGNoYXQ6JywgZXJyb3IpO1xuICAgICAgc2V0Q2hhdE5hbWUoJ0Vycm8gYW8gY2FycmVnYXIgbm9tZScpO1xuICAgIH1cbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG1lbnNhZ2VucyBleGlzdGVudGVzIGRvIGNoYXRcbiAgY29uc3QgbG9hZENoYXRNZXNzYWdlcyA9IGFzeW5jIChjaGF0SWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghdXNlcj8uZW1haWwpIHJldHVybjtcblxuICAgIHNldElzTG9hZGluZ0NoYXQodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBjaGF0TWVzc2FnZXMgPSBhd2FpdCBhaVNlcnZpY2UubG9hZENoYXRNZXNzYWdlcyh1c2VybmFtZSwgY2hhdElkKTtcblxuICAgICAgLy8gRGVidWc6IHZlcmlmaWNhciBtZW5zYWdlbnMgY2FycmVnYWRhc1xuICAgICAgY29uc29sZS5sb2coJz09PSBERUJVRzogTUVOU0FHRU5TIENBUlJFR0FEQVMgRE8gU1RPUkFHRSA9PT0nKTtcbiAgICAgIGNvbnNvbGUubG9nKCdUb3RhbCBkZSBtZW5zYWdlbnM6JywgY2hhdE1lc3NhZ2VzLmxlbmd0aCk7XG4gICAgICBjaGF0TWVzc2FnZXMuZm9yRWFjaCgobXNnLCBpbmRleCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhgTWVuc2FnZW0gJHtpbmRleCArIDF9OmAsIHtcbiAgICAgICAgICBpZDogbXNnLmlkLFxuICAgICAgICAgIHJvbGU6IG1zZy5yb2xlLFxuICAgICAgICAgIGhhc0F0dGFjaG1lbnRzOiAhIShtc2cuYXR0YWNobWVudHMgJiYgbXNnLmF0dGFjaG1lbnRzLmxlbmd0aCA+IDApLFxuICAgICAgICAgIGF0dGFjaG1lbnRzQ291bnQ6IG1zZy5hdHRhY2htZW50cz8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgYXR0YWNobWVudHM6IG1zZy5hdHRhY2htZW50c1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBjb252ZXJ0ZWRNZXNzYWdlcyA9IGFpU2VydmljZS5jb252ZXJ0RnJvbUFJRm9ybWF0KGNoYXRNZXNzYWdlcyk7XG5cbiAgICAgIC8vIERlYnVnOiB2ZXJpZmljYXIgbWVuc2FnZW5zIGNvbnZlcnRpZGFzXG4gICAgICBjb25zb2xlLmxvZygnPT09IERFQlVHOiBNRU5TQUdFTlMgQ09OVkVSVElEQVMgPT09Jyk7XG4gICAgICBjb25zb2xlLmxvZygnVG90YWwgZGUgbWVuc2FnZW5zIGNvbnZlcnRpZGFzOicsIGNvbnZlcnRlZE1lc3NhZ2VzLmxlbmd0aCk7XG4gICAgICBjb252ZXJ0ZWRNZXNzYWdlcy5mb3JFYWNoKChtc2csIGluZGV4KSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKGBNZW5zYWdlbSBjb252ZXJ0aWRhICR7aW5kZXggKyAxfTpgLCB7XG4gICAgICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgICAgICBzZW5kZXI6IG1zZy5zZW5kZXIsXG4gICAgICAgICAgaGFzQXR0YWNobWVudHM6ICEhKG1zZy5hdHRhY2htZW50cyAmJiBtc2cuYXR0YWNobWVudHMubGVuZ3RoID4gMCksXG4gICAgICAgICAgYXR0YWNobWVudHNDb3VudDogbXNnLmF0dGFjaG1lbnRzPy5sZW5ndGggfHwgMCxcbiAgICAgICAgICBhdHRhY2htZW50czogbXNnLmF0dGFjaG1lbnRzXG4gICAgICAgIH0pO1xuICAgICAgfSk7XG5cbiAgICAgIHNldE1lc3NhZ2VzKGNvbnZlcnRlZE1lc3NhZ2VzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBjYXJyZWdhciBtZW5zYWdlbnMgZG8gY2hhdDonLCBlcnJvcik7XG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZ0NoYXQoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBDYXJyZWdhciDDumx0aW1vIG1vZGVsbyB1c2FkbyBxdWFuZG8gbyBjb21wb25lbnRlIG1vbnRhclxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyKSB7XG4gICAgICBsb2FkTGFzdFVzZWRNb2RlbCgpO1xuICAgIH1cbiAgfSwgW3VzZXJdKTtcblxuICAvLyBDYXJyZWdhciBtZW5zYWdlbnMgcXVhbmRvIG8gY2hhdCBhdHVhbCBtdWRhclxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCc9PT0gREVCVUc6IFVTRUVGRkVDVCBDVVJSRU5UQ0hBVCBNVURPVSA9PT0nKTtcbiAgICBjb25zb2xlLmxvZygnY3VycmVudENoYXQ6JywgY3VycmVudENoYXQpO1xuICAgIGNvbnNvbGUubG9nKCdhY3R1YWxDaGF0SWQ6JywgYWN0dWFsQ2hhdElkKTtcblxuICAgIGlmIChjdXJyZW50Q2hhdCAmJiBjdXJyZW50Q2hhdCAhPT0gYWN0dWFsQ2hhdElkKSB7XG4gICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW5kbyBjaGF0IGRpZmVyZW50ZTonLCBjdXJyZW50Q2hhdCk7XG4gICAgICBzZXRBY3R1YWxDaGF0SWQoY3VycmVudENoYXQpO1xuICAgICAgc2V0SXNMb2FkaW5nQ2hhdCh0cnVlKTtcbiAgICAgIC8vIExpbXBhciBtZW5zYWdlbnMgaW1lZGlhdGFtZW50ZSBwYXJhIG1vc3RyYXIgbyBlc3RhZG8gZGUgY2FycmVnYW1lbnRvXG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGN1cnJlbnRDaGF0KTtcbiAgICAgIGxvYWRDaGF0TmFtZShjdXJyZW50Q2hhdCk7XG4gICAgfSBlbHNlIGlmICghY3VycmVudENoYXQpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdOZW5odW0gY2hhdCBzZWxlY2lvbmFkbywgcmVzZXRhbmRvIHBhcmEgTm92YSBDb252ZXJzYScpO1xuICAgICAgc2V0QWN0dWFsQ2hhdElkKG51bGwpO1xuICAgICAgc2V0TWVzc2FnZXMoW10pO1xuICAgICAgc2V0Q2hhdE5hbWUoJ05vdmEgQ29udmVyc2EnKTtcbiAgICAgIHNldElzTG9hZGluZ0NoYXQoZmFsc2UpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZygnQ2hhdCBhdHVhbCBuw6NvIG11ZG91LCBtYW50ZW5kbyBlc3RhZG8nKTtcbiAgICB9XG4gIH0sIFtjdXJyZW50Q2hhdCwgdXNlcj8uZW1haWxdKTtcblxuICAvLyBGdW7Dp8O1ZXMgcGFyYSBtYW5pcHVsYXIgbWVuc2FnZW5zXG4gIGNvbnN0IGhhbmRsZURlbGV0ZU1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWFjdHVhbENoYXRJZCB8fCAhdXNlcj8uZW1haWwpIHJldHVybjtcblxuICAgIC8vIFJlbW92ZXIgdmlzdWFsbWVudGUgcHJpbWVpcm8gcGFyYSBtZWxob3IgVVhcbiAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYuZmlsdGVyKG1zZyA9PiBtc2cuaWQgIT09IG1lc3NhZ2VJZCkpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBzdWNjZXNzID0gYXdhaXQgYWlTZXJ2aWNlLmRlbGV0ZU1lc3NhZ2UodXNlcm5hbWUsIGFjdHVhbENoYXRJZCwgbWVzc2FnZUlkKTtcblxuICAgICAgaWYgKCFzdWNjZXNzKSB7XG4gICAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIGEgbWVuc2FnZW1cbiAgICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWxoYSBhbyBkZWxldGFyIG1lbnNhZ2VtIG5vIHNlcnZpZG9yJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIGEgbWVuc2FnZW1cbiAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZGVsZXRhciBtZW5zYWdlbTonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlZ2VuZXJhdGVNZXNzYWdlID0gYXN5bmMgKG1lc3NhZ2VJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFhY3R1YWxDaGF0SWQgfHwgIXVzZXI/LmVtYWlsKSByZXR1cm47XG5cbiAgICBjb25zdCBtZXNzYWdlSW5kZXggPSBtZXNzYWdlcy5maW5kSW5kZXgobXNnID0+IG1zZy5pZCA9PT0gbWVzc2FnZUlkKTtcbiAgICBpZiAobWVzc2FnZUluZGV4ID09PSAtMSkgcmV0dXJuO1xuXG4gICAgY29uc3QgbWVzc2FnZVRvUmVnZW5lcmF0ZSA9IG1lc3NhZ2VzW21lc3NhZ2VJbmRleF07XG5cbiAgICAvLyBSZW1vdmVyIGFwZW5hcyBhcyBtZW5zYWdlbnMgQVDDk1MgYSBtZW5zYWdlbSBzZWxlY2lvbmFkYSAobsOjbyBpbmNsdWluZG8gZWxhKVxuICAgIGNvbnN0IG1lc3NhZ2VzQmVmb3JlUmVnZW5lcmF0aW9uID0gbWVzc2FnZXMuc2xpY2UoMCwgbWVzc2FnZUluZGV4ICsgMSk7XG4gICAgc2V0TWVzc2FnZXMobWVzc2FnZXNCZWZvcmVSZWdlbmVyYXRpb24pO1xuXG4gICAgLy8gUHJlcGFyYXIgbyBjb250ZcO6ZG8gZGEgbWVuc2FnZW0gcGFyYSByZWdlbmVyYXJcbiAgICBzZXRNZXNzYWdlKG1lc3NhZ2VUb1JlZ2VuZXJhdGUuY29udGVudCk7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldElzU3RyZWFtaW5nKHRydWUpO1xuXG4gICAgLy8gUHJlcGFyYXIgSUQgcGFyYSBhIG5vdmEgbWVuc2FnZW0gZGEgSUEgcXVlIHNlcsOhIGNyaWFkYSBkdXJhbnRlIG8gc3RyZWFtaW5nXG4gICAgY29uc3QgYWlNZXNzYWdlSWQgPSBhaVNlcnZpY2UuZ2VuZXJhdGVNZXNzYWdlSWQoKTtcbiAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQoYWlNZXNzYWdlSWQpO1xuXG4gICAgLy8gQnVzY2FyIHVzZXJuYW1lIGNvcnJldG8gZG8gdXN1w6FyaW9cbiAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIERlbGV0YXIgYXBlbmFzIGFzIG1lbnNhZ2VucyBQT1NURVJJT1JFUyBkbyBGaXJlYmFzZSBTdG9yYWdlIChuw6NvIGEgbWVuc2FnZW0gYXR1YWwpXG4gICAgICBmb3IgKGxldCBpID0gbWVzc2FnZUluZGV4ICsgMTsgaSA8IG1lc3NhZ2VzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGNvbnN0IG1zZ1RvRGVsZXRlID0gbWVzc2FnZXNbaV07XG4gICAgICAgIGF3YWl0IGFpU2VydmljZS5kZWxldGVNZXNzYWdlKHVzZXJuYW1lLCBhY3R1YWxDaGF0SWQsIG1zZ1RvRGVsZXRlLmlkKTtcbiAgICAgIH1cblxuICAgICAgLy8gRW52aWFyIHBhcmEgYSBJQSBwYXJhIHJlZ2VuZXJhclxuICAgICAgYXdhaXQgYWlTZXJ2aWNlLnNlbmRNZXNzYWdlU2FmZShcbiAgICAgICAge1xuICAgICAgICAgIHVzZXJuYW1lOiB1c2VybmFtZSxcbiAgICAgICAgICBjaGF0SWQ6IGFjdHVhbENoYXRJZCxcbiAgICAgICAgICBtZXNzYWdlOiBtZXNzYWdlVG9SZWdlbmVyYXRlLmNvbnRlbnQsXG4gICAgICAgICAgbW9kZWw6IHNlbGVjdGVkTW9kZWwsXG4gICAgICAgICAgaXNSZWdlbmVyYXRpb246IHRydWUsXG4gICAgICAgIH0sXG4gICAgICAgIC8vIG9uQ2h1bmsgLSBjcmlhciBtZW5zYWdlbSBuYSBwcmltZWlyYSBjaHVuayBlIGF0dWFsaXphciBjb20gY2FkYSBjaHVua1xuICAgICAgICAoY2h1bms6IHN0cmluZykgPT4ge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4ge1xuICAgICAgICAgICAgLy8gVmVyaWZpY2FyIHNlIGEgbWVuc2FnZW0gZGEgSUEgasOhIGV4aXN0ZVxuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdNZXNzYWdlSW5kZXggPSBwcmV2LmZpbmRJbmRleChtc2cgPT4gbXNnLmlkID09PSBhaU1lc3NhZ2VJZCk7XG5cbiAgICAgICAgICAgIGlmIChleGlzdGluZ01lc3NhZ2VJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgLy8gQXR1YWxpemFyIG1lbnNhZ2VtIGV4aXN0ZW50ZVxuICAgICAgICAgICAgICByZXR1cm4gcHJldi5tYXAobXNnID0+XG4gICAgICAgICAgICAgICAgbXNnLmlkID09PSBhaU1lc3NhZ2VJZFxuICAgICAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogbXNnLmNvbnRlbnQgKyBjaHVuayB9XG4gICAgICAgICAgICAgICAgICA6IG1zZ1xuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgLy8gQ3JpYXIgbm92YSBtZW5zYWdlbSBkYSBJQSBuYSBwcmltZWlyYSBjaHVua1xuICAgICAgICAgICAgICAvLyBSZW1vdmVyIG8gaW5kaWNhZG9yIGRlIGxvYWRpbmcgYXNzaW0gcXVlIGEgcHJpbWVpcmEgY2h1bmsgY2hlZ2FyXG4gICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG5cbiAgICAgICAgICAgICAgY29uc3QgYWlNZXNzYWdlOiBNZXNzYWdlID0ge1xuICAgICAgICAgICAgICAgIGlkOiBhaU1lc3NhZ2VJZCxcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjaHVuayxcbiAgICAgICAgICAgICAgICBzZW5kZXI6ICdhaScsXG4gICAgICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgaGFzV2ViU2VhcmNoOiBmYWxzZSwgLy8gUmVnZW5lcmHDp8OjbyBuw6NvIHVzYSB3ZWIgc2VhcmNoIHBvciBwYWRyw6NvXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIHJldHVybiBbLi4ucHJldiwgYWlNZXNzYWdlXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgLy8gb25Db21wbGV0ZSAtIGZpbmFsaXphciBzdHJlYW1pbmdcbiAgICAgICAgKGZ1bGxSZXNwb25zZTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogZnVsbFJlc3BvbnNlIH1cbiAgICAgICAgICAgICAgOiBtc2dcbiAgICAgICAgICApKTtcbiAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgIHNldElzU3RyZWFtaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlSWQobnVsbCk7XG4gICAgICAgICAgc2V0TWVzc2FnZSgnJyk7IC8vIExpbXBhciBvIGNhbXBvIGRlIGlucHV0XG5cbiAgICAgICAgICAvLyBBdHVhbGl6YXIgc2FsZG8gZG8gT3BlblJvdXRlciBhcMOzcyBhIHJlZ2VuZXJhw6fDo29cbiAgICAgICAgICBpZiAob25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZSkge1xuICAgICAgICAgICAgb25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgLy8gb25FcnJvciAtIHRyYXRhciBlcnJvc1xuICAgICAgICAoZXJyb3I6IHN0cmluZykgPT4ge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gbmEgcmVnZW5lcmHDp8OjbzonLCBlcnJvcik7XG4gICAgICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgICAgICAgIG1zZy5pZCA9PT0gYWlNZXNzYWdlSWRcbiAgICAgICAgICAgICAgPyB7IC4uLm1zZywgY29udGVudDogYOKdjCBFcnJvIG5hIHJlZ2VuZXJhw6fDo286ICR7ZXJyb3J9YCB9XG4gICAgICAgICAgICAgIDogbXNnXG4gICAgICAgICAgKSk7XG4gICAgICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgICAgIHNldE1lc3NhZ2UoJycpOyAvLyBMaW1wYXIgbyBjYW1wbyBkZSBpbnB1dFxuICAgICAgICB9XG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIHJlZ2VuZXJhciBtZW5zYWdlbTonLCBlcnJvcik7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xuICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZUlkKG51bGwpO1xuICAgICAgc2V0TWVzc2FnZSgnJyk7IC8vIExpbXBhciBvIGNhbXBvIGRlIGlucHV0XG5cbiAgICAgIC8vIFJlY2FycmVnYXIgbWVuc2FnZW5zIGVtIGNhc28gZGUgZXJyb1xuICAgICAgbG9hZENoYXRNZXNzYWdlcyhhY3R1YWxDaGF0SWQpO1xuICAgIH1cbiAgfTtcblxuXG5cbiAgY29uc3QgaGFuZGxlRWRpdE1lc3NhZ2UgPSBhc3luYyAobWVzc2FnZUlkOiBzdHJpbmcsIG5ld0NvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghYWN0dWFsQ2hhdElkIHx8ICF1c2VyPy5lbWFpbCkgcmV0dXJuO1xuXG4gICAgLy8gQXR1YWxpemFyIHZpc3VhbG1lbnRlIHByaW1laXJvIHBhcmEgbWVsaG9yIFVYXG4gICAgc2V0TWVzc2FnZXMocHJldiA9PiBwcmV2Lm1hcChtc2cgPT5cbiAgICAgIG1zZy5pZCA9PT0gbWVzc2FnZUlkID8geyAuLi5tc2csIGNvbnRlbnQ6IG5ld0NvbnRlbnQgfSA6IG1zZ1xuICAgICkpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBzdWNjZXNzID0gYXdhaXQgYWlTZXJ2aWNlLnVwZGF0ZU1lc3NhZ2UodXNlcm5hbWUsIGFjdHVhbENoYXRJZCwgbWVzc2FnZUlkLCBuZXdDb250ZW50KTtcblxuICAgICAgaWYgKCFzdWNjZXNzKSB7XG4gICAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIG8gY29udGXDumRvIG9yaWdpbmFsXG4gICAgICAgIGxvYWRDaGF0TWVzc2FnZXMoYWN0dWFsQ2hhdElkKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignRmFsaGEgYW8gYXR1YWxpemFyIG1lbnNhZ2VtIG5vIHNlcnZpZG9yJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIFNlIGZhbGhvdSwgcmVzdGF1cmFyIG8gY29udGXDumRvIG9yaWdpbmFsXG4gICAgICBsb2FkQ2hhdE1lc3NhZ2VzKGFjdHVhbENoYXRJZCk7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGF0dWFsaXphciBtZW5zYWdlbTonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvcHlNZXNzYWdlID0gKGNvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KGNvbnRlbnQpLnRoZW4oKCkgPT4ge1xuICAgICAgY29uc29sZS5sb2coJ01lbnNhZ2VtIGNvcGlhZGEgcGFyYSBhIMOhcmVhIGRlIHRyYW5zZmVyw6puY2lhJyk7XG4gICAgfSk7XG4gIH07XG5cbiAgLy8gRnVuw6fDtWVzIGRlIG5hdmVnYcOnw6NvXG4gIGNvbnN0IGhhbmRsZVNjcm9sbFRvVG9wID0gKCkgPT4ge1xuICAgIGNoYXRJbnRlcmZhY2VSZWYuY3VycmVudD8uc2Nyb2xsVG8oeyB0b3A6IDAsIGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTY3JvbGxUb0JvdHRvbSA9ICgpID0+IHtcbiAgICBjaGF0SW50ZXJmYWNlUmVmLmN1cnJlbnQ/LnNjcm9sbFRvKHtcbiAgICAgIHRvcDogY2hhdEludGVyZmFjZVJlZi5jdXJyZW50LnNjcm9sbEhlaWdodCxcbiAgICAgIGJlaGF2aW9yOiAnc21vb3RoJ1xuICAgIH0pO1xuICB9O1xuXG5cblxuICAvLyBGdW7Dp8OjbyBwYXJhIGNvbnZlcnRlciBtZW5zYWdlbnMgcGFyYSBvIGZvcm1hdG8gQ2hhdE1lc3NhZ2VcbiAgY29uc3QgY29udmVydFRvQ2hhdE1lc3NhZ2VzID0gKG1lc3NhZ2VzOiBNZXNzYWdlW10pOiBDaGF0TWVzc2FnZVtdID0+IHtcbiAgICByZXR1cm4gbWVzc2FnZXMubWFwKG1zZyA9PiAoe1xuICAgICAgaWQ6IG1zZy5pZCxcbiAgICAgIGNvbnRlbnQ6IG1zZy5jb250ZW50LFxuICAgICAgcm9sZTogbXNnLnNlbmRlciA9PT0gJ3VzZXInID8gJ3VzZXInIDogJ2Fzc2lzdGFudCcsXG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKG1zZy50aW1lc3RhbXApLmdldFRpbWUoKSxcbiAgICAgIGlzRmF2b3JpdGU6IG1zZy5pc0Zhdm9yaXRlIHx8IGZhbHNlLFxuICAgICAgYXR0YWNobWVudHM6IG1zZy5hdHRhY2htZW50cyB8fCBbXVxuICAgIH0pKTtcbiAgfTtcblxuICAvLyBGdW7Dp8OjbyBwYXJhIGFicmlyIG8gbW9kYWwgZGUgZG93bmxvYWRcbiAgY29uc3QgaGFuZGxlRG93bmxvYWRNb2RhbCA9ICgpID0+IHtcbiAgICBzZXRJc0Rvd25sb2FkTW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgYWJyaXIgbyBtb2RhbCBkZSBhbmV4b3NcbiAgY29uc3QgaGFuZGxlQXR0YWNobWVudHNNb2RhbCA9ICgpID0+IHtcbiAgICBzZXRJc0F0dGFjaG1lbnRzTW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgb2J0ZXIgdG9kb3Mgb3MgYW5leG9zIGRvIGNoYXRcbiAgY29uc3QgZ2V0QWxsQ2hhdEF0dGFjaG1lbnRzID0gKCk6IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdID0+IHtcbiAgICBjb25zdCBhbGxBdHRhY2htZW50czogaW1wb3J0KCdAL2xpYi90eXBlcy9jaGF0JykuQXR0YWNobWVudE1ldGFkYXRhW10gPSBbXTtcblxuICAgIG1lc3NhZ2VzLmZvckVhY2gobWVzc2FnZSA9PiB7XG4gICAgICBpZiAobWVzc2FnZS5hdHRhY2htZW50cyAmJiBtZXNzYWdlLmF0dGFjaG1lbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgYWxsQXR0YWNobWVudHMucHVzaCguLi5tZXNzYWdlLmF0dGFjaG1lbnRzKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIC8vIFJlbW92ZXIgZHVwbGljYXRhcyBiYXNlYWRvIG5vIElEXG4gICAgY29uc3QgdW5pcXVlQXR0YWNobWVudHMgPSBhbGxBdHRhY2htZW50cy5maWx0ZXIoKGF0dGFjaG1lbnQsIGluZGV4LCBzZWxmKSA9PlxuICAgICAgaW5kZXggPT09IHNlbGYuZmluZEluZGV4KGEgPT4gYS5pZCA9PT0gYXR0YWNobWVudC5pZClcbiAgICApO1xuXG4gICAgcmV0dXJuIHVuaXF1ZUF0dGFjaG1lbnRzO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgc2FsdmFyIG8gZXN0YWRvIGRvcyBhbmV4b3Mgbm8gRmlyZWJhc2UgU3RvcmFnZVxuICBjb25zdCBzYXZlQXR0YWNobWVudFN0YXRlcyA9IGFzeW5jICh1cGRhdGVkTWVzc2FnZXM6IE1lc3NhZ2VbXSkgPT4ge1xuICAgIGlmICghY3VycmVudFVzZXJuYW1lIHx8ICFhY3R1YWxDaGF0SWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICAvLyBQcmVwYXJhciBkYWRvcyBkbyBjaGF0IHBhcmEgc2FsdmFyXG4gICAgICBjb25zdCBjaGF0RGF0YSA9IHtcbiAgICAgICAgaWQ6IGFjdHVhbENoYXRJZCxcbiAgICAgICAgbmFtZTogY2hhdE5hbWUgfHwgJ0NoYXQnLFxuICAgICAgICBtZXNzYWdlczogdXBkYXRlZE1lc3NhZ2VzLm1hcChtc2cgPT4gKHtcbiAgICAgICAgICBpZDogbXNnLmlkLFxuICAgICAgICAgIGNvbnRlbnQ6IG1zZy5jb250ZW50LFxuICAgICAgICAgIHJvbGU6IG1zZy5zZW5kZXIgPT09ICd1c2VyJyA/ICd1c2VyJyA6ICdhc3Npc3RhbnQnLFxuICAgICAgICAgIHRpbWVzdGFtcDogbXNnLnRpbWVzdGFtcCxcbiAgICAgICAgICBpc0Zhdm9yaXRlOiBtc2cuaXNGYXZvcml0ZSxcbiAgICAgICAgICBhdHRhY2htZW50czogbXNnLmF0dGFjaG1lbnRzXG4gICAgICAgIH0pKSxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGxhc3RVcGRhdGVkOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH07XG5cbiAgICAgIC8vIFNhbHZhciBubyBGaXJlYmFzZSBTdG9yYWdlXG4gICAgICBjb25zdCBjaGF0SnNvbkJsb2IgPSBuZXcgQmxvYihbSlNPTi5zdHJpbmdpZnkoY2hhdERhdGEsIG51bGwsIDIpXSwge1xuICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBjaGF0SnNvblJlZiA9IHJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHtjdXJyZW50VXNlcm5hbWV9L2NvbnZlcnNhcy8ke2FjdHVhbENoYXRJZH0vY2hhdC5qc29uYCk7XG4gICAgICBhd2FpdCB1cGxvYWRCeXRlcyhjaGF0SnNvblJlZiwgY2hhdEpzb25CbG9iKTtcblxuICAgICAgY29uc29sZS5sb2coJ+KchSBFc3RhZG8gZG9zIGFuZXhvcyBzYWx2byBubyBGaXJlYmFzZSBTdG9yYWdlJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TgSBEYWRvcyBzYWx2b3M6Jywge1xuICAgICAgICBjaGF0SWQ6IGFjdHVhbENoYXRJZCxcbiAgICAgICAgdG90YWxNZXNzYWdlczogY2hhdERhdGEubWVzc2FnZXMubGVuZ3RoLFxuICAgICAgICBtZXNzYWdlc1dpdGhBdHRhY2htZW50czogY2hhdERhdGEubWVzc2FnZXMuZmlsdGVyKG1zZyA9PiBtc2cuYXR0YWNobWVudHM/Lmxlbmd0aCA+IDApLmxlbmd0aFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvIGFvIHNhbHZhciBlc3RhZG8gZG9zIGFuZXhvczonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgYWx0ZXJuYXIgbyBlc3RhZG8gYXRpdm8gZGUgdW0gYW5leG9cbiAgY29uc3QgaGFuZGxlVG9nZ2xlQXR0YWNobWVudCA9IChhdHRhY2htZW50SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldE1lc3NhZ2VzKHByZXZNZXNzYWdlcyA9PiB7XG4gICAgICBjb25zdCB1cGRhdGVkTWVzc2FnZXMgPSBwcmV2TWVzc2FnZXMubWFwKG1lc3NhZ2UgPT4ge1xuICAgICAgICBpZiAobWVzc2FnZS5hdHRhY2htZW50cyAmJiBtZXNzYWdlLmF0dGFjaG1lbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBjb25zdCB1cGRhdGVkQXR0YWNobWVudHMgPSBtZXNzYWdlLmF0dGFjaG1lbnRzLm1hcChhdHRhY2htZW50ID0+IHtcbiAgICAgICAgICAgIGlmIChhdHRhY2htZW50LmlkID09PSBhdHRhY2htZW50SWQpIHtcbiAgICAgICAgICAgICAgLy8gU2UgaXNBY3RpdmUgbsOjbyBlc3TDoSBkZWZpbmlkbywgY29uc2lkZXJhciBjb21vIHRydWUgKGF0aXZvIHBvciBwYWRyw6NvKVxuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50U3RhdGUgPSBhdHRhY2htZW50LmlzQWN0aXZlICE9PSBmYWxzZTtcbiAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uYXR0YWNobWVudCwgaXNBY3RpdmU6ICFjdXJyZW50U3RhdGUgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBhdHRhY2htZW50O1xuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiB7IC4uLm1lc3NhZ2UsIGF0dGFjaG1lbnRzOiB1cGRhdGVkQXR0YWNobWVudHMgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbWVzc2FnZTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBTYWx2YXIgbyBlc3RhZG8gYXR1YWxpemFkbyBubyBGaXJlYmFzZSBTdG9yYWdlXG4gICAgICBzYXZlQXR0YWNobWVudFN0YXRlcyh1cGRhdGVkTWVzc2FnZXMpO1xuXG4gICAgICByZXR1cm4gdXBkYXRlZE1lc3NhZ2VzO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgZmlsdHJhciBhbmV4b3MgYXRpdm9zIHBhcmEgZW52aW8gw6AgSUFcbiAgY29uc3QgZ2V0QWN0aXZlQXR0YWNobWVudHMgPSAoYXR0YWNobWVudHM/OiBpbXBvcnQoJ0AvbGliL3R5cGVzL2NoYXQnKS5BdHRhY2htZW50TWV0YWRhdGFbXSk6IGltcG9ydCgnQC9saWIvdHlwZXMvY2hhdCcpLkF0dGFjaG1lbnRNZXRhZGF0YVtdID0+IHtcbiAgICBpZiAoIWF0dGFjaG1lbnRzKSByZXR1cm4gW107XG5cbiAgICAvLyBQYXJhIGFuZXhvcyBub3ZvcyAoc2VtIGlzQWN0aXZlIGRlZmluaWRvKSwgaW5jbHVpciBwb3IgcGFkcsOjb1xuICAgIC8vIFBhcmEgYW5leG9zIGV4aXN0ZW50ZXMsIHZlcmlmaWNhciBzZSBpc0FjdGl2ZSBuw6NvIMOpIGZhbHNlXG4gICAgcmV0dXJuIGF0dGFjaG1lbnRzLmZpbHRlcihhdHRhY2htZW50ID0+IHtcbiAgICAgIC8vIFNlIGlzQWN0aXZlIG7Do28gZXN0w6EgZGVmaW5pZG8gKGFuZXhvIG5vdm8pLCBpbmNsdWlyXG4gICAgICBpZiAoYXR0YWNobWVudC5pc0FjdGl2ZSA9PT0gdW5kZWZpbmVkKSByZXR1cm4gdHJ1ZTtcbiAgICAgIC8vIFNlIGlzQWN0aXZlIGVzdMOhIGRlZmluaWRvLCBpbmNsdWlyIGFwZW5hcyBzZSBuw6NvIGZvciBmYWxzZVxuICAgICAgcmV0dXJuIGF0dGFjaG1lbnQuaXNBY3RpdmUgIT09IGZhbHNlO1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIFNpbmNyb25pemFyIGVzdGFkbyBxdWFuZG8gY3VycmVudENoYXQgbXVkYXJcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBMaW1wYXIgbWVuc2FnZW5zIHF1YW5kbyBtdWRhciBwYXJhIHVtIGNoYXQgZGlmZXJlbnRlIG91IHBhcmEgw6FyZWEgaW5pY2lhbFxuICAgIGlmIChjdXJyZW50Q2hhdCAhPT0gYWN0dWFsQ2hhdElkKSB7XG4gICAgICBzZXRNZXNzYWdlcyhbXSk7XG4gICAgfVxuICAgIHNldEFjdHVhbENoYXRJZChjdXJyZW50Q2hhdCk7XG4gIH0sIFtjdXJyZW50Q2hhdF0pO1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgb2J0ZXIgSURzIGRvcyBhbmV4b3MgYXRpdm9zXG4gIGNvbnN0IGdldEFjdGl2ZUF0dGFjaG1lbnRJZHMgPSAoKTogc3RyaW5nW10gPT4ge1xuICAgIGNvbnN0IGFsbEF0dGFjaG1lbnRzID0gZ2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCk7XG4gICAgcmV0dXJuIGFsbEF0dGFjaG1lbnRzLmZpbHRlcihhdHQgPT4gYXR0LmlzQWN0aXZlICE9PSBmYWxzZSkubWFwKGF0dCA9PiBhdHQuaWQpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBoLXNjcmVlblwiPlxuICAgICAgey8qIFVwcGVyYmFyICovfVxuICAgICAgPFVwcGVyYmFyXG4gICAgICAgIGN1cnJlbnRDaGF0PXtjdXJyZW50Q2hhdH1cbiAgICAgICAgY2hhdE5hbWU9e2NoYXROYW1lfVxuICAgICAgICBhaU1vZGVsPXtzZWxlY3RlZE1vZGVsfVxuICAgICAgICBvbkRvd25sb2FkPXtoYW5kbGVEb3dubG9hZE1vZGFsfVxuICAgICAgICBvbkF0dGFjaG1lbnRzPXtoYW5kbGVBdHRhY2htZW50c01vZGFsfVxuICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgYXR0YWNobWVudHNDb3VudD17Z2V0QWxsQ2hhdEF0dGFjaG1lbnRzKCkubGVuZ3RofVxuICAgICAgICBhaU1ldGFkYXRhPXt7XG4gICAgICAgICAgdXNlZENvVDogZmFsc2VcbiAgICAgICAgfX1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBDaGF0SW50ZXJmYWNlICovfVxuICAgICAgPGRpdiByZWY9e2NoYXRJbnRlcmZhY2VSZWZ9IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4taC0wIG92ZXJmbG93LWhpZGRlblwiIHN0eWxlPXt7IGhlaWdodDogJ2NhbGMoMTAwdmggLSAyMDBweCknIH19PlxuICAgICAgICA8Q2hhdEludGVyZmFjZVxuICAgICAgICAgIG1lc3NhZ2VzPXttZXNzYWdlc31cbiAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICBpc0xvYWRpbmdDaGF0PXtpc0xvYWRpbmdDaGF0fVxuICAgICAgICAgIG9uRGVsZXRlTWVzc2FnZT17aGFuZGxlRGVsZXRlTWVzc2FnZX1cbiAgICAgICAgICBvblJlZ2VuZXJhdGVNZXNzYWdlPXtoYW5kbGVSZWdlbmVyYXRlTWVzc2FnZX1cbiAgICAgICAgICBvbkVkaXRNZXNzYWdlPXtoYW5kbGVFZGl0TWVzc2FnZX1cbiAgICAgICAgICBvbkNvcHlNZXNzYWdlPXtoYW5kbGVDb3B5TWVzc2FnZX1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSW5wdXRCYXIgKi99XG4gICAgICA8SW5wdXRCYXJcbiAgICAgICAgbWVzc2FnZT17bWVzc2FnZX1cbiAgICAgICAgc2V0TWVzc2FnZT17c2V0TWVzc2FnZX1cbiAgICAgICAgb25TZW5kTWVzc2FnZT17aGFuZGxlU2VuZE1lc3NhZ2V9XG4gICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICBzZWxlY3RlZE1vZGVsPXtzZWxlY3RlZE1vZGVsfVxuICAgICAgICBvbk1vZGVsQ2hhbmdlPXtoYW5kbGVNb2RlbENoYW5nZX1cbiAgICAgICAgb25TY3JvbGxUb1RvcD17aGFuZGxlU2Nyb2xsVG9Ub3B9XG4gICAgICAgIG9uU2Nyb2xsVG9Cb3R0b209e2hhbmRsZVNjcm9sbFRvQm90dG9tfVxuICAgICAgICBpc1N0cmVhbWluZz17aXNTdHJlYW1pbmd9XG4gICAgICAgIG9uQ2FuY2VsU3RyZWFtaW5nPXtoYW5kbGVDYW5jZWxTdHJlYW1pbmd9XG4gICAgICAgIG9uT3Blbk1vZGVsTW9kYWw9eygpID0+IHNldElzTW9kZWxNb2RhbE9wZW4odHJ1ZSl9XG4gICAgICAgIHVzZXJuYW1lPXtjdXJyZW50VXNlcm5hbWV9XG4gICAgICAgIGNoYXRJZD17YWN0dWFsQ2hhdElkIHx8IHVuZGVmaW5lZH1cbiAgICAgICAgYWN0aXZlQXR0YWNobWVudHNDb3VudD17Z2V0QWN0aXZlQXR0YWNobWVudElkcygpLmxlbmd0aH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBEb3dubG9hZCBNb2RhbCAqL31cbiAgICAgIDxEb3dubG9hZE1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNEb3dubG9hZE1vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNEb3dubG9hZE1vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgIG1lc3NhZ2VzPXtjb252ZXJ0VG9DaGF0TWVzc2FnZXMobWVzc2FnZXMpfVxuICAgICAgICBjaGF0TmFtZT17Y2hhdE5hbWV9XG4gICAgICAvPlxuXG4gICAgICB7LyogTW9kZWwgU2VsZWN0aW9uIE1vZGFsICovfVxuICAgICAgPE1vZGVsU2VsZWN0aW9uTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc01vZGVsTW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc01vZGVsTW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgY3VycmVudE1vZGVsPXtzZWxlY3RlZE1vZGVsfVxuICAgICAgICBvbk1vZGVsU2VsZWN0PXtoYW5kbGVNb2RlbENoYW5nZX1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBBdHRhY2htZW50cyBNb2RhbCAqL31cbiAgICAgIDxBdHRhY2htZW50c01vZGFsXG4gICAgICAgIGlzT3Blbj17aXNBdHRhY2htZW50c01vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNBdHRhY2htZW50c01vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgIGF0dGFjaG1lbnRzPXtnZXRBbGxDaGF0QXR0YWNobWVudHMoKX1cbiAgICAgICAgYWN0aXZlQXR0YWNobWVudHM9e2dldEFjdGl2ZUF0dGFjaG1lbnRJZHMoKX1cbiAgICAgICAgb25Ub2dnbGVBdHRhY2htZW50PXtoYW5kbGVUb2dnbGVBdHRhY2htZW50fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsImRvYyIsInNldERvYyIsImdldERvYyIsInVwZGF0ZURvYyIsInJlZiIsInVwbG9hZEJ5dGVzIiwiZGIiLCJzdG9yYWdlIiwidXNlQXV0aCIsIlVwcGVyYmFyIiwiQ2hhdEludGVyZmFjZSIsIklucHV0QmFyIiwiRG93bmxvYWRNb2RhbCIsIk1vZGVsU2VsZWN0aW9uTW9kYWwiLCJBdHRhY2htZW50c01vZGFsIiwiYWlTZXJ2aWNlIiwiQ2hhdEFyZWEiLCJjdXJyZW50Q2hhdCIsIm9uQ2hhdENyZWF0ZWQiLCJvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlIiwidXNlciIsIm1lc3NhZ2UiLCJzZXRNZXNzYWdlIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwibWVzc2FnZXMiLCJzZXRNZXNzYWdlcyIsInNlbGVjdGVkTW9kZWwiLCJzZXRTZWxlY3RlZE1vZGVsIiwiYWN0dWFsQ2hhdElkIiwic2V0QWN0dWFsQ2hhdElkIiwiaXNEb3dubG9hZE1vZGFsT3BlbiIsInNldElzRG93bmxvYWRNb2RhbE9wZW4iLCJpc01vZGVsTW9kYWxPcGVuIiwic2V0SXNNb2RlbE1vZGFsT3BlbiIsImlzQXR0YWNobWVudHNNb2RhbE9wZW4iLCJzZXRJc0F0dGFjaG1lbnRzTW9kYWxPcGVuIiwiaXNTdHJlYW1pbmciLCJzZXRJc1N0cmVhbWluZyIsInN0cmVhbWluZ01lc3NhZ2VJZCIsInNldFN0cmVhbWluZ01lc3NhZ2VJZCIsImNoYXROYW1lIiwic2V0Q2hhdE5hbWUiLCJjb25zb2xlIiwibG9nIiwiRXJyb3IiLCJzdGFjayIsImlzTG9hZGluZ0NoYXQiLCJzZXRJc0xvYWRpbmdDaGF0IiwiY3VycmVudFVzZXJuYW1lIiwic2V0Q3VycmVudFVzZXJuYW1lIiwidW5kZWZpbmVkIiwiY2hhdEludGVyZmFjZVJlZiIsImxvYWRVc2VybmFtZSIsImVtYWlsIiwidXNlcm5hbWUiLCJnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUiLCJjb2xsZWN0aW9uIiwicXVlcnkiLCJ3aGVyZSIsImdldERvY3MiLCJ1c3Vhcmlvc1JlZiIsInEiLCJxdWVyeVNuYXBzaG90IiwiZW1wdHkiLCJ1c2VyRG9jIiwiZG9jcyIsInVzZXJEYXRhIiwiZGF0YSIsInNwbGl0IiwiZXJyb3IiLCJzYXZlTGFzdFVzZWRNb2RlbCIsIm1vZGVsSWQiLCJ1c2VyUmVmIiwiZXhpc3RzIiwibGFzdFVzZWRNb2RlbCIsImxhc3RNb2RlbFVwZGF0ZUF0IiwiRGF0ZSIsIm5vdyIsIm1lcmdlIiwibG9hZExhc3RVc2VkTW9kZWwiLCJoYW5kbGVNb2RlbENoYW5nZSIsImNyZWF0ZUF1dG9DaGF0IiwiZmlyc3RNZXNzYWdlIiwidGltZXN0YW1wIiwicmFuZG9tIiwiTWF0aCIsInRvU3RyaW5nIiwic3Vic3RyaW5nIiwiY2hhdElkIiwidG9JU09TdHJpbmciLCJmaW5hbENoYXROYW1lIiwidHJpbSIsImxlbmd0aCIsIndvcmRzIiwic2xpY2UiLCJtaW4iLCJqb2luIiwiZmlyZXN0b3JlRGF0YSIsImNvbnRleHQiLCJjcmVhdGVkQXQiLCJmb2xkZXJJZCIsImZyZXF1ZW5jeVBlbmFsdHkiLCJpc0ZpeGVkIiwibGFzdFVwZGF0ZWRBdCIsImxhdGV4SW5zdHJ1Y3Rpb25zIiwibWF4VG9rZW5zIiwibmFtZSIsInBhc3N3b3JkIiwicmVwZXRpdGlvblBlbmFsdHkiLCJzZXNzaW9uVGltZSIsImxhc3RTZXNzaW9uU3RhcnQiLCJsYXN0VXBkYXRlZCIsInRvdGFsVGltZSIsInN5c3RlbVByb21wdCIsInRlbXBlcmF0dXJlIiwidWx0aW1hTWVuc2FnZW0iLCJ1bHRpbWFNZW5zYWdlbUVtIiwidXBkYXRlZEF0IiwiY2hhdEpzb25EYXRhIiwiaWQiLCJjaGF0SnNvbkJsb2IiLCJCbG9iIiwiSlNPTiIsInN0cmluZ2lmeSIsInR5cGUiLCJzdG9yYWdlUmVmIiwiaGFuZGxlU2VuZE1lc3NhZ2UiLCJhdHRhY2htZW50cyIsIndlYlNlYXJjaEVuYWJsZWQiLCJoaXN0b3JpY2FsQXR0YWNobWVudHMiLCJnZXRBbGxDaGF0QXR0YWNobWVudHMiLCJmaWx0ZXIiLCJhdHQiLCJpc0FjdGl2ZSIsImFsbEF0dGFjaG1lbnRzVG9TZW5kIiwidW5pcXVlQXR0YWNobWVudHMiLCJhdHRhY2htZW50IiwiaW5kZXgiLCJzZWxmIiwiZmluZEluZGV4IiwiYSIsImZvckVhY2giLCJmaWxlbmFtZSIsIm1lc3NhZ2VzV2l0aEF0dGFjaG1lbnRzIiwibXNnIiwic2VuZGVyIiwiYXR0YWNobWVudHNDb3VudCIsInVzZXJNZXNzYWdlIiwiZ2VuZXJhdGVNZXNzYWdlSWQiLCJjb250ZW50IiwiY2hhdElkVG9Vc2UiLCJtZXNzYWdlRm9yQ2hhdCIsImxvYWRDaGF0TmFtZSIsInByZXYiLCJjdXJyZW50TWVzc2FnZSIsImFpTWVzc2FnZUlkIiwic2VuZE1lc3NhZ2VTYWZlIiwibW9kZWwiLCJjaHVuayIsImV4aXN0aW5nTWVzc2FnZUluZGV4IiwibWFwIiwiYWlNZXNzYWdlIiwiaGFzV2ViU2VhcmNoIiwiZnVsbFJlc3BvbnNlIiwiaGFuZGxlQ2FuY2VsU3RyZWFtaW5nIiwiY2FuY2VsUmVxdWVzdCIsImNoYXREb2MiLCJjaGF0RGF0YSIsImxvYWRDaGF0TWVzc2FnZXMiLCJjaGF0TWVzc2FnZXMiLCJyb2xlIiwiaGFzQXR0YWNobWVudHMiLCJjb252ZXJ0ZWRNZXNzYWdlcyIsImNvbnZlcnRGcm9tQUlGb3JtYXQiLCJoYW5kbGVEZWxldGVNZXNzYWdlIiwibWVzc2FnZUlkIiwic3VjY2VzcyIsImRlbGV0ZU1lc3NhZ2UiLCJoYW5kbGVSZWdlbmVyYXRlTWVzc2FnZSIsIm1lc3NhZ2VJbmRleCIsIm1lc3NhZ2VUb1JlZ2VuZXJhdGUiLCJtZXNzYWdlc0JlZm9yZVJlZ2VuZXJhdGlvbiIsImkiLCJtc2dUb0RlbGV0ZSIsImlzUmVnZW5lcmF0aW9uIiwiaGFuZGxlRWRpdE1lc3NhZ2UiLCJuZXdDb250ZW50IiwidXBkYXRlTWVzc2FnZSIsImhhbmRsZUNvcHlNZXNzYWdlIiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwidGhlbiIsImhhbmRsZVNjcm9sbFRvVG9wIiwiY3VycmVudCIsInNjcm9sbFRvIiwidG9wIiwiYmVoYXZpb3IiLCJoYW5kbGVTY3JvbGxUb0JvdHRvbSIsInNjcm9sbEhlaWdodCIsImNvbnZlcnRUb0NoYXRNZXNzYWdlcyIsImdldFRpbWUiLCJpc0Zhdm9yaXRlIiwiaGFuZGxlRG93bmxvYWRNb2RhbCIsImhhbmRsZUF0dGFjaG1lbnRzTW9kYWwiLCJhbGxBdHRhY2htZW50cyIsInB1c2giLCJzYXZlQXR0YWNobWVudFN0YXRlcyIsInVwZGF0ZWRNZXNzYWdlcyIsImNoYXRKc29uUmVmIiwidG90YWxNZXNzYWdlcyIsImhhbmRsZVRvZ2dsZUF0dGFjaG1lbnQiLCJhdHRhY2htZW50SWQiLCJwcmV2TWVzc2FnZXMiLCJ1cGRhdGVkQXR0YWNobWVudHMiLCJjdXJyZW50U3RhdGUiLCJnZXRBY3RpdmVBdHRhY2htZW50cyIsImdldEFjdGl2ZUF0dGFjaG1lbnRJZHMiLCJkaXYiLCJjbGFzc05hbWUiLCJhaU1vZGVsIiwib25Eb3dubG9hZCIsIm9uQXR0YWNobWVudHMiLCJhaU1ldGFkYXRhIiwidXNlZENvVCIsInN0eWxlIiwiaGVpZ2h0Iiwib25EZWxldGVNZXNzYWdlIiwib25SZWdlbmVyYXRlTWVzc2FnZSIsIm9uRWRpdE1lc3NhZ2UiLCJvbkNvcHlNZXNzYWdlIiwib25TZW5kTWVzc2FnZSIsIm9uTW9kZWxDaGFuZ2UiLCJvblNjcm9sbFRvVG9wIiwib25TY3JvbGxUb0JvdHRvbSIsIm9uQ2FuY2VsU3RyZWFtaW5nIiwib25PcGVuTW9kZWxNb2RhbCIsImFjdGl2ZUF0dGFjaG1lbnRzQ291bnQiLCJpc09wZW4iLCJvbkNsb3NlIiwiY3VycmVudE1vZGVsIiwib25Nb2RlbFNlbGVjdCIsImFjdGl2ZUF0dGFjaG1lbnRzIiwib25Ub2dnbGVBdHRhY2htZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\n"));

/***/ })

});