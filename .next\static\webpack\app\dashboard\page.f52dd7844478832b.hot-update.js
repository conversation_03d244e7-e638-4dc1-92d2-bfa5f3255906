"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* harmony import */ var _styles_markdown_advanced_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-advanced.css */ \"(app-pages-browser)/./src/styles/markdown-advanced.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\", hasWebSearch = false } = param;\n    // Função para detectar se o conteúdo contém indicadores de web search\n    const detectWebSearch = (text)=>{\n        const webSearchIndicators = [\n            \"A web search was conducted on\",\n            \"web search results\",\n            \"IMPORTANT: Cite them using markdown links\",\n            \"Example: [nytimes.com]\"\n        ];\n        return webSearchIndicators.some((indicator)=>text.includes(indicator));\n    };\n    // Função para extrair e estilizar a seção de web search\n    const processWebSearchContent = (text)=>{\n        const webSearchPattern = /A web search was conducted on[^]*?Example: \\[nytimes\\.com\\]\\([^)]+\\)\\./;\n        const match = text.match(webSearchPattern);\n        if (match) {\n            return {\n                webSearchSection: match[0],\n                mainContent: text.replace(webSearchPattern, \"\").trim()\n            };\n        }\n        return {\n            webSearchSection: \"\",\n            mainContent: text\n        };\n    };\n    const isWebSearchMessage = hasWebSearch || detectWebSearch(content);\n    const { webSearchSection, mainContent } = isWebSearchMessage ? processWebSearchContent(content) : {\n        webSearchSection: \"\",\n        mainContent: content\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: [\n            webSearchSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-section mb-4 p-3 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-cyan-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-300 text-sm font-medium\",\n                                children: \"\\uD83C\\uDF10 Busca na Web Ativada\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-200/80 leading-relaxed web-search-info\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                            remarkPlugins: [\n                                remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            ],\n                            children: webSearchSection\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                remarkPlugins: [\n                    remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                ],\n                rehypePlugins: [\n                    rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    [\n                        rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        {\n                            detect: true,\n                            ignoreMissing: true\n                        }\n                    ]\n                ],\n                components: {\n                    // Customizar renderização de código\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || \"\");\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"bg-gray-800 rounded-lg p-4 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de links\n                    a (param) {\n                        let { children, href, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-blue-400 hover:text-blue-300 underline\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de tabelas\n                    table (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse border border-gray-600\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0);\n                    },\n                    th (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    td (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"border border-gray-600 px-4 py-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de blockquotes\n                    blockquote (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de listas\n                    ul (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    ol (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de títulos\n                    h1 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h2 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h3 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de parágrafos\n                    p (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 leading-relaxed text-gray-200\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de linha horizontal\n                    hr (param) {\n                        let { ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"border-gray-600 my-6\",\n                            ...props\n                        }, void 0, false, void 0, void 0);\n                    }\n                },\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});