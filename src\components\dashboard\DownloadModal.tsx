'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChatMessage } from '@/lib/types/chat';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

interface DownloadModalProps {
  isOpen: boolean;
  onClose: () => void;
  messages: ChatMessage[];
  chatName: string;
}

type DownloadType = 'all' | 'user' | 'ai';

const DownloadModal = ({ isOpen, onClose, messages, chatName }: DownloadModalProps) => {
  const [selectedType, setSelectedType] = useState<DownloadType>('all');
  const [isGenerating, setIsGenerating] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Função para processar markdown e LaTeX
  const processMessageContent = (content: string): string => {
    try {
      // Configurar marked para suporte a LaTeX
      marked.setOptions({
        breaks: true,
        gfm: true,
      });

      // Processar markdown
      let processedContent = marked(content);

      // Sanitizar HTML
      processedContent = DOMPurify.sanitize(processedContent);

      return processedContent;
    } catch (error) {
      console.error('Erro ao processar conteúdo:', error);
      // Fallback: retornar conteúdo original com quebras de linha convertidas
      return content.replace(/\n/g, '<br>');
    }
  };

  const generateStyledHTML = (filteredMessages: ChatMessage[], chatName: string, type: DownloadType) => {
    const typeLabels = {
      all: 'Todas as mensagens',
      user: 'Mensagens do usuário',
      ai: 'Mensagens da IA'
    };

    return `<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${chatName} - ${typeLabels[type]}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- KaTeX CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css" integrity="sha384-GvrOXuhMATgEsSwCs4smul74iXGOixntILdUW9XmUC6+HX0sLNAK3q71HotJqlAn" crossorigin="anonymous">

    <!-- Highlight.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">

    <!-- KaTeX JavaScript -->
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js" integrity="sha384-cpW21h6RZv/phavutF+AuVYrr+dA8xD9zs6FwLpaCct6O9ctzYFfFr4dgmgccOTx" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

    <!-- Highlight.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0c1426 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0c1426 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: #f8fafc;
            line-height: 1.7;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 3rem 2rem;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 4rem;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));
            border-radius: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.4);
            backdrop-filter: blur(20px);
            box-shadow:
                0 25px 50px -12px rgba(0, 0, 0, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
        }

        .header::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 25%, #dbeafe 50%, #93c5fd 75%, #60a5fa 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
            position: relative;
            z-index: 1;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header p {
            color: rgba(203, 213, 225, 0.9);
            font-size: 1.2rem;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        
        .messages {
            display: flex;
            flex-direction: column;
            gap: 2.5rem;
        }

        .message {
            border-radius: 1.5rem;
            padding: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow:
                0 10px 25px -5px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05);
        }

        .message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
        }

        .message.user {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(34, 197, 94, 0.1));
            border-color: rgba(16, 185, 129, 0.4);
            margin-left: 3rem;
            transform: translateX(0);
        }

        .message.user::before {
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);
        }

        .message.ai {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.1));
            border-color: rgba(59, 130, 246, 0.4);
            margin-right: 3rem;
            transform: translateX(0);
        }
        
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            font-weight: 700;
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
        }

        .message-header .icon {
            width: 40px;
            height: 40px;
            margin-right: 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            box-shadow:
                0 4px 8px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .message-header .icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
            border-radius: 50%;
        }

        .message.user .icon {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.8), rgba(34, 197, 94, 0.6));
            border: 2px solid rgba(16, 185, 129, 0.5);
        }

        .message.ai .icon {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.6));
            border: 2px solid rgba(59, 130, 246, 0.5);
        }
        
        .message-content {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.6), rgba(30, 41, 59, 0.4));
            padding: 2rem;
            border-radius: 1.25rem;
            border: 1px solid rgba(59, 130, 246, 0.2);
            word-wrap: break-word;
            font-size: 1rem;
            line-height: 1.8;
            backdrop-filter: blur(10px);
            box-shadow:
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .message-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        }

        /* ===== ESTILOS PARA MARKDOWN ===== */
        .message-content h1,
        .message-content h2,
        .message-content h3,
        .message-content h4,
        .message-content h5,
        .message-content h6 {
            font-weight: 700;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            line-height: 1.3;
            background: linear-gradient(135deg, #60a5fa, #93c5fd, #dbeafe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .message-content h1 { font-size: 1.875rem; border-bottom: 2px solid rgba(59, 130, 246, 0.3); padding-bottom: 0.5rem; }
        .message-content h2 { font-size: 1.5rem; border-bottom: 1px solid rgba(59, 130, 246, 0.2); padding-bottom: 0.25rem; }
        .message-content h3 { font-size: 1.25rem; }
        .message-content h4 { font-size: 1.125rem; }
        .message-content h5 { font-size: 1rem; }
        .message-content h6 { font-size: 0.875rem; }

        .message-content p {
            margin-bottom: 1rem;
            line-height: 1.7;
            color: #cbd5e1;
        }

        .message-content ul,
        .message-content ol {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
            color: #cbd5e1;
        }

        .message-content ul li {
            position: relative;
            margin-bottom: 0.5rem;
            list-style: none;
            padding-left: 1.25rem;
        }

        .message-content ul li::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.6rem;
            width: 6px;
            height: 6px;
            background: linear-gradient(135deg, #3b82f6, #60a5fa);
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .message-content ol li {
            margin-bottom: 0.5rem;
        }

        .message-content blockquote {
            margin: 1rem 0;
            padding: 1rem 1.25rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
            border-left: 4px solid #3b82f6;
            border-radius: 0 0.5rem 0.5rem 0;
            font-style: italic;
            color: #e2e8f0;
        }

        .message-content code {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.3), rgba(59, 130, 246, 0.2));
            color: #fbbf24;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        .message-content pre {
            background: linear-gradient(135deg, rgba(13, 28, 74, 0.8), rgba(30, 58, 138, 0.6)) !important;
            border: 1px solid rgba(59, 130, 246, 0.4);
            border-radius: 0.75rem;
            padding: 1.25rem;
            margin: 1rem 0;
            overflow-x: auto;
            position: relative;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .message-content pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .message-content pre code {
            background: transparent !important;
            color: #e2e8f0 !important;
            padding: 0 !important;
            border: none !important;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .message-content a {
            color: #60a5fa;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 1px solid transparent;
        }

        .message-content a:hover {
            color: #93c5fd;
            border-bottom-color: #93c5fd;
        }

        .message-content table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 1rem 0;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.1), rgba(59, 130, 246, 0.05));
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .message-content th {
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.8), rgba(59, 130, 246, 0.6));
            color: #ffffff;
            font-weight: 600;
            padding: 0.75rem 1rem;
            text-align: left;
            font-size: 0.875rem;
        }

        .message-content td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
            color: #cbd5e1;
        }

        .message-content tr:last-child td {
            border-bottom: none;
        }

        .message-content hr {
            margin: 1.5rem 0;
            border: none;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3b82f6, #60a5fa, #3b82f6, transparent);
            border-radius: 1px;
        }

        .message-content strong {
            color: #f1f5f9;
            font-weight: 700;
        }

        .message-content em {
            color: #cbd5e1;
            font-style: italic;
        }

        /* ===== ESTILOS PARA KATEX ===== */
        .message-content .katex {
            font-size: 1.1em !important;
            color: #e2e8f0 !important;
        }

        .message-content .katex-display {
            margin: 1.5rem 0 !important;
            padding: 1rem !important;
            background: rgba(30, 58, 138, 0.1) !important;
            border: 1px solid rgba(59, 130, 246, 0.3) !important;
            border-radius: 0.5rem !important;
            overflow-x: auto !important;
            text-align: center;
        }

        .message-content .katex .base {
            color: #f1f5f9 !important;
        }

        .message-content .katex .mbin,
        .message-content .katex .mrel {
            color: #60a5fa !important;
        }

        .message-content .katex .mord {
            color: #e2e8f0 !important;
        }

        .message-content .katex .mop {
            color: #34d399 !important;
        }

        .message-content .katex .mopen,
        .message-content .katex .mclose {
            color: #fbbf24 !important;
        }

        /* ===== ESTILOS PARA HIGHLIGHT.JS ===== */
        .message-content .hljs {
            background: transparent !important;
            color: #e2e8f0;
            padding: 0;
        }

        .message-content .hljs-keyword {
            color: #c084fc !important;
            font-weight: 600;
        }

        .message-content .hljs-string {
            color: #34d399 !important;
        }

        .message-content .hljs-number {
            color: #fbbf24 !important;
        }

        .message-content .hljs-comment {
            color: #6b7280 !important;
            font-style: italic;
        }

        .message-content .hljs-function {
            color: #60a5fa !important;
        }

        .message-content .hljs-variable {
            color: #f87171 !important;
        }

        .message-content .hljs-title {
            color: #fbbf24 !important;
            font-weight: 600;
        }

        .message-content .hljs-attr {
            color: #60a5fa !important;
        }

        .message-content .hljs-built_in {
            color: #c084fc !important;
        }

        .message-content .hljs-type {
            color: #34d399 !important;
        }

        .message-content .hljs-literal {
            color: #f87171 !important;
        }

        .message-content .hljs-meta {
            color: #6b7280 !important;
        }

        .message-content .hljs-tag {
            color: #60a5fa !important;
        }

        .message-content .hljs-name {
            color: #fbbf24 !important;
        }

        /* ===== RESPONSIVIDADE ===== */
        /* ===== RESPONSIVIDADE MELHORADA ===== */
        @media (max-width: 768px) {
            .container {
                padding: 1.5rem 1rem;
            }

            .header {
                padding: 2rem 1.5rem;
                margin-bottom: 3rem;
            }

            .header h1 {
                font-size: 2.25rem;
            }

            .header p {
                font-size: 1rem;
            }

            .message {
                margin-left: 1rem !important;
                margin-right: 1rem !important;
                padding: 1.5rem;
            }

            .message-header .icon {
                width: 32px;
                height: 32px;
                font-size: 1rem;
            }

            .message-content {
                padding: 1.5rem;
                font-size: 0.9rem;
            }

            .message-content h1 { font-size: 1.5rem; }
            .message-content h2 { font-size: 1.25rem; }
            .message-content h3 { font-size: 1.125rem; }

            .message-content pre {
                padding: 1rem;
                font-size: 0.8rem;
            }

            .message-content .katex-display {
                font-size: 0.9em !important;
                padding: 0.75rem !important;
            }

            .message-time {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }

            .footer {
                padding: 2rem 1.5rem;
                margin-top: 3rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.875rem;
            }

            .message {
                margin-left: 0.5rem !important;
                margin-right: 0.5rem !important;
                padding: 1.25rem;
            }

            .message-content {
                padding: 1.25rem;
                font-size: 0.875rem;
            }

            .message-content h1 { font-size: 1.25rem; }
            .message-content h2 { font-size: 1.125rem; }
            .message-content h3 { font-size: 1rem; }
        }

        /* ===== EFEITOS ESPECIAIS ===== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message {
            animation: fadeIn 0.6s ease-out;
        }

        .message:nth-child(even) {
            animation-delay: 0.1s;
        }

        .message:nth-child(odd) {
            animation-delay: 0.2s;
        }

        /* ===== SCROLL SUAVE ===== */
        html {
            scroll-behavior: smooth;
        }

        /* ===== SELEÇÃO DE TEXTO ===== */
        ::selection {
            background: rgba(59, 130, 246, 0.3);
            color: #ffffff;
        }

        ::-moz-selection {
            background: rgba(59, 130, 246, 0.3);
            color: #ffffff;
        }

        .message-time {
            font-size: 0.875rem;
            color: rgba(148, 163, 184, 0.8);
            margin-top: 1.5rem;
            text-align: right;
            font-weight: 500;
            padding: 0.5rem 1rem;
            background: rgba(15, 23, 42, 0.3);
            border-radius: 0.75rem;
            border: 1px solid rgba(59, 130, 246, 0.1);
            backdrop-filter: blur(5px);
            display: inline-block;
            float: right;
            clear: both;
        }

        .footer {
            text-align: center;
            margin-top: 4rem;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, rgba(30, 58, 138, 0.2), rgba(59, 130, 246, 0.1));
            border-radius: 2rem;
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(20px);
            box-shadow:
                0 10px 25px -5px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
        }

        .footer p {
            color: rgba(203, 213, 225, 0.9);
            font-size: 1rem;
            font-weight: 500;
            margin: 0;
            background: linear-gradient(135deg, #94a3b8, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .message.user {
                margin-left: 0;
            }
            
            .message.ai {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${chatName}</h1>
            <p>${typeLabels[type]} • ${filteredMessages.length} mensagem${filteredMessages.length !== 1 ? 's' : ''}</p>
        </div>
        
        <div class="messages">
            ${filteredMessages.map(message => `
                <div class="message ${message.role === 'user' ? 'user' : 'ai'}">
                    <div class="message-header">
                        <div class="icon">
                            ${message.role === 'user' ? '👤' : '🤖'}
                        </div>
                        ${message.role === 'user' ? 'Você' : 'IA'}
                    </div>
                    <div class="message-content">${processMessageContent(message.content)}</div>
                    <div class="message-time">
                        ${new Date(message.timestamp).toLocaleString('pt-BR')}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>✨ Exportado em ${new Date().toLocaleString('pt-BR')} • Rafthor AI ✨</p>
        </div>
    </div>

    <script>
        // Inicializar Highlight.js
        document.addEventListener('DOMContentLoaded', function() {
            hljs.highlightAll();

            // Inicializar KaTeX
            if (typeof renderMathInElement !== 'undefined') {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\\\(', right: '\\\\)', display: false},
                        {left: '\\\\[', right: '\\\\]', display: true}
                    ],
                    throwOnError: false,
                    errorColor: '#cc0000',
                    strict: false,
                    trust: false,
                    macros: {
                        "\\\\f": "#1f(#2)"
                    }
                });
            }
        });
    </script>
</body>
</html>`;
  };

  const handleDownload = async () => {
    setIsGenerating(true);
    
    try {
      // Filter messages based on selected type
      let filteredMessages = messages;
      if (selectedType === 'user') {
        filteredMessages = messages.filter(msg => msg.role === 'user');
      } else if (selectedType === 'ai') {
        filteredMessages = messages.filter(msg => msg.role === 'assistant');
      }

      // Generate HTML content
      const htmlContent = generateStyledHTML(filteredMessages, chatName, selectedType);
      
      // Create and download file
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${chatName}_${selectedType}_messages.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      onClose();
    } catch (error) {
      console.error('Error generating download:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  if (!mounted || !isOpen) return null;

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center transition-opacity duration-300"
      style={{
        zIndex: 99999,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0
      }}
    >
      <div
        className="border border-blue-600/30 rounded-2xl shadow-2xl p-0 w-full max-w-lg transform transition-all duration-300 relative"
        style={{
          background: 'linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(29, 78, 216, 0.95) 50%, rgba(30, 58, 138, 0.95) 100%)',
          backdropFilter: 'blur(20px)',
          position: 'relative',
          zIndex: 100000
        }}
      >
        {/* Efeito de brilho sutil */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-cyan-500/10 pointer-events-none rounded-2xl"></div>
        
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b border-blue-600/30 relative z-10">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-blue-100">Download do Chat</h2>
              <p className="text-sm text-blue-300/70">Exporte suas conversas em HTML</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-blue-300 hover:text-blue-100 transition-all duration-200 p-2 rounded-xl hover:bg-blue-800/40 hover:scale-105"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6 relative z-10">
          <div className="mb-6">
            <p className="text-blue-200 font-medium mb-2">
              Escolha quais mensagens incluir no arquivo HTML:
            </p>
            <p className="text-blue-300/70 text-sm">
              O arquivo será exportado com formatação completa, incluindo Markdown, LaTeX, syntax highlighting e estilos profissionais
            </p>
          </div>

          <div className="space-y-4 mb-8">
            {/* All Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'all'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="all"
                  checked={selectedType === 'all'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <div className="text-blue-100 font-semibold">Todas as mensagens</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Inclui mensagens do usuário e da IA</div>
                </div>
              </div>
            </label>

            {/* User Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'user'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="user"
                  checked={selectedType === 'user'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <div className="text-blue-100 font-semibold">Apenas mensagens do usuário</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Somente suas perguntas e comentários</div>
                </div>
              </div>
            </label>

            {/* AI Messages Option */}
            <label className={`flex items-center p-4 rounded-xl border transition-all duration-200 cursor-pointer backdrop-blur-sm hover:scale-[1.02] ${
              selectedType === 'ai'
                ? 'border-blue-500/50 bg-blue-600/20 shadow-lg shadow-blue-500/20'
                : 'border-blue-600/30 bg-blue-900/30 hover:border-blue-500/40 hover:bg-blue-800/40'
            }`}>
              <div className="flex items-center">
                <input
                  type="radio"
                  name="downloadType"
                  value="ai"
                  checked={selectedType === 'ai'}
                  onChange={(e) => setSelectedType(e.target.value as DownloadType)}
                  className="w-4 h-4 text-blue-600 bg-blue-900 border-blue-600 focus:ring-blue-500 focus:ring-2"
                />
                <div className="ml-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    <div className="text-blue-100 font-semibold">Apenas mensagens da IA</div>
                  </div>
                  <div className="text-blue-300/70 text-sm mt-1">Somente as respostas da inteligência artificial</div>
                </div>
              </div>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-3 text-blue-300 hover:text-blue-100 transition-all duration-200 rounded-xl hover:bg-blue-800/30 font-medium"
            >
              Cancelar
            </button>
            <button
              onClick={handleDownload}
              disabled={isGenerating}
              className="px-8 py-3 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-600 hover:from-blue-500 hover:via-blue-400 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-semibold shadow-lg hover:shadow-blue-500/30 hover:scale-105 disabled:hover:scale-100"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-3"></div>
                  Gerando arquivo...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Baixar HTML
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default DownloadModal;
