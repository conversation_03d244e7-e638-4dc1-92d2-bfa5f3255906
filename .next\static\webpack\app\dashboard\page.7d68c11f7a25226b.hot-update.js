"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/Sidebar */ \"(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\");\n/* harmony import */ var _components_dashboard_ChatArea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/ChatArea */ \"(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\");\n/* harmony import */ var _components_dashboard_SettingsModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SettingsModal */ \"(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentChat, setCurrentChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para lidar com a criação de chat automático\n    const handleChatCreated = (chatId)=>{\n        var // Recarregar a sidebar para mostrar o novo chat\n        _sidebarRef_current;\n        setCurrentChat(chatId);\n        (_sidebarRef_current = sidebarRef.current) === null || _sidebarRef_current === void 0 ? void 0 : _sidebarRef_current.reloadChats();\n    };\n    // Redirecionar se não estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        authLoading,\n        router\n    ]);\n    // Buscar dados do usuário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            if (!user) return;\n            try {\n                // Buscar todos os documentos na coleção usuarios para encontrar o usuário pelo email\n                const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n                const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n                const querySnapshot = await getDocs(q);\n                if (!querySnapshot.empty) {\n                    var _user_email;\n                    // Usuário encontrado\n                    const userDoc = querySnapshot.docs[0];\n                    const data = userDoc.data();\n                    setUserData({\n                        username: data.username || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                        email: data.email || user.email || \"\",\n                        balance: data.balance || 0,\n                        createdAt: data.createdAt || new Date().toISOString()\n                    });\n                } else {\n                    var _user_email1;\n                    // Se não encontrar o documento, criar dados padrão\n                    setUserData({\n                        username: ((_user_email1 = user.email) === null || _user_email1 === void 0 ? void 0 : _user_email1.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                        email: user.email || \"\",\n                        balance: 0,\n                        createdAt: new Date().toISOString()\n                    });\n                }\n            } catch (error) {\n                var _user_email2;\n                console.error(\"Erro ao buscar dados do usu\\xe1rio:\", error);\n                // Dados padrão em caso de erro\n                setUserData({\n                    username: ((_user_email2 = user.email) === null || _user_email2 === void 0 ? void 0 : _user_email2.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                    email: user.email || \"\",\n                    balance: 0,\n                    createdAt: new Date().toISOString()\n                });\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (user && !authLoading) {\n            fetchUserData();\n        }\n    }, [\n        user,\n        authLoading\n    ]);\n    const handleUpdateOpenRouterBalance = ()=>{\n        if (sidebarRef.current) {\n            sidebarRef.current.updateOpenRouterBalance();\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-rafthor flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: \"Carregando...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || !userData) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-rafthor flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ref: sidebarRef,\n                userData: userData,\n                isOpen: sidebarOpen,\n                onToggle: ()=>setSidebarOpen(!sidebarOpen),\n                onSettingsOpen: ()=>setSettingsOpen(true),\n                onChatSelect: setCurrentChat,\n                currentChat: currentChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col lg:ml-80 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSidebarOpen(true),\n                            className: \"text-white hover:text-white/80 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ChatArea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        currentChat: currentChat,\n                        onChatCreated: handleChatCreated\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SettingsModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: settingsOpen,\n                onClose: ()=>setSettingsOpen(false),\n                userData: userData,\n                onUserDataUpdate: setUserData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black/50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OqHlHnq7MCvSPycr8PY9OiPzVFM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});