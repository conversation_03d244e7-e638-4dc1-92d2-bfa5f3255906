'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

export default function Home() {
  const { user, logout, loading } = useAuth();
  const router = useRouter();

  // Redirecionar usuários logados para a dashboard
  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard');
    }
  }, [user, loading, router]);

  const testFirestore = async () => {
    try {
      console.log('Testando conexão com Firestore...');
      await setDoc(doc(db, 'test', 'test-doc'), {
        message: 'Teste de conexão',
        timestamp: new Date().toISOString()
      });
      alert('Teste do Firestore bem-sucedido! Verifique o console.');
      console.log('Documento de teste criado com sucesso!');
    } catch (error) {
      console.error('Erro no teste do Firestore:', error);
      alert('Erro no teste do Firestore. Verifique o console.');
    }
  };

  if (loading) {
    return (
      <main className="min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white/80">Carregando...</p>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8">
      {/* Elementos decorativos de fundo */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header com navegação */}
      <div className="absolute top-8 left-8 right-8 flex justify-between items-center z-10">
        <div>
          <h1 className="text-2xl font-bold text-white">Rafthor</h1>
          <p className="text-white/70 text-sm">AI Chatbot Platform</p>
        </div>

        <div className="flex items-center space-x-4">
          {user ? (
            <div className="flex items-center space-x-4">
              <span className="text-white/90">Olá, {user.email}</span>
              <button
                onClick={logout}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30"
              >
                Sair
              </button>
            </div>
          ) : (
            <Link
              href="/login"
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30"
            >
              Entrar
            </Link>
          )}
        </div>
      </div>

      <div className="relative z-10 text-center">
        <h1 className="text-5xl font-bold text-white mb-6">
          Bem-vindo ao Rafthor
        </h1>
        <p className="text-xl text-white/80 mb-8">
          Plataforma de chatbot com múltiplas IAs
        </p>

        {user ? (
          <div className="space-y-4">
            <p className="text-white/90 text-lg">
              Você está logado como: <strong>{user.email}</strong>
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30"
              >
                Iniciar Chat
              </button>
              <button className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30">
                Configurações
              </button>
              <button
                onClick={testFirestore}
                className="bg-red-500/20 hover:bg-red-500/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-red-500/30"
              >
                Testar Firestore
              </button>
            </div>
          </div>
        ) : (
          <Link
            href="/login"
            className="inline-block bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-2xl transition-all duration-300 backdrop-blur-sm border border-white/30"
          >
            Entrar
          </Link>
        )}
      </div>
    </main>
  )
}
