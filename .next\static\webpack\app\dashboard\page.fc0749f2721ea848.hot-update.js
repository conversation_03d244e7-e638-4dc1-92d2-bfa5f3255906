"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/services/openRouterService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/openRouterService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   openRouterService: function() { return /* binding */ openRouterService; }\n/* harmony export */ });\nclass OpenRouterService {\n    async fetchModels() {\n        // Verificar cache\n        if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {\n            return this.cache.models;\n        }\n        try {\n            const response = await fetch(\"https://openrouter.ai/api/v1/models\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(\"your-openrouter-api-key-here\" || 0),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            const models = data.data.map((model)=>({\n                    id: model.id,\n                    name: model.name,\n                    description: model.description || \"\",\n                    context_length: model.context_length,\n                    pricing: {\n                        prompt: model.pricing.prompt,\n                        completion: model.pricing.completion,\n                        image: model.pricing.image\n                    },\n                    architecture: model.architecture,\n                    created: model.created,\n                    isFavorite: false\n                }));\n            // Atualizar cache\n            this.cache = {\n                models,\n                timestamp: Date.now()\n            };\n            return models;\n        } catch (error) {\n            console.error(\"Error fetching OpenRouter models:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.cache) {\n                return this.cache.models;\n            }\n            throw error;\n        }\n    }\n    filterByCategory(models, category) {\n        switch(category){\n            case \"free\":\n                return models.filter((model)=>this.isFreeModel(model));\n            case \"paid\":\n                return models.filter((model)=>!this.isFreeModel(model));\n            case \"favorites\":\n                return models.filter((model)=>model.isFavorite);\n            default:\n                return models;\n        }\n    }\n    sortModels(models, sortBy) {\n        const sortedModels = [\n            ...models\n        ];\n        switch(sortBy){\n            case \"newest\":\n                return sortedModels.sort((a, b)=>(b.created || 0) - (a.created || 0));\n            case \"price_low\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(a) - this.getTotalPrice(b));\n            case \"price_high\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(b) - this.getTotalPrice(a));\n            case \"context_high\":\n                return sortedModels.sort((a, b)=>b.context_length - a.context_length);\n            default:\n                return sortedModels;\n        }\n    }\n    isFreeModel(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice === 0 && completionPrice === 0;\n    }\n    getTotalPrice(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice + completionPrice;\n    }\n    formatPrice(price) {\n        const numPrice = parseFloat(price) * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens\n        if (numPrice === 0) return \"Gr\\xe1tis\";\n        if (numPrice < 0.001) return \"< $0.001\";\n        return \"$\".concat(numPrice.toFixed(3));\n    }\n    formatContextLength(length) {\n        return length.toLocaleString(); // Mostra o número completo com separadores de milhares\n    }\n    // Busca avançada com fuzzy matching\n    searchModels(models, searchTerm) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n        if (!searchTerm.trim()) {\n            return [];\n        }\n        const term = searchTerm.toLowerCase();\n        const results = [];\n        for (const model of models){\n            let score = 0;\n            const matchedFields = [];\n            let highlightedName = model.name;\n            let highlightedDescription = model.description || \"\";\n            // Busca no nome (peso maior)\n            if (model.name.toLowerCase().includes(term)) {\n                score += 10;\n                matchedFields.push(\"name\");\n                highlightedName = this.highlightText(model.name, term);\n            }\n            // Busca no ID (peso médio)\n            if (model.id.toLowerCase().includes(term)) {\n                score += 7;\n                matchedFields.push(\"id\");\n            }\n            // Busca na descrição (peso menor)\n            if (model.description && model.description.toLowerCase().includes(term)) {\n                score += 3;\n                matchedFields.push(\"description\");\n                highlightedDescription = this.highlightText(model.description, term);\n            }\n            // Boost para favoritos\n            if (boostFavorites && model.isFavorite) {\n                score *= 1.5;\n            }\n            // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n            if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && this.isFreeModel(model)) {\n                score += 5;\n            }\n            // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n            if ((term.includes(\"expensive\") || term.includes(\"caro\")) && this.getTotalPrice(model) > 0.00002) {\n                score += 5;\n            }\n            if (score > 0) {\n                results.push({\n                    model,\n                    score,\n                    matchedFields,\n                    highlightedName,\n                    highlightedDescription\n                });\n            }\n        }\n        // Ordenar por score e limitar resultados\n        return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n    }\n    highlightText(text, term) {\n        const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n        return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n    }\n    // Limpar cache\n    clearCache() {\n        this.cache = null;\n    }\n    constructor(){\n        this.cache = null;\n        this.creditsCache = null;\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.CREDITS_CACHE_DURATION = 30 * 1000 // 30 segundos para créditos\n        ;\n    }\n}\nconst openRouterService = new OpenRouterService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/openRouterService.ts\n"));

/***/ })

});