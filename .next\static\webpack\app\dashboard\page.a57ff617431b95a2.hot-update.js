"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, onToggle, onSettingsOpen, onChatSelect, currentChat, onUpdateOpenRouterBalance } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openRouterBalance, setOpenRouterBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        balance: 0,\n        isLoading: true,\n        error: undefined\n    });\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    // Ref para controlar requisições em andamento\n    const fetchingBalanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const fetchOpenRouterBalance = async ()=>{\n        // Evitar múltiplas requisições simultâneas\n        if (fetchingBalanceRef.current) {\n            console.log(\"Requisi\\xe7\\xe3o de saldo j\\xe1 em andamento, ignorando...\");\n            return;\n        }\n        try {\n            fetchingBalanceRef.current = true;\n            console.log(\"Iniciando busca do saldo do OpenRouter...\");\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: true,\n                    error: undefined\n                }));\n            let openRouterApiKey = \"\";\n            // Primeiro, tentar buscar na nova estrutura (endpoints)\n            try {\n                const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"endpoints\");\n                const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(endpointsRef);\n                endpointsSnapshot.forEach((doc)=>{\n                    const data = doc.data();\n                    if (data.isActive && data.url && data.url.includes(\"openrouter.ai\")) {\n                        openRouterApiKey = data.apiKey;\n                    }\n                });\n            } catch (error) {\n                console.log(\"Nova estrutura n\\xe3o encontrada, tentando estrutura antiga...\");\n            }\n            // Se não encontrou na nova estrutura, buscar na estrutura antiga\n            if (!openRouterApiKey) {\n                try {\n                    const { doc: docImport, getDoc } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                    const configRef = docImport(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"configuracoes\", \"settings\");\n                    const configDoc = await getDoc(configRef);\n                    if (configDoc.exists()) {\n                        const config = configDoc.data();\n                        if (config.endpoints) {\n                            // Buscar endpoint ativo do OpenRouter na estrutura antiga\n                            Object.values(config.endpoints).forEach((endpoint)=>{\n                                if (endpoint.ativo && endpoint.url && endpoint.url.includes(\"openrouter.ai\")) {\n                                    openRouterApiKey = endpoint.apiKey;\n                                }\n                            });\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"Estrutura antiga tamb\\xe9m n\\xe3o encontrada\");\n                }\n            }\n            if (!openRouterApiKey) {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: \"Nenhuma API key do OpenRouter configurada\"\n                    }));\n                return;\n            }\n            console.log(\"API Key encontrada, buscando saldo...\");\n            // Fazer requisição para buscar créditos\n            const response = await fetch(\"/api/openrouter/credits\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    apiKey: openRouterApiKey\n                })\n            });\n            const data = await response.json();\n            console.log(\"Resposta da API:\", data);\n            if (data.success) {\n                setOpenRouterBalance({\n                    balance: data.balance,\n                    isLoading: false,\n                    error: undefined\n                });\n                console.log(\"Saldo carregado com sucesso:\", data.balance);\n            } else {\n                setOpenRouterBalance((prev)=>({\n                        ...prev,\n                        isLoading: false,\n                        error: data.error || \"Erro ao buscar saldo\"\n                    }));\n                console.log(\"Erro ao buscar saldo:\", data.error);\n            }\n        } catch (error) {\n            console.error(\"Erro ao buscar saldo do OpenRouter:\", error);\n            setOpenRouterBalance((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: \"Erro ao conectar com OpenRouter\"\n                }));\n        } finally{\n            fetchingBalanceRef.current = false;\n        }\n    };\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    // Ref para controlar se já foi carregado\n    const balanceLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n            // Carregar saldo apenas se ainda não foi carregado\n            if (!balanceLoadedRef.current) {\n                console.log(\"Carregando saldo do OpenRouter pela primeira vez...\");\n                fetchOpenRouterBalance();\n                balanceLoadedRef.current = true;\n            }\n        }\n    }, [\n        userData.username\n    ]);\n    // Expor as funções para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats,\n            updateOpenRouterBalance: fetchOpenRouterBalance\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    // Função para deletar todos os anexos de um chat\n    const deleteChatAttachments = async (chatId)=>{\n        try {\n            const attachmentsRef = ref(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(chatId, \"/anexos\"));\n            const attachmentsList = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.listAll)(attachmentsRef);\n            // Deletar todos os arquivos de anexos\n            const deletePromises = attachmentsList.items.map((item)=>(0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(item));\n            await Promise.all(deletePromises);\n            console.log(\"\".concat(attachmentsList.items.length, \" anexos deletados do chat \").concat(chatId));\n        } catch (error) {\n            console.log(\"Erro ao deletar anexos ou pasta de anexos n\\xe3o encontrada:\", error);\n        }\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Tentar deletar arquivo do Storage\n                try {\n                    const storageRef = ref(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(storageRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\", \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-lg\",\n                                            children: userData.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            openRouterBalance.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 border border-blue-300 border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Carregando...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 19\n                                            }, undefined) : openRouterBalance.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-400 text-sm\",\n                                                title: openRouterBalance.error,\n                                                children: \"$0.00\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: [\n                                                    \"$\",\n                                                    openRouterBalance.balance.toFixed(4)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onSettingsOpen,\n                                        className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xc1rea Inicial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 674,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewFolder,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium\",\n                                            title: \"Nova Pasta\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 655,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Conversas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            onDragOver: (e)=>handleDragOver(e, folder.id),\n                                            onDragLeave: handleDragLeave,\n                                            onDrop: (e)=>handleDrop(e, folder.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                    onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                    onMouseLeave: ()=>setHoveredFolder(null),\n                                                    onClick: ()=>toggleFolder(folder.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 5l7 7-7 7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 729,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 728,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            style: {\n                                                                                color: getFolderHexColor(folder.color)\n                                                                            },\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 752,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                        children: folder.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 759,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                        children: folder.chats.length\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 762,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 758,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                children: folder.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 767,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Editar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleEditFolder(folder.id);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 787,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 786,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 778,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Deletar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleDeleteFolder(folder.id, folder.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 790,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 717,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-6 mt-2 space-y-1\",\n                                                    children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: ()=>handleDragStart(chat.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(currentChat === chat.id ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(draggedChat === chat.id ? \"opacity-50 scale-95\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-full text-left p-3 flex items-start space-x-3\",\n                                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(currentChat === chat.id ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                                                                            children: [\n                                                                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-2 h-2 text-white\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 3,\n                                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 834,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 833,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 832,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    strokeWidth: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 840,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 839,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 825,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"truncate text-sm font-semibold mb-1 \".concat(currentChat === chat.id ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                                                                    children: chat.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 845,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"truncate text-xs leading-relaxed transition-all duration-300 \".concat(currentChat === chat.id ? \"text-blue-200/80\" : \"text-blue-300/70 group-hover:text-blue-200\"),\n                                                                                    children: chat.lastMessage || \"Nenhuma mensagem ainda...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 852,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs mt-1 block \".concat(currentChat === chat.id ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                                                                    children: formatTime(chat.lastMessageTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 860,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Editar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"edit\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 882,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 881,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 873,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Deletar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"delete\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 894,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 893,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 885,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 872,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, chat.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 810,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, folder.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    onDragOver: (e)=>handleDragOver(e, null),\n                                    onDragLeave: handleDragLeave,\n                                    onDrop: (e)=>handleDrop(e, null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 917,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Sem Pasta \",\n                                                            dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                            children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/30\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: \"Nenhuma conversa sem pasta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 932,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                    chat: chat,\n                                                    isActive: currentChat === chat.id,\n                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                    onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                    onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                    onDragStart: handleDragStart,\n                                                    onDragEnd: handleDragEnd,\n                                                    isDragging: draggedChat === chat.id\n                                                }, chat.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 936,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 13\n                                }, undefined),\n                                folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/30 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 956,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/50 text-sm\",\n                                            children: \"Nenhuma conversa ainda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 961,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/30 text-xs mt-1\",\n                                            children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 962,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden p-4 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Fechar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 977,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 969,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 968,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 610,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 984,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 993,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1006,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1014,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1026,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1041,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\")), \"csNqVWqV5Uv5aKsPI37t7cvNwpA=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging } = param;\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const handleEdit = (e)=>{\n        e.stopPropagation();\n        onEdit(chat.id, chat.name);\n    };\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        onDelete(chat.id, chat.name);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"relative w-full rounded-lg transition-all duration-200 group cursor-move \".concat(isActive ? \"bg-blue-600/50\" : \"hover:bg-white/5\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left px-3 py-3 text-white/80 hover:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                            children: [\n                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-2 h-2 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 3,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 1124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200\",\n                                            children: formatTime(chat.lastMessageTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 line-clamp-2 leading-relaxed\",\n                                    children: chat.lastMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1127,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 1108,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Configurar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1148,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1143,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Deletar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1160,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 1142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 1094,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRXFGO0FBQ2U7QUFDdEM7QUFDakI7QUFDRztBQUNJO0FBQ0U7QUFDUTtBQTJDOUQsTUFBTW9CLHdCQUFVbEIsR0FBQUEsaURBQVVBLFNBQWlGLFFBUXhHbUI7UUFSeUcsRUFDMUdDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxRQUFRLEVBQ1JDLGNBQWMsRUFDZEMsWUFBWSxFQUNaQyxXQUFXLEVBQ1hDLHlCQUF5QixFQUMxQjs7SUFDQyxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBRzlCLCtDQUFRQSxDQUFlLEVBQUU7SUFDdkQsTUFBTSxDQUFDK0Isa0JBQWtCQyxvQkFBb0IsR0FBR2hDLCtDQUFRQSxDQUFTLEVBQUU7SUFDbkUsTUFBTSxDQUFDaUMsbUJBQW1CQyxxQkFBcUIsR0FBR2xDLCtDQUFRQSxDQUFvQjtRQUM1RW1DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxPQUFPQztJQUNUO0lBQ0EsTUFBTSxDQUFDQyxxQkFBcUJDLHVCQUF1QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDeUMsbUJBQW1CQyxxQkFBcUIsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzJDLGFBQWFDLGVBQWUsR0FBRzVDLCtDQUFRQSxDQUFjO0lBQzVELE1BQU0sQ0FBQzZDLHVCQUF1QkMseUJBQXlCLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUMrQyxxQkFBcUJDLHVCQUF1QixHQUFHaEQsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDaUQsZUFBZUMsaUJBQWlCLEdBQUdsRCwrQ0FBUUEsQ0FNeEM7SUFDVixNQUFNLENBQUNtRCxhQUFhQyxlQUFlLEdBQUdwRCwrQ0FBUUEsQ0FBZ0I7SUFDOUQsTUFBTSxDQUFDcUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3RELCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN1RCxlQUFlQyxpQkFBaUIsR0FBR3hELCtDQUFRQSxDQUFnQjtJQUVsRSxxQ0FBcUM7SUFDckMsTUFBTSxDQUFDeUQsb0JBQW9CQyxzQkFBc0IsR0FBRzFELCtDQUFRQSxDQUt6RDtRQUNEdUIsUUFBUTtRQUNSb0MsTUFBTTtRQUNOQyxJQUFJO1FBQ0pDLE1BQU07SUFDUjtJQUNBLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHL0QsK0NBQVFBLENBQUM7SUFFN0MsOEJBQThCO0lBQzlCLE1BQU0sQ0FBQ2dFLGVBQWVDLGlCQUFpQixHQUFHakUsK0NBQVFBLENBSy9DO1FBQ0R1QixRQUFRO1FBQ1IyQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsUUFBUTtJQUNWO0lBRUEsOENBQThDO0lBQzlDLE1BQU1DLHFCQUFxQmpFLDZDQUFNQSxDQUFDO0lBRWxDLE1BQU1rRSx5QkFBeUI7UUFDN0IsMkNBQTJDO1FBQzNDLElBQUlELG1CQUFtQkUsT0FBTyxFQUFFO1lBQzlCQyxRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsSUFBSTtZQUNGSixtQkFBbUJFLE9BQU8sR0FBRztZQUM3QkMsUUFBUUMsR0FBRyxDQUFDO1lBQ1p2QyxxQkFBcUJ3QyxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUV0QyxXQUFXO29CQUFNQyxPQUFPQztnQkFBVTtZQUUzRSxJQUFJcUMsbUJBQW1CO1lBRXZCLHdEQUF3RDtZQUN4RCxJQUFJO2dCQUNGLE1BQU1DLGVBQWV2RSw4REFBVUEsQ0FBQ1MsNkNBQUVBLEVBQUUsWUFBWVEsU0FBU3VELFFBQVEsRUFBRTtnQkFDbkUsTUFBTUMsb0JBQW9CLE1BQU14RSwyREFBT0EsQ0FBQ3NFO2dCQUV4Q0Usa0JBQWtCQyxPQUFPLENBQUMsQ0FBQ3JFO29CQUN6QixNQUFNc0UsT0FBT3RFLElBQUlzRSxJQUFJO29CQUNyQixJQUFJQSxLQUFLQyxRQUFRLElBQUlELEtBQUtFLEdBQUcsSUFBSUYsS0FBS0UsR0FBRyxDQUFDQyxRQUFRLENBQUMsa0JBQWtCO3dCQUNuRVIsbUJBQW1CSyxLQUFLSSxNQUFNO29CQUNoQztnQkFDRjtZQUNGLEVBQUUsT0FBTy9DLE9BQU87Z0JBQ2RtQyxRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLGlFQUFpRTtZQUNqRSxJQUFJLENBQUNFLGtCQUFrQjtnQkFDckIsSUFBSTtvQkFDRixNQUFNLEVBQUVqRSxLQUFLMkUsU0FBUyxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNLDZMQUFPO29CQUNoRCxNQUFNQyxZQUFZRixVQUFVdkUsNkNBQUVBLEVBQUUsWUFBWVEsU0FBU3VELFFBQVEsRUFBRSxpQkFBaUI7b0JBQ2hGLE1BQU1XLFlBQVksTUFBTUYsT0FBT0M7b0JBRS9CLElBQUlDLFVBQVVDLE1BQU0sSUFBSTt3QkFDdEIsTUFBTUMsU0FBU0YsVUFBVVIsSUFBSTt3QkFDN0IsSUFBSVUsT0FBT0MsU0FBUyxFQUFFOzRCQUNwQiwwREFBMEQ7NEJBQzFEQyxPQUFPQyxNQUFNLENBQUNILE9BQU9DLFNBQVMsRUFBRVosT0FBTyxDQUFDLENBQUNlO2dDQUN2QyxJQUFJQSxTQUFTQyxLQUFLLElBQUlELFNBQVNaLEdBQUcsSUFBSVksU0FBU1osR0FBRyxDQUFDQyxRQUFRLENBQUMsa0JBQWtCO29DQUM1RVIsbUJBQW1CbUIsU0FBU1YsTUFBTTtnQ0FDcEM7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPL0MsT0FBTztvQkFDZG1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGO1lBRUEsSUFBSSxDQUFDRSxrQkFBa0I7Z0JBQ3JCekMscUJBQXFCd0MsQ0FBQUEsT0FBUzt3QkFDNUIsR0FBR0EsSUFBSTt3QkFDUHRDLFdBQVc7d0JBQ1hDLE9BQU87b0JBQ1Q7Z0JBQ0E7WUFDRjtZQUVBbUMsUUFBUUMsR0FBRyxDQUFDO1lBRVosd0NBQXdDO1lBQ3hDLE1BQU11QixXQUFXLE1BQU1DLE1BQU0sMkJBQTJCO2dCQUN0REMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVsQixRQUFRVDtnQkFBaUI7WUFDbEQ7WUFFQSxNQUFNSyxPQUFPLE1BQU1nQixTQUFTTyxJQUFJO1lBQ2hDL0IsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQk87WUFFaEMsSUFBSUEsS0FBS3dCLE9BQU8sRUFBRTtnQkFDaEJ0RSxxQkFBcUI7b0JBQ25CQyxTQUFTNkMsS0FBSzdDLE9BQU87b0JBQ3JCQyxXQUFXO29CQUNYQyxPQUFPQztnQkFDVDtnQkFDQWtDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0NPLEtBQUs3QyxPQUFPO1lBQzFELE9BQU87Z0JBQ0xELHFCQUFxQndDLENBQUFBLE9BQVM7d0JBQzVCLEdBQUdBLElBQUk7d0JBQ1B0QyxXQUFXO3dCQUNYQyxPQUFPMkMsS0FBSzNDLEtBQUssSUFBSTtvQkFDdkI7Z0JBQ0FtQyxRQUFRQyxHQUFHLENBQUMseUJBQXlCTyxLQUFLM0MsS0FBSztZQUNqRDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkbUMsUUFBUW5DLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JESCxxQkFBcUJ3QyxDQUFBQSxPQUFTO29CQUM1QixHQUFHQSxJQUFJO29CQUNQdEMsV0FBVztvQkFDWEMsT0FBTztnQkFDVDtRQUNGLFNBQVU7WUFDUmdDLG1CQUFtQkUsT0FBTyxHQUFHO1FBQy9CO0lBQ0Y7SUFFQSxNQUFNa0MsWUFBWTtRQUNoQixJQUFJO1lBQ0YsaUJBQWlCO1lBQ2pCLE1BQU1DLFdBQVdyRyw4REFBVUEsQ0FBQ1MsNkNBQUVBLEVBQUUsWUFBWVEsU0FBU3VELFFBQVEsRUFBRTtZQUMvRCxNQUFNOEIsYUFBYW5HLHlEQUFLQSxDQUFDa0csVUFBVW5HLDJEQUFPQSxDQUFDLGlCQUFpQjtZQUM1RCxNQUFNcUcsZ0JBQWdCLE1BQU10RywyREFBT0EsQ0FBQ3FHO1lBRXBDLE1BQU1FLFFBQWdCLEVBQUU7WUFDeEJELGNBQWM3QixPQUFPLENBQUMsQ0FBQ3JFO2dCQUNyQixNQUFNc0UsT0FBT3RFLElBQUlzRSxJQUFJO2dCQUNyQjZCLE1BQU1DLElBQUksQ0FBQztvQkFDVGxELElBQUlsRCxJQUFJa0QsRUFBRTtvQkFDVkMsTUFBTW1CLEtBQUtuQixJQUFJLElBQUk7b0JBQ25Ca0QsYUFBYS9CLEtBQUtnQyxjQUFjLElBQUk7b0JBQ3BDQyxpQkFBaUJqQyxLQUFLa0MsZ0JBQWdCLElBQUlsQyxLQUFLbUMsU0FBUztvQkFDeERDLFFBQVFwQyxLQUFLcUMsUUFBUTtvQkFDckJDLFVBQVV0QyxLQUFLc0MsUUFBUTtnQkFDekI7WUFDRjtZQUVBLGtCQUFrQjtZQUNsQixNQUFNQyxhQUFhbEgsOERBQVVBLENBQUNTLDZDQUFFQSxFQUFFLFlBQVlRLFNBQVN1RCxRQUFRLEVBQUU7WUFDakUsTUFBTTJDLGVBQWVoSCx5REFBS0EsQ0FBQytHLFlBQVloSCwyREFBT0EsQ0FBQyxhQUFhO1lBQzVELE1BQU1rSCxrQkFBa0IsTUFBTW5ILDJEQUFPQSxDQUFDa0g7WUFFdEMsTUFBTUUsZ0JBQThCLEVBQUU7WUFDdENELGdCQUFnQjFDLE9BQU8sQ0FBQyxDQUFDckU7Z0JBQ3ZCLE1BQU1zRSxPQUFPdEUsSUFBSXNFLElBQUk7Z0JBQ3JCLE1BQU0yQyxjQUFjZCxNQUFNZSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtULE1BQU0sS0FBSzFHLElBQUlrRCxFQUFFO2dCQUUvRDhELGNBQWNaLElBQUksQ0FBQztvQkFDakJsRCxJQUFJbEQsSUFBSWtELEVBQUU7b0JBQ1ZDLE1BQU1tQixLQUFLbkIsSUFBSTtvQkFDZmlFLGFBQWE5QyxLQUFLOEMsV0FBVztvQkFDN0JDLE9BQU8vQyxLQUFLK0MsS0FBSyxJQUFJO29CQUNyQkMsWUFBWWhELEtBQUtpRCxpQkFBaUIsS0FBSztvQkFDdkNwQixPQUFPYztnQkFDVDtZQUNGO1lBRUEsa0JBQWtCO1lBQ2xCLE1BQU1PLGNBQWNyQixNQUFNZSxNQUFNLENBQUNDLENBQUFBLE9BQVEsQ0FBQ0EsS0FBS1QsTUFBTTtZQUVyRHRGLFdBQVc0RjtZQUNYMUYsb0JBQW9Ca0c7UUFFdEIsRUFBRSxPQUFPN0YsT0FBTztZQUNkbUMsUUFBUW5DLEtBQUssQ0FBQyxvQ0FBb0NBO1FBQ3BEO0lBQ0Y7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTThGLG1CQUFtQi9ILDZDQUFNQSxDQUFDO0lBRWhDSCxnREFBU0EsQ0FBQztRQUNSLElBQUlxQixTQUFTdUQsUUFBUSxFQUFFO1lBQ3JCNEI7WUFFQSxtREFBbUQ7WUFDbkQsSUFBSSxDQUFDMEIsaUJBQWlCNUQsT0FBTyxFQUFFO2dCQUM3QkMsUUFBUUMsR0FBRyxDQUFDO2dCQUNaSDtnQkFDQTZELGlCQUFpQjVELE9BQU8sR0FBRztZQUM3QjtRQUNGO0lBQ0YsR0FBRztRQUFDakQsU0FBU3VELFFBQVE7S0FBQztJQUV0Qix5Q0FBeUM7SUFDekMxRSwwREFBbUJBLENBQUNrQixLQUFLLElBQU87WUFDOUIrRyxhQUFhM0I7WUFDYjRCLHlCQUF5Qi9EO1FBQzNCO0lBRUEsTUFBTWdFLGdCQUFnQjtRQUNwQjlGLHVCQUF1QjtJQUN6QjtJQUVBLE1BQU0rRixrQkFBa0I7UUFDdEJ6Rix5QkFBeUI7SUFDM0I7SUFFQSxNQUFNMEYsc0JBQXNCLENBQUNuQjtRQUMzQjdDLFFBQVFDLEdBQUcsQ0FBQyxpQkFBaUI0QztRQUM3QlosYUFBYSx1Q0FBdUM7SUFDdEQ7SUFFQSxNQUFNZ0Msc0JBQXNCLENBQUNwQjtRQUMzQjdDLFFBQVFDLEdBQUcsQ0FBQyxxQkFBcUI0QztRQUNqQ1osYUFBYSx3Q0FBd0M7UUFDckR6RCx1QkFBdUI7UUFDdkJFLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU13RixtQkFBbUIsT0FBT3JCO1FBQzlCLElBQUk7Z0JBR2lCc0I7WUFGbkIscUNBQXFDO1lBQ3JDLE1BQU1BLFlBQVksTUFBTXJJLDJEQUFPQSxDQUFDRSx5REFBS0EsQ0FBQ0gsOERBQVVBLENBQUNTLDZDQUFFQSxFQUFFLFlBQVlRLFNBQVN1RCxRQUFRLEVBQUU7WUFDcEYsTUFBTStELGNBQWFELHVCQUFBQSxVQUFVRSxJQUFJLENBQUNDLElBQUksQ0FBQ3BJLENBQUFBLE1BQU9BLElBQUlrRCxFQUFFLEtBQUt5RCx1QkFBdENzQiwyQ0FBQUEscUJBQWlEM0QsSUFBSTtZQUV4RSxJQUFJNEQsWUFBWTtnQkFDZDFGLGlCQUFpQjtvQkFDZlUsSUFBSXlEO29CQUNKeEQsTUFBTStFLFdBQVcvRSxJQUFJO29CQUNyQmlFLGFBQWFjLFdBQVdkLFdBQVc7b0JBQ25DQyxPQUFPYSxXQUFXYixLQUFLLElBQUk7b0JBQzNCRSxtQkFBbUJXLFdBQVdYLGlCQUFpQixLQUFLO2dCQUN0RDtnQkFDQWpGLHVCQUF1QjtZQUN6QjtRQUNGLEVBQUUsT0FBT1gsT0FBTztZQUNkbUMsUUFBUW5DLEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xEMEcsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQzNCLFVBQWtCNEI7UUFDNUN2RixzQkFBc0I7WUFDcEJuQyxRQUFRO1lBQ1JvQyxNQUFNO1lBQ05DLElBQUl5RDtZQUNKeEQsTUFBTW9GO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDaEYsUUFBZ0JDO1FBQ3hDVCxzQkFBc0I7WUFDcEJuQyxRQUFRO1lBQ1JvQyxNQUFNO1lBQ05DLElBQUlNO1lBQ0pMLE1BQU1NO1FBQ1I7SUFDRjtJQUVBLGlEQUFpRDtJQUNqRCxNQUFNZ0Ysd0JBQXdCLE9BQU9qRjtRQUNuQyxJQUFJO1lBQ0YsTUFBTWtGLGlCQUFpQi9ILElBQUlOLGtEQUFPQSxFQUFFLFlBQTJDbUQsT0FBL0I1QyxTQUFTdUQsUUFBUSxFQUFDLGVBQW9CLE9BQVBYLFFBQU87WUFDdEYsTUFBTW1GLGtCQUFrQixNQUFNeEkseURBQU9BLENBQUN1STtZQUV0QyxzQ0FBc0M7WUFDdEMsTUFBTUUsaUJBQWlCRCxnQkFBZ0JFLEtBQUssQ0FBQ0MsR0FBRyxDQUFDQyxDQUFBQSxPQUFRN0ksOERBQVlBLENBQUM2STtZQUN0RSxNQUFNQyxRQUFRQyxHQUFHLENBQUNMO1lBRWxCOUUsUUFBUUMsR0FBRyxDQUFDLEdBQTREUCxPQUF6RG1GLGdCQUFnQkUsS0FBSyxDQUFDSyxNQUFNLEVBQUMsOEJBQW1DLE9BQVAxRjtRQUMxRSxFQUFFLE9BQU83QixPQUFPO1lBQ2RtQyxRQUFRQyxHQUFHLENBQUMsZ0VBQTZEcEM7UUFDM0U7SUFDRjtJQUVBLE1BQU13SCxnQkFBZ0I7UUFDcEI5RixjQUFjO1FBRWQsSUFBSTtZQUNGLElBQUlOLG1CQUFtQkUsSUFBSSxLQUFLLFVBQVU7b0JBS2xCOUI7Z0JBSnRCLGdCQUFnQjtnQkFDaEIsTUFBTWxCLDZEQUFTQSxDQUFDRCx1REFBR0EsQ0FBQ0ksNkNBQUVBLEVBQUUsWUFBWVEsU0FBU3VELFFBQVEsRUFBRSxVQUFVcEIsbUJBQW1CRyxFQUFFO2dCQUV0RixpREFBaUQ7Z0JBQ2pELE1BQU1rRyxnQkFBZ0JqSSxFQUFBQSxnQkFBQUEsUUFBUWlILElBQUksQ0FBQ2lCLENBQUFBLElBQUtBLEVBQUVuRyxFQUFFLEtBQUtILG1CQUFtQkcsRUFBRSxlQUFoRC9CLG9DQUFBQSxjQUFtRGdGLEtBQUssS0FBSSxFQUFFO2dCQUNwRixLQUFLLE1BQU1nQixRQUFRaUMsY0FBZTtvQkFDaEMsTUFBTXJKLDZEQUFTQSxDQUFDQyx1REFBR0EsQ0FBQ0ksNkNBQUVBLEVBQUUsWUFBWVEsU0FBU3VELFFBQVEsRUFBRSxhQUFhZ0QsS0FBS2pFLEVBQUUsR0FBRzt3QkFDNUV5RCxVQUFVO3dCQUNWMkMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO29CQUNuQztnQkFDRjtnQkFFQTFGLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJoQixtQkFBbUJHLEVBQUU7WUFDdEQsT0FBTztnQkFDTCxlQUFlO2dCQUNmLE1BQU1qRCw2REFBU0EsQ0FBQ0QsdURBQUdBLENBQUNJLDZDQUFFQSxFQUFFLFlBQVlRLFNBQVN1RCxRQUFRLEVBQUUsYUFBYXBCLG1CQUFtQkcsRUFBRTtnQkFFekYsb0NBQW9DO2dCQUNwQyxJQUFJO29CQUNGLE1BQU11RyxhQUFhOUksSUFBSU4sa0RBQU9BLEVBQUUsWUFBMkMwQyxPQUEvQm5DLFNBQVN1RCxRQUFRLEVBQUMsZUFBbUMsT0FBdEJwQixtQkFBbUJHLEVBQUUsRUFBQztvQkFDakcsTUFBTWhELDhEQUFZQSxDQUFDdUo7Z0JBQ3JCLEVBQUUsT0FBT0MsY0FBYztvQkFDckI1RixRQUFRQyxHQUFHLENBQUMseUNBQXNDMkY7Z0JBQ3BEO2dCQUVBLHNDQUFzQztnQkFDdEMsSUFBSXpJLGdCQUFnQjhCLG1CQUFtQkcsRUFBRSxFQUFFO29CQUN6Q2xDLGFBQWE7Z0JBQ2Y7Z0JBRUE4QyxRQUFRQyxHQUFHLENBQUMsa0JBQWtCaEIsbUJBQW1CRyxFQUFFO1lBQ3JEO1lBRUEsbUJBQW1CO1lBQ25CNkM7WUFFQSxlQUFlO1lBQ2YvQyxzQkFBc0I7Z0JBQ3BCbkMsUUFBUTtnQkFDUm9DLE1BQU07Z0JBQ05DLElBQUk7Z0JBQ0pDLE1BQU07WUFDUjtRQUVGLEVBQUUsT0FBT3hCLE9BQU87WUFDZG1DLFFBQVFuQyxLQUFLLENBQUMsb0JBQW9CQTtZQUNsQzBHLE1BQU07UUFDUixTQUFVO1lBQ1JoRixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSwyQkFBMkI7SUFDM0IsTUFBTXNHLGtCQUFrQixDQUFDbkc7UUFDdkJkLGVBQWVjO0lBQ2pCO0lBRUEsTUFBTW9HLGdCQUFnQjtRQUNwQmxILGVBQWU7UUFDZkUsa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTWlILGlCQUFpQixDQUFDQyxHQUFvQm5EO1FBQzFDbUQsRUFBRUMsY0FBYztRQUNoQm5ILGtCQUFrQitEO0lBQ3BCO0lBRUEsTUFBTXFELGtCQUFrQjtRQUN0QnBILGtCQUFrQjtJQUNwQjtJQUVBLE1BQU1xSCxhQUFhLE9BQU9ILEdBQW9CbkQ7UUFDNUNtRCxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQ3RILGFBQWE7UUFFbEIsSUFBSTtZQUNGLGdDQUFnQztZQUNoQyxNQUFNMUMsNkRBQVNBLENBQUNDLHVEQUFHQSxDQUFDSSw2Q0FBRUEsRUFBRSxZQUFZUSxTQUFTdUQsUUFBUSxFQUFFLGFBQWExQixjQUFjO2dCQUNoRmtFLFVBQVVBO2dCQUNWMkMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO1lBQ25DO1lBRUExRixRQUFRQyxHQUFHLENBQUMsUUFBeUM0QyxPQUFqQ2xFLGFBQVksdUJBQTZDLE9BQXhCa0UsWUFBWTtZQUVqRSwyQ0FBMkM7WUFDM0NaO1FBRUYsRUFBRSxPQUFPcEUsT0FBTztZQUNkbUMsUUFBUW5DLEtBQUssQ0FBQyx1QkFBdUJBO1FBQ3ZDLFNBQVU7WUFDUmUsZUFBZTtZQUNmRSxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLE1BQU1zSCxvQkFBb0IsQ0FBQzFHO1FBQ3pCTSxRQUFRQyxHQUFHLENBQUMsZ0JBQWdCUDtRQUM1Qiw4QkFBOEI7UUFDOUJ1QztRQUNBL0UsYUFBYXdDO0lBQ2Y7SUFFQSxNQUFNMkcsaUJBQWlCLENBQUNoRDtRQUN0QmpGLGVBQWVpRjtRQUNmbkYscUJBQXFCO0lBQ3ZCO0lBRUEsTUFBTW9JLG9CQUFvQixDQUFDNUc7UUFDekJNLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JQO1FBQ2hDLDhCQUE4QjtRQUM5QnVDO1FBQ0EvRCxxQkFBcUI7UUFDckJFLGVBQWU7SUFDakI7SUFJQSxNQUFNbUksa0JBQWtCO1FBQ3RCckosYUFBYSxPQUFPLDhDQUE4QztJQUNwRTtJQUVBLE1BQU1zSixlQUFlLENBQUMzRDtRQUNwQnZGLFdBQVc0QyxDQUFBQSxPQUFRQSxLQUFLOEUsR0FBRyxDQUFDcEMsQ0FBQUEsU0FDMUJBLE9BQU94RCxFQUFFLEtBQUt5RCxXQUNWO29CQUFFLEdBQUdELE1BQU07b0JBQUVZLFlBQVksQ0FBQ1osT0FBT1ksVUFBVTtnQkFBQyxJQUM1Q1o7SUFFUjtJQUVBLE1BQU02RCxhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLE9BQU8sSUFBSWxCLEtBQUtpQjtRQUN0QixNQUFNRSxNQUFNLElBQUluQjtRQUNoQixNQUFNb0IsY0FBYyxDQUFDRCxJQUFJRSxPQUFPLEtBQUtILEtBQUtHLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxFQUFDO1FBRXJFLElBQUlELGNBQWMsSUFBSTtZQUNwQixPQUFPRixLQUFLSSxrQkFBa0IsQ0FBQyxTQUFTO2dCQUN0Q0MsTUFBTTtnQkFDTkMsUUFBUTtZQUNWO1FBQ0YsT0FBTyxJQUFJSixjQUFjLEtBQUs7WUFDNUIsT0FBT0YsS0FBS08sa0JBQWtCLENBQUMsU0FBUztnQkFDdENDLFNBQVM7WUFDWDtRQUNGLE9BQU87WUFDTCxPQUFPUixLQUFLTyxrQkFBa0IsQ0FBQyxTQUFTO2dCQUN0Q0UsS0FBSztnQkFDTEMsT0FBTztZQUNUO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QixNQUFNQyxXQUFzQztZQUMxQyxRQUFRO1lBQ1IsU0FBUztZQUNULFVBQVU7WUFDVixPQUFPO1lBQ1AsVUFBVTtZQUNWLFFBQVE7WUFDUixRQUFRO1lBQ1IsVUFBVTtZQUNWLFFBQVE7WUFDUixRQUFRO1FBQ1Y7UUFDQSxPQUFPQSxRQUFRLENBQUNELFVBQVUsSUFBSUE7SUFDaEM7SUFFQSx3Q0FBd0M7SUFDeEMsTUFBTUUsb0JBQW9CLE9BQU8vSCxRQUFnQmdJO1FBQy9DLElBQUk7Z0JBRWVDO1lBRGpCLE1BQU1BLFVBQVUsTUFBTTdMLDJEQUFPQSxDQUFDRSx5REFBS0EsQ0FBQ0gsOERBQVVBLENBQUNTLDZDQUFFQSxFQUFFLFlBQVlRLFNBQVN1RCxRQUFRLEVBQUU7WUFDbEYsTUFBTXVILFlBQVdELHFCQUFBQSxRQUFRdEQsSUFBSSxDQUFDQyxJQUFJLENBQUNwSSxDQUFBQSxNQUFPQSxJQUFJa0QsRUFBRSxLQUFLTSxxQkFBcENpSSx5Q0FBQUEsbUJBQTZDbkgsSUFBSTtZQUVsRSxJQUFJb0gsWUFBWUEsU0FBUzlFLFFBQVEsRUFBRTtnQkFDakMsT0FBTzhFLFNBQVM5RSxRQUFRLEtBQUs0RTtZQUMvQjtZQUVBLE9BQU8sTUFBTSxtQ0FBbUM7UUFDbEQsRUFBRSxPQUFPN0osT0FBTztZQUNkbUMsUUFBUW5DLEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDLE9BQU87UUFDVDtJQUNGO0lBRUEsTUFBTWdLLHdCQUF3QixDQUFDbkksUUFBZ0JDLFVBQWtCQztRQUMvRCxNQUFNeUQsT0FBTztlQUFJOUY7ZUFBcUJGLFFBQVF5SyxPQUFPLENBQUN2QyxDQUFBQSxJQUFLQSxFQUFFbEQsS0FBSztTQUFFLENBQUNpQyxJQUFJLENBQUN5RCxDQUFBQSxJQUFLQSxFQUFFM0ksRUFBRSxLQUFLTTtRQUV4RixJQUFJMkQsaUJBQUFBLDJCQUFBQSxLQUFNUCxRQUFRLEVBQUU7WUFDbEIsdUNBQXVDO1lBQ3ZDckQsaUJBQWlCO2dCQUNmMUMsUUFBUTtnQkFDUjJDO2dCQUNBQztnQkFDQUM7WUFDRjtRQUNGLE9BQU87WUFDTCxnREFBZ0Q7WUFDaERvSSxjQUFjdEksUUFBUUMsVUFBVUM7UUFDbEM7SUFDRjtJQUVBLE1BQU1vSSxnQkFBZ0IsQ0FBQ3RJLFFBQWdCQyxVQUFrQkM7UUFDdkQsT0FBUUE7WUFDTixLQUFLO2dCQUNIMUMsYUFBYXdDO2dCQUNiO1lBQ0YsS0FBSztnQkFDSCxNQUFNMkQsT0FBTzt1QkFBSTlGO3VCQUFxQkYsUUFBUXlLLE9BQU8sQ0FBQ3ZDLENBQUFBLElBQUtBLEVBQUVsRCxLQUFLO2lCQUFFLENBQUNpQyxJQUFJLENBQUN5RCxDQUFBQSxJQUFLQSxFQUFFM0ksRUFBRSxLQUFLTTtnQkFDeEYsSUFBSTJELE1BQU07b0JBQ1JnRCxlQUFlaEQ7Z0JBQ2pCO2dCQUNBO1lBQ0YsS0FBSztnQkFDSHFCLGlCQUFpQmhGLFFBQVFDO2dCQUN6QjtRQUNKO0lBQ0Y7SUFFQSxNQUFNc0ksd0JBQXdCO1FBQzVCRCxjQUFjeEksY0FBY0UsTUFBTSxFQUFFRixjQUFjRyxRQUFRLEVBQUVILGNBQWNJLE1BQU07UUFDaEZILGlCQUFpQjtZQUNmMUMsUUFBUTtZQUNSMkMsUUFBUTtZQUNSQyxVQUFVO1lBQ1ZDLFFBQVE7UUFDVjtJQUNGO0lBRUEsTUFBTXNJLGdCQUFnQixDQUFDdks7UUFDckIsT0FBTyxJQUFJd0ssS0FBS0MsWUFBWSxDQUFDLFNBQVM7WUFDcENDLE9BQU87WUFDUEMsVUFBVTtRQUNaLEdBQUdDLE1BQU0sQ0FBQzVLO0lBQ1o7SUFFQSxxQkFDRTs7MEJBRUUsOERBQUM2SztnQkFBSUMsV0FBVyxrT0FHb0QsT0FBaEUxTCxTQUFTLGtCQUFrQixzQ0FBcUM7MEJBRWxFLDRFQUFDeUw7b0JBQUlDLFdBQVU7O3NDQUViLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUtELFdBQVU7c0RBQ2IzTCxTQUFTdUQsUUFBUSxDQUFDc0ksTUFBTSxDQUFDLEdBQUdDLFdBQVc7Ozs7Ozs7Ozs7O2tEQUc1Qyw4REFBQ0o7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDSTtnREFBR0osV0FBVTswREFBb0MzTCxTQUFTdUQsUUFBUTs7Ozs7OzRDQUNsRTVDLGtCQUFrQkcsU0FBUyxpQkFDMUIsOERBQUM0SztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzs7Ozs7a0VBQ2YsOERBQUNLO3dEQUFFTCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7NERBRXJDaEwsa0JBQWtCSSxLQUFLLGlCQUN6Qiw4REFBQ2lMO2dEQUFFTCxXQUFVO2dEQUF1Qk0sT0FBT3RMLGtCQUFrQkksS0FBSzswREFBRTs7Ozs7MEVBSXBFLDhEQUFDaUw7Z0RBQUVMLFdBQVU7O29EQUF3QjtvREFDakNoTCxrQkFBa0JFLE9BQU8sQ0FBQ3FMLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7OztrREFJMUMsOERBQUNDO3dDQUNDQyxTQUFTak07d0NBQ1R3TCxXQUFVO2tEQUVWLDRFQUFDVTs0Q0FBSVYsV0FBVTs0Q0FBVVcsTUFBSzs0Q0FBT0MsUUFBTzs0Q0FBZUMsU0FBUTs7OERBQ2pFLDhEQUFDQztvREFBS0MsZUFBYztvREFBUUMsZ0JBQWU7b0RBQVFDLGFBQWE7b0RBQzlEQyxHQUFFOzs7Ozs7OERBQ0osOERBQUNKO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPN0UsOERBQUNuQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNRO29DQUNDQyxTQUFTM0M7b0NBQ1RrQyxXQUFVOztzREFHViw4REFBQ1U7NENBQUlWLFdBQVU7NENBQVVXLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQ2pFLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQzlEQyxHQUFFOzs7Ozs7Ozs7OztzREFFTiw4REFBQ2pCO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBR1IsOERBQUNGO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1E7NENBQ0NDLFNBQVNwRjs0Q0FDVDJFLFdBQVU7OzhEQUdWLDhEQUFDVTtvREFBSVYsV0FBVTtvREFBVVcsTUFBSztvREFBT0MsUUFBTztvREFBZUMsU0FBUTs4REFDakUsNEVBQUNDO3dEQUFLQyxlQUFjO3dEQUFRQyxnQkFBZTt3REFBUUMsYUFBYTt3REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7OERBRXZFLDhEQUFDakI7OERBQUs7Ozs7Ozs7Ozs7OztzREFHUiw4REFBQ087NENBQ0NDLFNBQVNuRjs0Q0FDVDBFLFdBQVU7NENBRVZNLE9BQU07c0RBRU4sNEVBQUNJO2dEQUFJVixXQUFVO2dEQUFVVyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUNqRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUMxREMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPaEIsOERBQUNuQjs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ21COzRDQUFHbkIsV0FBVTs7OERBQ1osOERBQUNEO29EQUFJQyxXQUFVOzs7Ozs7OERBQ2YsOERBQUNDOzhEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1aLDhEQUFDRjtvQ0FBSUMsV0FBVTs4Q0FDWnBMLFFBQVEySCxHQUFHLENBQUMsQ0FBQ3BDLHVCQUNaLDhEQUFDNEY7NENBRUNDLFdBQVU7NENBQ1ZvQixZQUFZLENBQUM3RCxJQUFNRCxlQUFlQyxHQUFHcEQsT0FBT3hELEVBQUU7NENBQzlDMEssYUFBYTVEOzRDQUNiNkQsUUFBUSxDQUFDL0QsSUFBTUcsV0FBV0gsR0FBR3BELE9BQU94RCxFQUFFOzs4REFHdEMsOERBQUNvSjtvREFDQ0MsV0FBVyx3RUFFUDVKLE9BREZFLGtCQUFrQjZELE9BQU94RCxFQUFFLEdBQUcsbUJBQW1CLHdCQUNsRCxLQUFnRixPQUE3RVAsbUJBQW1CK0QsT0FBT3hELEVBQUUsR0FBRywyQ0FBMkM7b0RBQzlFNEssY0FBYyxJQUFNaEwsaUJBQWlCNEQsT0FBT3hELEVBQUU7b0RBQzlDNkssY0FBYyxJQUFNakwsaUJBQWlCO29EQUNyQ2tLLFNBQVMsSUFBTTFDLGFBQWE1RCxPQUFPeEQsRUFBRTs4REFFckMsNEVBQUNvSjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFFYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNVOzRFQUNDVixXQUFXLDJEQUVWLE9BREM3RixPQUFPWSxVQUFVLEdBQUcsY0FBYzs0RUFFcEM0RixNQUFLOzRFQUNMQyxRQUFPOzRFQUNQQyxTQUFRO3NGQUVSLDRFQUFDQztnRkFBS0MsZUFBYztnRkFBUUMsZ0JBQWU7Z0ZBQVFDLGFBQWE7Z0ZBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBS3pFLDhEQUFDbkI7d0VBQ0NDLFdBQVU7d0VBQ1ZKLE9BQU87NEVBQUU2QixpQkFBaUI1QyxrQkFBa0IxRSxPQUFPVyxLQUFLLElBQUk7d0VBQUs7a0ZBRWpFLDRFQUFDNEY7NEVBQ0NWLFdBQVU7NEVBQ1ZKLE9BQU87Z0ZBQUU5RSxPQUFPK0Qsa0JBQWtCMUUsT0FBT1csS0FBSzs0RUFBRTs0RUFDaEQ2RixNQUFLOzRFQUNMRSxTQUFRO3NGQUVSLDRFQUFDQztnRkFBS0ksR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztrRkFLWiw4REFBQ25CO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDbUI7d0ZBQUduQixXQUFVO2tHQUNYN0YsT0FBT3ZELElBQUk7Ozs7OztrR0FFZCw4REFBQ3FKO3dGQUFLRCxXQUFVO2tHQUNiN0YsT0FBT1AsS0FBSyxDQUFDK0MsTUFBTTs7Ozs7Ozs7Ozs7OzRFQUd2QnhDLE9BQU9VLFdBQVcsa0JBQ2pCLDhEQUFDd0Y7Z0ZBQUVMLFdBQVU7MEZBQ1Y3RixPQUFPVSxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEVBTzNCLDhEQUFDa0Y7Z0VBQUlDLFdBQVcsMkRBRWYsT0FEQzFKLGtCQUFrQjZELE9BQU94RCxFQUFFLEdBQUcsOEJBQThCOztrRkFFNUQsOERBQUM2Sjt3RUFDQ1IsV0FBVTt3RUFDVk0sT0FBTTt3RUFDTkcsU0FBUyxDQUFDbEQ7NEVBQ1JBLEVBQUVtRSxlQUFlOzRFQUNqQmpHLGlCQUFpQnRCLE9BQU94RCxFQUFFO3dFQUM1QjtrRkFFQSw0RUFBQytKOzRFQUFJVixXQUFVOzRFQUFjVyxNQUFLOzRFQUFPQyxRQUFPOzRFQUFlQyxTQUFRO3NGQUNyRSw0RUFBQ0M7Z0ZBQUtDLGVBQWM7Z0ZBQVFDLGdCQUFlO2dGQUFRQyxhQUFhO2dGQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tGQUd6RSw4REFBQ1Y7d0VBQ0NSLFdBQVU7d0VBQ1ZNLE9BQU07d0VBQ05HLFNBQVMsQ0FBQ2xEOzRFQUNSQSxFQUFFbUUsZUFBZTs0RUFDakIzRixtQkFBbUI1QixPQUFPeEQsRUFBRSxFQUFFd0QsT0FBT3ZELElBQUk7d0VBQzNDO2tGQUVBLDRFQUFDOEo7NEVBQUlWLFdBQVU7NEVBQWNXLE1BQUs7NEVBQU9DLFFBQU87NEVBQWVDLFNBQVE7c0ZBQ3JFLDRFQUFDQztnRkFBS0MsZUFBYztnRkFBUUMsZ0JBQWU7Z0ZBQVFDLGFBQWE7Z0ZBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnREFROUUvRyxPQUFPWSxVQUFVLGtCQUNoQiw4REFBQ2dGO29EQUFJQyxXQUFVOzhEQUNaN0YsT0FBT1AsS0FBSyxDQUFDMkMsR0FBRyxDQUFDLENBQUMzQixxQkFDakIsOERBQUNtRjs0REFFQzRCLFNBQVM7NERBQ1RDLGFBQWEsSUFBTXhFLGdCQUFnQnhDLEtBQUtqRSxFQUFFOzREQUMxQ2tMLFdBQVd4RTs0REFDWDJDLFdBQVcscUVBSVA5SixPQUhGeEIsZ0JBQWdCa0csS0FBS2pFLEVBQUUsR0FDbkIseUZBQ0Esa0RBQ0wsS0FBd0QsT0FBckRULGdCQUFnQjBFLEtBQUtqRSxFQUFFLEdBQUcsd0JBQXdCOzs4RUFFdEQsOERBQUM2SjtvRUFDQ1IsV0FBVTtvRUFDVlMsU0FBUyxJQUFNckIsc0JBQXNCeEUsS0FBS2pFLEVBQUUsRUFBRWlFLEtBQUtoRSxJQUFJLEVBQUU7O3NGQUV6RCw4REFBQ21KOzRFQUFJQyxXQUFXLDBHQUlmLE9BSEN0TCxnQkFBZ0JrRyxLQUFLakUsRUFBRSxHQUNuQiw2RUFDQTs7Z0ZBR0hpRSxLQUFLUCxRQUFRLGtCQUNaLDhEQUFDMEY7b0ZBQUlDLFdBQVU7OEZBQ2IsNEVBQUNVO3dGQUFJVixXQUFVO3dGQUFxQlcsTUFBSzt3RkFBT0MsUUFBTzt3RkFBZUMsU0FBUTtrR0FDNUUsNEVBQUNDOzRGQUFLQyxlQUFjOzRGQUFRQyxnQkFBZTs0RkFBUUMsYUFBYTs0RkFDMURDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBSWQsOERBQUNSO29GQUFJVixXQUFVO29GQUFxQlcsTUFBSztvRkFBT0MsUUFBTztvRkFBZUMsU0FBUTtvRkFBWUksYUFBYTs4RkFDckcsNEVBQUNIO3dGQUFLQyxlQUFjO3dGQUFRQyxnQkFBZTt3RkFBUUUsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBSXpELDhEQUFDbkI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDbUI7b0ZBQUduQixXQUFXLHVDQUlkLE9BSEN0TCxnQkFBZ0JrRyxLQUFLakUsRUFBRSxHQUNuQixlQUNBOzhGQUVIaUUsS0FBS2hFLElBQUk7Ozs7Ozs4RkFFWiw4REFBQ3lKO29GQUFFTCxXQUFXLGdFQUliLE9BSEN0TCxnQkFBZ0JrRyxLQUFLakUsRUFBRSxHQUNuQixxQkFDQTs4RkFFSGlFLEtBQUtkLFdBQVcsSUFBSTs7Ozs7O2dGQUV0QmMsS0FBS1osZUFBZSxrQkFDbkIsOERBQUNpRztvRkFBS0QsV0FBVyxzQkFJaEIsT0FIQ3RMLGdCQUFnQmtHLEtBQUtqRSxFQUFFLEdBQ25CLHFCQUNBOzhGQUVIcUgsV0FBV3BELEtBQUtaLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFPeEMsOERBQUMrRjtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNROzRFQUNDUixXQUFVOzRFQUNWTSxPQUFNOzRFQUNORyxTQUFTLENBQUNsRDtnRkFDUkEsRUFBRW1FLGVBQWU7Z0ZBQ2pCdEMsc0JBQXNCeEUsS0FBS2pFLEVBQUUsRUFBRWlFLEtBQUtoRSxJQUFJLEVBQUU7NEVBQzVDO3NGQUVBLDRFQUFDOEo7Z0ZBQUlWLFdBQVU7Z0ZBQWNXLE1BQUs7Z0ZBQU9DLFFBQU87Z0ZBQWVDLFNBQVE7MEZBQ3JFLDRFQUFDQztvRkFBS0MsZUFBYztvRkFBUUMsZ0JBQWU7b0ZBQVFDLGFBQWE7b0ZBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7c0ZBR3pFLDhEQUFDVjs0RUFDQ1IsV0FBVTs0RUFDVk0sT0FBTTs0RUFDTkcsU0FBUyxDQUFDbEQ7Z0ZBQ1JBLEVBQUVtRSxlQUFlO2dGQUNqQnRDLHNCQUFzQnhFLEtBQUtqRSxFQUFFLEVBQUVpRSxLQUFLaEUsSUFBSSxFQUFFOzRFQUM1QztzRkFFQSw0RUFBQzhKO2dGQUFJVixXQUFVO2dGQUFjVyxNQUFLO2dGQUFPQyxRQUFPO2dGQUFlQyxTQUFROzBGQUNyRSw0RUFBQ0M7b0ZBQUtDLGVBQWM7b0ZBQVFDLGdCQUFlO29GQUFRQyxhQUFhO29GQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyREFuRnRFdEcsS0FBS2pFLEVBQUU7Ozs7Ozs7Ozs7OzJDQXJHZndELE9BQU94RCxFQUFFOzs7Ozs7Ozs7OzhDQXFNcEIsOERBQUNvSjtvQ0FDQ0MsV0FBVTtvQ0FDVm9CLFlBQVksQ0FBQzdELElBQU1ELGVBQWVDLEdBQUc7b0NBQ3JDOEQsYUFBYTVEO29DQUNiNkQsUUFBUSxDQUFDL0QsSUFBTUcsV0FBV0gsR0FBRzs7c0RBRTdCLDhEQUFDd0M7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUM4QjtnREFBRzlCLFdBQVcsb0hBRWQsT0FEQzVKLG1CQUFtQixRQUFRRixjQUFjLGtCQUFrQjs7a0VBRTNELDhEQUFDNko7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ0M7OzREQUFLOzREQUFXN0osbUJBQW1CLFFBQVFGLGNBQWMsdUNBQXVDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR3JHLDhEQUFDNko7NENBQUlDLFdBQVcsc0RBRWYsT0FEQzVKLG1CQUFtQixRQUFRRixjQUFjLDRFQUE0RTtzREFFcEhwQixpQkFBaUI2SCxNQUFNLEtBQUssa0JBQzNCLDhEQUFDb0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ1U7NERBQUlWLFdBQVU7NERBQXdCVyxNQUFLOzREQUFPQyxRQUFPOzREQUFlQyxTQUFRO3NFQUMvRSw0RUFBQ0M7Z0VBQUtDLGVBQWM7Z0VBQVFDLGdCQUFlO2dFQUFRQyxhQUFhO2dFQUM5REMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztrRUFHUiw4REFBQ2I7d0RBQUVMLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs0REFHdkNsTCxpQkFBaUJ5SCxHQUFHLENBQUMsQ0FBQzNCLHFCQUNwQiw4REFBQ21IO29EQUVDbkgsTUFBTUE7b0RBQ041QyxVQUFVdEQsZ0JBQWdCa0csS0FBS2pFLEVBQUU7b0RBQ2pDOEosU0FBUyxJQUFNckIsc0JBQXNCeEUsS0FBS2pFLEVBQUUsRUFBRWlFLEtBQUtoRSxJQUFJLEVBQUU7b0RBQ3pEb0wsUUFBUSxDQUFDL0ssUUFBUUMsV0FBYWtJLHNCQUFzQm5JLFFBQVFDLFVBQVU7b0RBQ3RFK0ssVUFBVSxDQUFDaEwsUUFBUUMsV0FBYWtJLHNCQUFzQm5JLFFBQVFDLFVBQVU7b0RBQ3hFMEssYUFBYXhFO29EQUNieUUsV0FBV3hFO29EQUNYNkUsWUFBWWhNLGdCQUFnQjBFLEtBQUtqRSxFQUFFO21EQVI5QmlFLEtBQUtqRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2dDQWdCckIvQixRQUFRK0gsTUFBTSxLQUFLLEtBQUs3SCxpQkFBaUI2SCxNQUFNLEtBQUssbUJBQ25ELDhEQUFDb0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ1U7Z0RBQUlWLFdBQVU7Z0RBQW9CVyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUMzRSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUM5REMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztzREFHUiw4REFBQ2I7NENBQUVMLFdBQVU7c0RBQXdCOzs7Ozs7c0RBQ3JDLDhEQUFDSzs0Q0FBRUwsV0FBVTtzREFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNaEQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDUTtnQ0FDQ0MsU0FBU2xNO2dDQUNUeUwsV0FBVTs7a0RBR1YsOERBQUNVO3dDQUFJVixXQUFVO3dDQUFVVyxNQUFLO3dDQUFPQyxRQUFPO3dDQUFlQyxTQUFRO2tEQUNqRSw0RUFBQ0M7NENBQUtDLGVBQWM7NENBQVFDLGdCQUFlOzRDQUFRQyxhQUFhOzRDQUFHQyxHQUFFOzs7Ozs7Ozs7OztrREFFdkUsOERBQUNqQjtrREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPZCw4REFBQ2xNLHdEQUFlQTtnQkFDZE8sUUFBUWdCO2dCQUNSNk0sU0FBUyxJQUFNNU0sdUJBQXVCO2dCQUN0Q3FDLFVBQVV2RCxTQUFTdUQsUUFBUTtnQkFDM0J3SyxlQUFlekU7Ozs7OztZQUloQmpJLDZCQUNDLDhEQUFDM0Isd0RBQWVBO2dCQUNkTyxRQUFRa0I7Z0JBQ1IyTSxTQUFTO29CQUNQMU0scUJBQXFCO29CQUNyQkUsZUFBZTtnQkFDakI7Z0JBQ0FpQyxVQUFVdkQsU0FBU3VELFFBQVE7Z0JBQzNCd0ssZUFBZXZFO2dCQUNmbkksYUFBYUE7Ozs7OzswQkFLakIsOERBQUMxQiwwREFBaUJBO2dCQUNoQk0sUUFBUXNCO2dCQUNSdU0sU0FBUyxJQUFNdE0seUJBQXlCO2dCQUN4QytCLFVBQVV2RCxTQUFTdUQsUUFBUTtnQkFDM0J5SyxpQkFBaUI5Rzs7Ozs7OzBCQUluQiw4REFBQ3ZILDBEQUFpQkE7Z0JBQ2hCTSxRQUFRd0I7Z0JBQ1JxTSxTQUFTO29CQUNQcE0sdUJBQXVCO29CQUN2QkUsaUJBQWlCO2dCQUNuQjtnQkFDQTJCLFVBQVV2RCxTQUFTdUQsUUFBUTtnQkFDM0J5SyxpQkFBaUI3RztnQkFDakJ4RixlQUFlQTs7Ozs7OzBCQUlqQiw4REFBQy9CLDJEQUFrQkE7Z0JBQ2pCSyxRQUFRa0MsbUJBQW1CbEMsTUFBTTtnQkFDakM2TixTQUFTLElBQU0xTCxzQkFBc0I7d0JBQUVuQyxRQUFRO3dCQUFPb0MsTUFBTTt3QkFBUUMsSUFBSTt3QkFBSUMsTUFBTTtvQkFBRztnQkFDckYwTCxXQUFXMUY7Z0JBQ1gwRCxPQUFPOUosbUJBQW1CRSxJQUFJLEtBQUssV0FBVyxrQkFBa0I7Z0JBQ2hFNkwsU0FDRS9MLG1CQUFtQkUsSUFBSSxLQUFLLFdBQ3hCLGlIQUNBO2dCQUVOOEwsVUFBVWhNLG1CQUFtQkksSUFBSTtnQkFDakN6QixXQUFXMEI7Ozs7OzswQkFJYiw4REFBQzNDLCtEQUFzQkE7Z0JBQ3JCSSxRQUFReUMsY0FBY3pDLE1BQU07Z0JBQzVCNk4sU0FBUyxJQUFNbkwsaUJBQWlCO3dCQUFFMUMsUUFBUTt3QkFBTzJDLFFBQVE7d0JBQUlDLFVBQVU7d0JBQUlDLFFBQVE7b0JBQVM7Z0JBQzVGc0wsV0FBV2pEO2dCQUNYdEksVUFBVUgsY0FBY0csUUFBUTtnQkFDaEN3TCxrQkFBa0IsQ0FBQ3JJLFdBQWEyRSxrQkFBa0JqSSxjQUFjRSxNQUFNLEVBQUVvRDs7Ozs7Ozs7QUFJaEY7O0FBRUFsRyxRQUFRd08sV0FBVyxHQUFHO0FBRXRCLCtEQUFleE8sT0FBT0EsRUFBQztBQWN2QixTQUFTNE4sU0FBUyxLQUFnRztRQUFoRyxFQUFFbkgsSUFBSSxFQUFFNUMsUUFBUSxFQUFFeUksT0FBTyxFQUFFdUIsTUFBTSxFQUFFQyxRQUFRLEVBQUVMLFdBQVcsRUFBRUMsU0FBUyxFQUFFSyxVQUFVLEVBQWlCLEdBQWhHO0lBQ2hCLE1BQU1sRSxhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLE9BQU8sSUFBSWxCLEtBQUtpQjtRQUN0QixNQUFNRSxNQUFNLElBQUluQjtRQUNoQixNQUFNb0IsY0FBYyxDQUFDRCxJQUFJRSxPQUFPLEtBQUtILEtBQUtHLE9BQU8sRUFBQyxJQUFNLFFBQU8sS0FBSyxFQUFDO1FBRXJFLElBQUlELGNBQWMsSUFBSTtZQUNwQixPQUFPRixLQUFLSSxrQkFBa0IsQ0FBQyxTQUFTO2dCQUFFQyxNQUFNO2dCQUFXQyxRQUFRO1lBQVU7UUFDL0UsT0FBTyxJQUFJSixjQUFjLEtBQUs7WUFDNUIsT0FBTyxTQUFtQyxPQUE3QndFLEtBQUtDLEtBQUssQ0FBQ3pFLGNBQWMsS0FBSTtRQUM1QyxPQUFPO1lBQ0wsT0FBT0YsS0FBS08sa0JBQWtCLENBQUMsU0FBUztnQkFBRUUsS0FBSztnQkFBV0MsT0FBTztZQUFVO1FBQzdFO0lBQ0Y7SUFFQSxNQUFNa0UsYUFBYSxDQUFDdkY7UUFDbEJBLEVBQUVtRSxlQUFlO1FBQ2pCTSxPQUFPcEgsS0FBS2pFLEVBQUUsRUFBRWlFLEtBQUtoRSxJQUFJO0lBQzNCO0lBRUEsTUFBTW1NLGVBQWUsQ0FBQ3hGO1FBQ3BCQSxFQUFFbUUsZUFBZTtRQUNqQk8sU0FBU3JILEtBQUtqRSxFQUFFLEVBQUVpRSxLQUFLaEUsSUFBSTtJQUM3QjtJQUVBLHFCQUNFLDhEQUFDbUo7UUFDQzRCLFNBQVM7UUFDVEMsYUFBYSxJQUFNQSxZQUFZaEgsS0FBS2pFLEVBQUU7UUFDdENrTCxXQUFXQTtRQUNYN0IsV0FBVyw0RUFJUGtDLE9BSEZsSyxXQUNJLG1CQUNBLG9CQUNMLEtBQTJDLE9BQXhDa0ssYUFBYSx3QkFBd0I7OzBCQUV6Qyw4REFBQzFCO2dCQUNDQyxTQUFTQTtnQkFDVFQsV0FBVTswQkFFViw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVywwR0FJZixPQUhDaEksV0FDSSw2RUFDQTs7Z0NBR0g0QyxLQUFLUCxRQUFRLGtCQUNaLDhEQUFDMEY7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNVO3dDQUFJVixXQUFVO3dDQUFxQlcsTUFBSzt3Q0FBT0MsUUFBTzt3Q0FBZUMsU0FBUTtrREFDNUUsNEVBQUNDOzRDQUFLQyxlQUFjOzRDQUFRQyxnQkFBZTs0Q0FBUUMsYUFBYTs0Q0FDMURDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSWQsOERBQUNSO29DQUFJVixXQUFVO29DQUFxQlcsTUFBSztvQ0FBT0MsUUFBTztvQ0FBZUMsU0FBUTtvQ0FBWUksYUFBYTs4Q0FDckcsNEVBQUNIO3dDQUFLQyxlQUFjO3dDQUFRQyxnQkFBZTt3Q0FBUUUsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBR3pELDhEQUFDbkI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNnRDs0Q0FBR2hELFdBQVU7c0RBQWdDcEYsS0FBS2hFLElBQUk7Ozs7OztzREFDdkQsOERBQUNxSjs0Q0FBS0QsV0FBVTtzREFDYmhDLFdBQVdwRCxLQUFLWixlQUFlOzs7Ozs7Ozs7Ozs7OENBR3BDLDhEQUFDcUc7b0NBQUVMLFdBQVU7OENBQ1ZwRixLQUFLZCxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPekIsOERBQUNpRztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNRO3dCQUNDQyxTQUFTcUM7d0JBQ1Q5QyxXQUFVO3dCQUNWTSxPQUFNO2tDQUVOLDRFQUFDSTs0QkFBSVYsV0FBVTs0QkFBVVcsTUFBSzs0QkFBT0MsUUFBTzs0QkFBZUMsU0FBUTs7OENBQ2pFLDhEQUFDQztvQ0FBS0MsZUFBYztvQ0FBUUMsZ0JBQWU7b0NBQVFDLGFBQWE7b0NBQzFEQyxHQUFFOzs7Ozs7OENBQ1IsOERBQUNKO29DQUFLQyxlQUFjO29DQUFRQyxnQkFBZTtvQ0FBUUMsYUFBYTtvQ0FBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR3pFLDhEQUFDVjt3QkFDQ0MsU0FBU3NDO3dCQUNUL0MsV0FBVTt3QkFDVk0sT0FBTTtrQ0FFTiw0RUFBQ0k7NEJBQUlWLFdBQVU7NEJBQVVXLE1BQUs7NEJBQU9DLFFBQU87NEJBQWVDLFNBQVE7c0NBQ2pFLDRFQUFDQztnQ0FBS0MsZUFBYztnQ0FBUUMsZ0JBQWU7Z0NBQVFDLGFBQWE7Z0NBQzFEQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXBCO01BbkdTYSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvU2lkZWJhci50c3g/OGE4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIGZvcndhcmRSZWYsIHVzZUltcGVyYXRpdmVIYW5kbGUsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGNvbGxlY3Rpb24sIGdldERvY3MsIG9yZGVyQnksIHF1ZXJ5LCB1cGRhdGVEb2MsIGRvYywgZGVsZXRlRG9jIH0gZnJvbSAnZmlyZWJhc2UvZmlyZXN0b3JlJztcbmltcG9ydCB7IHJlZiwgZGVsZXRlT2JqZWN0LCBsaXN0QWxsIH0gZnJvbSAnZmlyZWJhc2Uvc3RvcmFnZSc7XG5pbXBvcnQgeyBkYiwgc3RvcmFnZSB9IGZyb20gJ0AvbGliL2ZpcmViYXNlJztcbmltcG9ydCBDcmVhdGVDaGF0TW9kYWwgZnJvbSAnLi9DcmVhdGVDaGF0TW9kYWwnO1xuaW1wb3J0IENyZWF0ZUZvbGRlck1vZGFsIGZyb20gJy4vQ3JlYXRlRm9sZGVyTW9kYWwnO1xuaW1wb3J0IENvbmZpcm1EZWxldGVNb2RhbCBmcm9tICcuL0NvbmZpcm1EZWxldGVNb2RhbCc7XG5pbXBvcnQgUGFzc3dvcmRQcm90ZWN0ZWRNb2RhbCBmcm9tICcuL1Bhc3N3b3JkUHJvdGVjdGVkTW9kYWwnO1xuXG5pbnRlcmZhY2UgVXNlckRhdGEge1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBiYWxhbmNlOiBudW1iZXI7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgT3BlblJvdXRlckJhbGFuY2Uge1xuICBiYWxhbmNlOiBudW1iZXI7XG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgZXJyb3I/OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBDaGF0IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBsYXN0TWVzc2FnZTogc3RyaW5nO1xuICBsYXN0TWVzc2FnZVRpbWU6IHN0cmluZztcbiAgZm9sZGVyPzogc3RyaW5nO1xuICBwYXNzd29yZD86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIENoYXRGb2xkZXIge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBjb2xvcjogc3RyaW5nO1xuICBjaGF0czogQ2hhdFtdO1xuICBpc0V4cGFuZGVkOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgU2lkZWJhclByb3BzIHtcbiAgdXNlckRhdGE6IFVzZXJEYXRhO1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uVG9nZ2xlOiAoKSA9PiB2b2lkO1xuICBvblNldHRpbmdzT3BlbjogKCkgPT4gdm9pZDtcbiAgb25DaGF0U2VsZWN0OiAoY2hhdElkOiBzdHJpbmcgfCBudWxsKSA9PiB2b2lkO1xuICBjdXJyZW50Q2hhdDogc3RyaW5nIHwgbnVsbDtcbiAgb25VcGRhdGVPcGVuUm91dGVyQmFsYW5jZT86ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IFNpZGViYXIgPSBmb3J3YXJkUmVmPHsgcmVsb2FkQ2hhdHM6ICgpID0+IHZvaWQ7IHVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlOiAoKSA9PiB2b2lkIH0sIFNpZGViYXJQcm9wcz4oKHtcbiAgdXNlckRhdGEsXG4gIGlzT3BlbixcbiAgb25Ub2dnbGUsXG4gIG9uU2V0dGluZ3NPcGVuLFxuICBvbkNoYXRTZWxlY3QsXG4gIGN1cnJlbnRDaGF0LFxuICBvblVwZGF0ZU9wZW5Sb3V0ZXJCYWxhbmNlXG59LCByZWYpID0+IHtcbiAgY29uc3QgW2ZvbGRlcnMsIHNldEZvbGRlcnNdID0gdXNlU3RhdGU8Q2hhdEZvbGRlcltdPihbXSk7XG4gIGNvbnN0IFt1bm9yZ2FuaXplZENoYXRzLCBzZXRVbm9yZ2FuaXplZENoYXRzXSA9IHVzZVN0YXRlPENoYXRbXT4oW10pO1xuICBjb25zdCBbb3BlblJvdXRlckJhbGFuY2UsIHNldE9wZW5Sb3V0ZXJCYWxhbmNlXSA9IHVzZVN0YXRlPE9wZW5Sb3V0ZXJCYWxhbmNlPih7XG4gICAgYmFsYW5jZTogMCxcbiAgICBpc0xvYWRpbmc6IHRydWUsIC8vIEluaWNpYXIgY29tbyBsb2FkaW5nXG4gICAgZXJyb3I6IHVuZGVmaW5lZFxuICB9KTtcbiAgY29uc3QgW2NyZWF0ZUNoYXRNb2RhbE9wZW4sIHNldENyZWF0ZUNoYXRNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdENoYXRNb2RhbE9wZW4sIHNldEVkaXRDaGF0TW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdDaGF0LCBzZXRFZGl0aW5nQ2hhdF0gPSB1c2VTdGF0ZTxDaGF0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjcmVhdGVGb2xkZXJNb2RhbE9wZW4sIHNldENyZWF0ZUZvbGRlck1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlZGl0Rm9sZGVyTW9kYWxPcGVuLCBzZXRFZGl0Rm9sZGVyTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdGb2xkZXIsIHNldEVkaXRpbmdGb2xkZXJdID0gdXNlU3RhdGU8e1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICAgIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICAgIGNvbG9yOiBzdHJpbmc7XG4gICAgZXhwYW5kZWRCeURlZmF1bHQ6IGJvb2xlYW47XG4gIH0gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2RyYWdnZWRDaGF0LCBzZXREcmFnZ2VkQ2hhdF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2RyYWdPdmVyRm9sZGVyLCBzZXREcmFnT3ZlckZvbGRlcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2hvdmVyZWRGb2xkZXIsIHNldEhvdmVyZWRGb2xkZXJdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG5cbiAgLy8gRXN0YWRvcyBwYXJhIG1vZGFpcyBkZSBjb25maXJtYcOnw6NvXG4gIGNvbnN0IFtkZWxldGVDb25maXJtTW9kYWwsIHNldERlbGV0ZUNvbmZpcm1Nb2RhbF0gPSB1c2VTdGF0ZTx7XG4gICAgaXNPcGVuOiBib29sZWFuO1xuICAgIHR5cGU6ICdjaGF0JyB8ICdmb2xkZXInO1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbmFtZTogc3RyaW5nO1xuICB9Pih7XG4gICAgaXNPcGVuOiBmYWxzZSxcbiAgICB0eXBlOiAnY2hhdCcsXG4gICAgaWQ6ICcnLFxuICAgIG5hbWU6ICcnXG4gIH0pO1xuICBjb25zdCBbaXNEZWxldGluZywgc2V0SXNEZWxldGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gRXN0YWRvcyBwYXJhIG1vZGFsIGRlIHNlbmhhXG4gIGNvbnN0IFtwYXNzd29yZE1vZGFsLCBzZXRQYXNzd29yZE1vZGFsXSA9IHVzZVN0YXRlPHtcbiAgICBpc09wZW46IGJvb2xlYW47XG4gICAgY2hhdElkOiBzdHJpbmc7XG4gICAgY2hhdE5hbWU6IHN0cmluZztcbiAgICBhY3Rpb246ICdhY2Nlc3MnIHwgJ2VkaXQnIHwgJ2RlbGV0ZSc7XG4gIH0+KHtcbiAgICBpc09wZW46IGZhbHNlLFxuICAgIGNoYXRJZDogJycsXG4gICAgY2hhdE5hbWU6ICcnLFxuICAgIGFjdGlvbjogJ2FjY2VzcydcbiAgfSk7XG5cbiAgLy8gUmVmIHBhcmEgY29udHJvbGFyIHJlcXVpc2nDp8O1ZXMgZW0gYW5kYW1lbnRvXG4gIGNvbnN0IGZldGNoaW5nQmFsYW5jZVJlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgY29uc3QgZmV0Y2hPcGVuUm91dGVyQmFsYW5jZSA9IGFzeW5jICgpID0+IHtcbiAgICAvLyBFdml0YXIgbcO6bHRpcGxhcyByZXF1aXNpw6fDtWVzIHNpbXVsdMOibmVhc1xuICAgIGlmIChmZXRjaGluZ0JhbGFuY2VSZWYuY3VycmVudCkge1xuICAgICAgY29uc29sZS5sb2coJ1JlcXVpc2nDp8OjbyBkZSBzYWxkbyBqw6EgZW0gYW5kYW1lbnRvLCBpZ25vcmFuZG8uLi4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgZmV0Y2hpbmdCYWxhbmNlUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgY29uc29sZS5sb2coJ0luaWNpYW5kbyBidXNjYSBkbyBzYWxkbyBkbyBPcGVuUm91dGVyLi4uJyk7XG4gICAgICBzZXRPcGVuUm91dGVyQmFsYW5jZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzTG9hZGluZzogdHJ1ZSwgZXJyb3I6IHVuZGVmaW5lZCB9KSk7XG5cbiAgICAgIGxldCBvcGVuUm91dGVyQXBpS2V5ID0gJyc7XG5cbiAgICAgIC8vIFByaW1laXJvLCB0ZW50YXIgYnVzY2FyIG5hIG5vdmEgZXN0cnV0dXJhIChlbmRwb2ludHMpXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBlbmRwb2ludHNSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ2VuZHBvaW50cycpO1xuICAgICAgICBjb25zdCBlbmRwb2ludHNTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MoZW5kcG9pbnRzUmVmKTtcblxuICAgICAgICBlbmRwb2ludHNTbmFwc2hvdC5mb3JFYWNoKChkb2MpID0+IHtcbiAgICAgICAgICBjb25zdCBkYXRhID0gZG9jLmRhdGEoKTtcbiAgICAgICAgICBpZiAoZGF0YS5pc0FjdGl2ZSAmJiBkYXRhLnVybCAmJiBkYXRhLnVybC5pbmNsdWRlcygnb3BlbnJvdXRlci5haScpKSB7XG4gICAgICAgICAgICBvcGVuUm91dGVyQXBpS2V5ID0gZGF0YS5hcGlLZXk7XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdOb3ZhIGVzdHJ1dHVyYSBuw6NvIGVuY29udHJhZGEsIHRlbnRhbmRvIGVzdHJ1dHVyYSBhbnRpZ2EuLi4nKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2UgbsOjbyBlbmNvbnRyb3UgbmEgbm92YSBlc3RydXR1cmEsIGJ1c2NhciBuYSBlc3RydXR1cmEgYW50aWdhXG4gICAgICBpZiAoIW9wZW5Sb3V0ZXJBcGlLZXkpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCB7IGRvYzogZG9jSW1wb3J0LCBnZXREb2MgfSA9IGF3YWl0IGltcG9ydCgnZmlyZWJhc2UvZmlyZXN0b3JlJyk7XG4gICAgICAgICAgY29uc3QgY29uZmlnUmVmID0gZG9jSW1wb3J0KGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ2NvbmZpZ3VyYWNvZXMnLCAnc2V0dGluZ3MnKTtcbiAgICAgICAgICBjb25zdCBjb25maWdEb2MgPSBhd2FpdCBnZXREb2MoY29uZmlnUmVmKTtcblxuICAgICAgICAgIGlmIChjb25maWdEb2MuZXhpc3RzKCkpIHtcbiAgICAgICAgICAgIGNvbnN0IGNvbmZpZyA9IGNvbmZpZ0RvYy5kYXRhKCk7XG4gICAgICAgICAgICBpZiAoY29uZmlnLmVuZHBvaW50cykge1xuICAgICAgICAgICAgICAvLyBCdXNjYXIgZW5kcG9pbnQgYXRpdm8gZG8gT3BlblJvdXRlciBuYSBlc3RydXR1cmEgYW50aWdhXG4gICAgICAgICAgICAgIE9iamVjdC52YWx1ZXMoY29uZmlnLmVuZHBvaW50cykuZm9yRWFjaCgoZW5kcG9pbnQ6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChlbmRwb2ludC5hdGl2byAmJiBlbmRwb2ludC51cmwgJiYgZW5kcG9pbnQudXJsLmluY2x1ZGVzKCdvcGVucm91dGVyLmFpJykpIHtcbiAgICAgICAgICAgICAgICAgIG9wZW5Sb3V0ZXJBcGlLZXkgPSBlbmRwb2ludC5hcGlLZXk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0VzdHJ1dHVyYSBhbnRpZ2EgdGFtYsOpbSBuw6NvIGVuY29udHJhZGEnKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoIW9wZW5Sb3V0ZXJBcGlLZXkpIHtcbiAgICAgICAgc2V0T3BlblJvdXRlckJhbGFuY2UocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICBlcnJvcjogJ05lbmh1bWEgQVBJIGtleSBkbyBPcGVuUm91dGVyIGNvbmZpZ3VyYWRhJ1xuICAgICAgICB9KSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ0FQSSBLZXkgZW5jb250cmFkYSwgYnVzY2FuZG8gc2FsZG8uLi4nKTtcblxuICAgICAgLy8gRmF6ZXIgcmVxdWlzacOnw6NvIHBhcmEgYnVzY2FyIGNyw6lkaXRvc1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9vcGVucm91dGVyL2NyZWRpdHMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBhcGlLZXk6IG9wZW5Sb3V0ZXJBcGlLZXkgfSksXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCdSZXNwb3N0YSBkYSBBUEk6JywgZGF0YSk7XG5cbiAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0T3BlblJvdXRlckJhbGFuY2Uoe1xuICAgICAgICAgIGJhbGFuY2U6IGRhdGEuYmFsYW5jZSxcbiAgICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiB1bmRlZmluZWRcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdTYWxkbyBjYXJyZWdhZG8gY29tIHN1Y2Vzc286JywgZGF0YS5iYWxhbmNlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldE9wZW5Sb3V0ZXJCYWxhbmNlKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IGRhdGEuZXJyb3IgfHwgJ0Vycm8gYW8gYnVzY2FyIHNhbGRvJ1xuICAgICAgICB9KSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCdFcnJvIGFvIGJ1c2NhciBzYWxkbzonLCBkYXRhLmVycm9yKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBidXNjYXIgc2FsZG8gZG8gT3BlblJvdXRlcjonLCBlcnJvcik7XG4gICAgICBzZXRPcGVuUm91dGVyQmFsYW5jZShwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgIGVycm9yOiAnRXJybyBhbyBjb25lY3RhciBjb20gT3BlblJvdXRlcidcbiAgICAgIH0pKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgZmV0Y2hpbmdCYWxhbmNlUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZENoYXRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBDYXJyZWdhciBjaGF0c1xuICAgICAgY29uc3QgY2hhdHNSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ2NvbnZlcnNhcycpO1xuICAgICAgY29uc3QgY2hhdHNRdWVyeSA9IHF1ZXJ5KGNoYXRzUmVmLCBvcmRlckJ5KCdsYXN0VXBkYXRlZEF0JywgJ2Rlc2MnKSk7XG4gICAgICBjb25zdCBjaGF0c1NuYXBzaG90ID0gYXdhaXQgZ2V0RG9jcyhjaGF0c1F1ZXJ5KTtcblxuICAgICAgY29uc3QgY2hhdHM6IENoYXRbXSA9IFtdO1xuICAgICAgY2hhdHNTbmFwc2hvdC5mb3JFYWNoKChkb2MpID0+IHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGRvYy5kYXRhKCk7XG4gICAgICAgIGNoYXRzLnB1c2goe1xuICAgICAgICAgIGlkOiBkb2MuaWQsXG4gICAgICAgICAgbmFtZTogZGF0YS5uYW1lIHx8ICdDb252ZXJzYSBzZW0gbm9tZScsXG4gICAgICAgICAgbGFzdE1lc3NhZ2U6IGRhdGEudWx0aW1hTWVuc2FnZW0gfHwgJ05lbmh1bWEgbWVuc2FnZW0gYWluZGEnLFxuICAgICAgICAgIGxhc3RNZXNzYWdlVGltZTogZGF0YS51bHRpbWFNZW5zYWdlbUVtIHx8IGRhdGEuY3JlYXRlZEF0LFxuICAgICAgICAgIGZvbGRlcjogZGF0YS5mb2xkZXJJZCxcbiAgICAgICAgICBwYXNzd29yZDogZGF0YS5wYXNzd29yZFxuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBDYXJyZWdhciBwYXN0YXNcbiAgICAgIGNvbnN0IGZvbGRlcnNSZWYgPSBjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ3Bhc3RhcycpO1xuICAgICAgY29uc3QgZm9sZGVyc1F1ZXJ5ID0gcXVlcnkoZm9sZGVyc1JlZiwgb3JkZXJCeSgnY3JlYXRlZEF0JywgJ2FzYycpKTtcbiAgICAgIGNvbnN0IGZvbGRlcnNTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MoZm9sZGVyc1F1ZXJ5KTtcblxuICAgICAgY29uc3QgbG9hZGVkRm9sZGVyczogQ2hhdEZvbGRlcltdID0gW107XG4gICAgICBmb2xkZXJzU25hcHNob3QuZm9yRWFjaCgoZG9jKSA9PiB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBkb2MuZGF0YSgpO1xuICAgICAgICBjb25zdCBmb2xkZXJDaGF0cyA9IGNoYXRzLmZpbHRlcihjaGF0ID0+IGNoYXQuZm9sZGVyID09PSBkb2MuaWQpO1xuXG4gICAgICAgIGxvYWRlZEZvbGRlcnMucHVzaCh7XG4gICAgICAgICAgaWQ6IGRvYy5pZCxcbiAgICAgICAgICBuYW1lOiBkYXRhLm5hbWUsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24sXG4gICAgICAgICAgY29sb3I6IGRhdGEuY29sb3IgfHwgJyMzQjgyRjYnLFxuICAgICAgICAgIGlzRXhwYW5kZWQ6IGRhdGEuZXhwYW5kZWRCeURlZmF1bHQgIT09IGZhbHNlLFxuICAgICAgICAgIGNoYXRzOiBmb2xkZXJDaGF0c1xuICAgICAgICB9KTtcbiAgICAgIH0pO1xuXG4gICAgICAvLyBDaGF0cyBzZW0gcGFzdGFcbiAgICAgIGNvbnN0IHVub3JnYW5pemVkID0gY2hhdHMuZmlsdGVyKGNoYXQgPT4gIWNoYXQuZm9sZGVyKTtcblxuICAgICAgc2V0Rm9sZGVycyhsb2FkZWRGb2xkZXJzKTtcbiAgICAgIHNldFVub3JnYW5pemVkQ2hhdHModW5vcmdhbml6ZWQpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gY2FycmVnYXIgY2hhdHMgZSBwYXN0YXM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyBSZWYgcGFyYSBjb250cm9sYXIgc2UgasOhIGZvaSBjYXJyZWdhZG9cbiAgY29uc3QgYmFsYW5jZUxvYWRlZFJlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlckRhdGEudXNlcm5hbWUpIHtcbiAgICAgIGxvYWRDaGF0cygpO1xuXG4gICAgICAvLyBDYXJyZWdhciBzYWxkbyBhcGVuYXMgc2UgYWluZGEgbsOjbyBmb2kgY2FycmVnYWRvXG4gICAgICBpZiAoIWJhbGFuY2VMb2FkZWRSZWYuY3VycmVudCkge1xuICAgICAgICBjb25zb2xlLmxvZygnQ2FycmVnYW5kbyBzYWxkbyBkbyBPcGVuUm91dGVyIHBlbGEgcHJpbWVpcmEgdmV6Li4uJyk7XG4gICAgICAgIGZldGNoT3BlblJvdXRlckJhbGFuY2UoKTtcbiAgICAgICAgYmFsYW5jZUxvYWRlZFJlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt1c2VyRGF0YS51c2VybmFtZV0pO1xuXG4gIC8vIEV4cG9yIGFzIGZ1bsOnw7VlcyBwYXJhIG8gY29tcG9uZW50ZSBwYWlcbiAgdXNlSW1wZXJhdGl2ZUhhbmRsZShyZWYsICgpID0+ICh7XG4gICAgcmVsb2FkQ2hhdHM6IGxvYWRDaGF0cyxcbiAgICB1cGRhdGVPcGVuUm91dGVyQmFsYW5jZTogZmV0Y2hPcGVuUm91dGVyQmFsYW5jZVxuICB9KSk7XG5cbiAgY29uc3QgaGFuZGxlTmV3Q2hhdCA9ICgpID0+IHtcbiAgICBzZXRDcmVhdGVDaGF0TW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU5ld0ZvbGRlciA9ICgpID0+IHtcbiAgICBzZXRDcmVhdGVGb2xkZXJNb2RhbE9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRm9sZGVyQ3JlYXRlZCA9IChmb2xkZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1Bhc3RhIGNyaWFkYTonLCBmb2xkZXJJZCk7XG4gICAgbG9hZENoYXRzKCk7IC8vIFJlY2FycmVnYXIgcGFyYSBtb3N0cmFyIGEgbm92YSBwYXN0YVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZvbGRlclVwZGF0ZWQgPSAoZm9sZGVySWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdQYXN0YSBhdHVhbGl6YWRhOicsIGZvbGRlcklkKTtcbiAgICBsb2FkQ2hhdHMoKTsgLy8gUmVjYXJyZWdhciBwYXJhIG1vc3RyYXIgYXMgYWx0ZXJhw6fDtWVzXG4gICAgc2V0RWRpdEZvbGRlck1vZGFsT3BlbihmYWxzZSk7XG4gICAgc2V0RWRpdGluZ0ZvbGRlcihudWxsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0Rm9sZGVyID0gYXN5bmMgKGZvbGRlcklkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQnVzY2FyIGRhZG9zIGRhIHBhc3RhIG5vIEZpcmVzdG9yZVxuICAgICAgY29uc3QgZm9sZGVyRG9jID0gYXdhaXQgZ2V0RG9jcyhxdWVyeShjb2xsZWN0aW9uKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ3Bhc3RhcycpKSk7XG4gICAgICBjb25zdCBmb2xkZXJEYXRhID0gZm9sZGVyRG9jLmRvY3MuZmluZChkb2MgPT4gZG9jLmlkID09PSBmb2xkZXJJZCk/LmRhdGEoKTtcblxuICAgICAgaWYgKGZvbGRlckRhdGEpIHtcbiAgICAgICAgc2V0RWRpdGluZ0ZvbGRlcih7XG4gICAgICAgICAgaWQ6IGZvbGRlcklkLFxuICAgICAgICAgIG5hbWU6IGZvbGRlckRhdGEubmFtZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogZm9sZGVyRGF0YS5kZXNjcmlwdGlvbixcbiAgICAgICAgICBjb2xvcjogZm9sZGVyRGF0YS5jb2xvciB8fCAnIzNCODJGNicsXG4gICAgICAgICAgZXhwYW5kZWRCeURlZmF1bHQ6IGZvbGRlckRhdGEuZXhwYW5kZWRCeURlZmF1bHQgIT09IGZhbHNlXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRFZGl0Rm9sZGVyTW9kYWxPcGVuKHRydWUpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIGRhZG9zIGRhIHBhc3RhOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvIGFvIGNhcnJlZ2FyIGRhZG9zIGRhIHBhc3RhLicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVGb2xkZXIgPSAoZm9sZGVySWQ6IHN0cmluZywgZm9sZGVyTmFtZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0RGVsZXRlQ29uZmlybU1vZGFsKHtcbiAgICAgIGlzT3BlbjogdHJ1ZSxcbiAgICAgIHR5cGU6ICdmb2xkZXInLFxuICAgICAgaWQ6IGZvbGRlcklkLFxuICAgICAgbmFtZTogZm9sZGVyTmFtZVxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNoYXQgPSAoY2hhdElkOiBzdHJpbmcsIGNoYXROYW1lOiBzdHJpbmcpID0+IHtcbiAgICBzZXREZWxldGVDb25maXJtTW9kYWwoe1xuICAgICAgaXNPcGVuOiB0cnVlLFxuICAgICAgdHlwZTogJ2NoYXQnLFxuICAgICAgaWQ6IGNoYXRJZCxcbiAgICAgIG5hbWU6IGNoYXROYW1lXG4gICAgfSk7XG4gIH07XG5cbiAgLy8gRnVuw6fDo28gcGFyYSBkZWxldGFyIHRvZG9zIG9zIGFuZXhvcyBkZSB1bSBjaGF0XG4gIGNvbnN0IGRlbGV0ZUNoYXRBdHRhY2htZW50cyA9IGFzeW5jIChjaGF0SWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBhdHRhY2htZW50c1JlZiA9IHJlZihzdG9yYWdlLCBgdXN1YXJpb3MvJHt1c2VyRGF0YS51c2VybmFtZX0vY29udmVyc2FzLyR7Y2hhdElkfS9hbmV4b3NgKTtcbiAgICAgIGNvbnN0IGF0dGFjaG1lbnRzTGlzdCA9IGF3YWl0IGxpc3RBbGwoYXR0YWNobWVudHNSZWYpO1xuXG4gICAgICAvLyBEZWxldGFyIHRvZG9zIG9zIGFycXVpdm9zIGRlIGFuZXhvc1xuICAgICAgY29uc3QgZGVsZXRlUHJvbWlzZXMgPSBhdHRhY2htZW50c0xpc3QuaXRlbXMubWFwKGl0ZW0gPT4gZGVsZXRlT2JqZWN0KGl0ZW0pKTtcbiAgICAgIGF3YWl0IFByb21pc2UuYWxsKGRlbGV0ZVByb21pc2VzKTtcblxuICAgICAgY29uc29sZS5sb2coYCR7YXR0YWNobWVudHNMaXN0Lml0ZW1zLmxlbmd0aH0gYW5leG9zIGRlbGV0YWRvcyBkbyBjaGF0ICR7Y2hhdElkfWApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmxvZygnRXJybyBhbyBkZWxldGFyIGFuZXhvcyBvdSBwYXN0YSBkZSBhbmV4b3MgbsOjbyBlbmNvbnRyYWRhOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgY29uZmlybURlbGV0ZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRJc0RlbGV0aW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGlmIChkZWxldGVDb25maXJtTW9kYWwudHlwZSA9PT0gJ2ZvbGRlcicpIHtcbiAgICAgICAgLy8gRGVsZXRhciBwYXN0YVxuICAgICAgICBhd2FpdCBkZWxldGVEb2MoZG9jKGRiLCAndXN1YXJpb3MnLCB1c2VyRGF0YS51c2VybmFtZSwgJ3Bhc3RhcycsIGRlbGV0ZUNvbmZpcm1Nb2RhbC5pZCkpO1xuXG4gICAgICAgIC8vIE1vdmVyIHRvZG9zIG9zIGNoYXRzIGRhIHBhc3RhIHBhcmEgXCJzZW0gcGFzdGFcIlxuICAgICAgICBjb25zdCBjaGF0c0luRm9sZGVyID0gZm9sZGVycy5maW5kKGYgPT4gZi5pZCA9PT0gZGVsZXRlQ29uZmlybU1vZGFsLmlkKT8uY2hhdHMgfHwgW107XG4gICAgICAgIGZvciAoY29uc3QgY2hhdCBvZiBjaGF0c0luRm9sZGVyKSB7XG4gICAgICAgICAgYXdhaXQgdXBkYXRlRG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlckRhdGEudXNlcm5hbWUsICdjb252ZXJzYXMnLCBjaGF0LmlkKSwge1xuICAgICAgICAgICAgZm9sZGVySWQ6IG51bGwsXG4gICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1Bhc3RhIGRlbGV0YWRhOicsIGRlbGV0ZUNvbmZpcm1Nb2RhbC5pZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBEZWxldGFyIGNoYXRcbiAgICAgICAgYXdhaXQgZGVsZXRlRG9jKGRvYyhkYiwgJ3VzdWFyaW9zJywgdXNlckRhdGEudXNlcm5hbWUsICdjb252ZXJzYXMnLCBkZWxldGVDb25maXJtTW9kYWwuaWQpKTtcblxuICAgICAgICAvLyBUZW50YXIgZGVsZXRhciBhcnF1aXZvIGRvIFN0b3JhZ2VcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBzdG9yYWdlUmVmID0gcmVmKHN0b3JhZ2UsIGB1c3Vhcmlvcy8ke3VzZXJEYXRhLnVzZXJuYW1lfS9jb252ZXJzYXMvJHtkZWxldGVDb25maXJtTW9kYWwuaWR9L2NoYXQuanNvbmApO1xuICAgICAgICAgIGF3YWl0IGRlbGV0ZU9iamVjdChzdG9yYWdlUmVmKTtcbiAgICAgICAgfSBjYXRjaCAoc3RvcmFnZUVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0FycXVpdm8gbm8gc3RvcmFnZSBuw6NvIGVuY29udHJhZG86Jywgc3RvcmFnZUVycm9yKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIFNlIGVyYSBvIGNoYXQgYXRpdm8sIGxpbXBhciBzZWxlw6fDo29cbiAgICAgICAgaWYgKGN1cnJlbnRDaGF0ID09PSBkZWxldGVDb25maXJtTW9kYWwuaWQpIHtcbiAgICAgICAgICBvbkNoYXRTZWxlY3QoJycpO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc29sZS5sb2coJ0NoYXQgZGVsZXRhZG86JywgZGVsZXRlQ29uZmlybU1vZGFsLmlkKTtcbiAgICAgIH1cblxuICAgICAgLy8gUmVjYXJyZWdhciBkYWRvc1xuICAgICAgbG9hZENoYXRzKCk7XG5cbiAgICAgIC8vIEZlY2hhciBtb2RhbFxuICAgICAgc2V0RGVsZXRlQ29uZmlybU1vZGFsKHtcbiAgICAgICAgaXNPcGVuOiBmYWxzZSxcbiAgICAgICAgdHlwZTogJ2NoYXQnLFxuICAgICAgICBpZDogJycsXG4gICAgICAgIG5hbWU6ICcnXG4gICAgICB9KTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvIGFvIGRlbGV0YXI6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0Vycm8gYW8gZGVsZXRhci4gVGVudGUgbm92YW1lbnRlLicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0RlbGV0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRnVuw6fDtWVzIGRlIGRyYWcgYW5kIGRyb3BcbiAgY29uc3QgaGFuZGxlRHJhZ1N0YXJ0ID0gKGNoYXRJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0RHJhZ2dlZENoYXQoY2hhdElkKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcmFnRW5kID0gKCkgPT4ge1xuICAgIHNldERyYWdnZWRDaGF0KG51bGwpO1xuICAgIHNldERyYWdPdmVyRm9sZGVyKG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyYWdPdmVyID0gKGU6IFJlYWN0LkRyYWdFdmVudCwgZm9sZGVySWQ6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0RHJhZ092ZXJGb2xkZXIoZm9sZGVySWQpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyYWdMZWF2ZSA9ICgpID0+IHtcbiAgICBzZXREcmFnT3ZlckZvbGRlcihudWxsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gYXN5bmMgKGU6IFJlYWN0LkRyYWdFdmVudCwgZm9sZGVySWQ6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG5cbiAgICBpZiAoIWRyYWdnZWRDaGF0KSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgLy8gQXR1YWxpemFyIG8gY2hhdCBubyBGaXJlc3RvcmVcbiAgICAgIGF3YWl0IHVwZGF0ZURvYyhkb2MoZGIsICd1c3VhcmlvcycsIHVzZXJEYXRhLnVzZXJuYW1lLCAnY29udmVyc2FzJywgZHJhZ2dlZENoYXQpLCB7XG4gICAgICAgIGZvbGRlcklkOiBmb2xkZXJJZCxcbiAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH0pO1xuXG4gICAgICBjb25zb2xlLmxvZyhgQ2hhdCAke2RyYWdnZWRDaGF0fSBtb3ZpZG8gcGFyYSBwYXN0YSAke2ZvbGRlcklkIHx8ICdzZW0gcGFzdGEnfWApO1xuXG4gICAgICAvLyBSZWNhcnJlZ2FyIGNoYXRzIHBhcmEgcmVmbGV0aXIgYSBtdWRhbsOnYVxuICAgICAgbG9hZENoYXRzKCk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyBtb3ZlciBjaGF0OicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0RHJhZ2dlZENoYXQobnVsbCk7XG4gICAgICBzZXREcmFnT3ZlckZvbGRlcihudWxsKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2hhdENyZWF0ZWQgPSAoY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZygnQ2hhdCBjcmlhZG86JywgY2hhdElkKTtcbiAgICAvLyBSZWNhcnJlZ2FyIGEgbGlzdGEgZGUgY2hhdHNcbiAgICBsb2FkQ2hhdHMoKTtcbiAgICBvbkNoYXRTZWxlY3QoY2hhdElkKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVFZGl0Q2hhdCA9IChjaGF0OiBDaGF0KSA9PiB7XG4gICAgc2V0RWRpdGluZ0NoYXQoY2hhdCk7XG4gICAgc2V0RWRpdENoYXRNb2RhbE9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ2hhdFVwZGF0ZWQgPSAoY2hhdElkOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZygnQ2hhdCBhdHVhbGl6YWRvOicsIGNoYXRJZCk7XG4gICAgLy8gUmVjYXJyZWdhciBhIGxpc3RhIGRlIGNoYXRzXG4gICAgbG9hZENoYXRzKCk7XG4gICAgc2V0RWRpdENoYXRNb2RhbE9wZW4oZmFsc2UpO1xuICAgIHNldEVkaXRpbmdDaGF0KG51bGwpO1xuICB9O1xuXG5cblxuICBjb25zdCBoYW5kbGVIb21lQ2xpY2sgPSAoKSA9PiB7XG4gICAgb25DaGF0U2VsZWN0KG51bGwpOyAvLyBMaW1wYXIgY2hhdCBhdHVhbCBwYXJhIGlyIHBhcmEgw6FyZWEgaW5pY2lhbFxuICB9O1xuXG4gIGNvbnN0IHRvZ2dsZUZvbGRlciA9IChmb2xkZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9sZGVycyhwcmV2ID0+IHByZXYubWFwKGZvbGRlciA9PlxuICAgICAgZm9sZGVyLmlkID09PSBmb2xkZXJJZFxuICAgICAgICA/IHsgLi4uZm9sZGVyLCBpc0V4cGFuZGVkOiAhZm9sZGVyLmlzRXhwYW5kZWQgfVxuICAgICAgICA6IGZvbGRlclxuICAgICkpO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAodGltZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVTdHJpbmcpO1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgZGlmZkluSG91cnMgPSAobm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCk7XG5cbiAgICBpZiAoZGlmZkluSG91cnMgPCAyNCkge1xuICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCdwdC1CUicsIHtcbiAgICAgICAgaG91cjogJzItZGlnaXQnLFxuICAgICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgICAgfSk7XG4gICAgfSBlbHNlIGlmIChkaWZmSW5Ib3VycyA8IDE2OCkgeyAvLyA3IGRpYXNcbiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlRGF0ZVN0cmluZygncHQtQlInLCB7XG4gICAgICAgIHdlZWtkYXk6ICdzaG9ydCdcbiAgICAgIH0pO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3B0LUJSJywge1xuICAgICAgICBkYXk6ICcyLWRpZ2l0JyxcbiAgICAgICAgbW9udGg6ICcyLWRpZ2l0J1xuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldEZvbGRlckhleENvbG9yID0gKGNvbG9yTmFtZTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICBjb25zdCBjb2xvck1hcDogeyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfSA9IHtcbiAgICAgICdibHVlJzogJyMzQjgyRjYnLFxuICAgICAgJ2dyZWVuJzogJyMxMEI5ODEnLFxuICAgICAgJ3llbGxvdyc6ICcjRjU5RTBCJyxcbiAgICAgICdyZWQnOiAnI0VGNDQ0NCcsXG4gICAgICAncHVycGxlJzogJyM4QjVDRjYnLFxuICAgICAgJ2N5YW4nOiAnIzA2QjZENCcsXG4gICAgICAnbGltZSc6ICcjODRDQzE2JyxcbiAgICAgICdvcmFuZ2UnOiAnI0Y5NzMxNicsXG4gICAgICAncGluayc6ICcjRUM0ODk5JyxcbiAgICAgICdncmF5JzogJyM2QjcyODAnXG4gICAgfTtcbiAgICByZXR1cm4gY29sb3JNYXBbY29sb3JOYW1lXSB8fCBjb2xvck5hbWU7XG4gIH07XG5cbiAgLy8gRnVuw6fDtWVzIHBhcmEgY2hhdCBwcm90ZWdpZG8gcG9yIHNlbmhhXG4gIGNvbnN0IGNoZWNrQ2hhdFBhc3N3b3JkID0gYXN5bmMgKGNoYXRJZDogc3RyaW5nLCBpbnB1dFBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+ID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY2hhdERvYyA9IGF3YWl0IGdldERvY3MocXVlcnkoY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJywgdXNlckRhdGEudXNlcm5hbWUsICdjb252ZXJzYXMnKSkpO1xuICAgICAgY29uc3QgY2hhdERhdGEgPSBjaGF0RG9jLmRvY3MuZmluZChkb2MgPT4gZG9jLmlkID09PSBjaGF0SWQpPy5kYXRhKCk7XG5cbiAgICAgIGlmIChjaGF0RGF0YSAmJiBjaGF0RGF0YS5wYXNzd29yZCkge1xuICAgICAgICByZXR1cm4gY2hhdERhdGEucGFzc3dvcmQgPT09IGlucHV0UGFzc3dvcmQ7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB0cnVlOyAvLyBTZSBuw6NvIHRlbSBzZW5oYSwgcGVybWl0ZSBhY2Vzc29cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJybyBhbyB2ZXJpZmljYXIgc2VuaGE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQcm90ZWN0ZWRBY3Rpb24gPSAoY2hhdElkOiBzdHJpbmcsIGNoYXROYW1lOiBzdHJpbmcsIGFjdGlvbjogJ2FjY2VzcycgfCAnZWRpdCcgfCAnZGVsZXRlJykgPT4ge1xuICAgIGNvbnN0IGNoYXQgPSBbLi4udW5vcmdhbml6ZWRDaGF0cywgLi4uZm9sZGVycy5mbGF0TWFwKGYgPT4gZi5jaGF0cyldLmZpbmQoYyA9PiBjLmlkID09PSBjaGF0SWQpO1xuXG4gICAgaWYgKGNoYXQ/LnBhc3N3b3JkKSB7XG4gICAgICAvLyBDaGF0IHByb3RlZ2lkbywgYWJyaXIgbW9kYWwgZGUgc2VuaGFcbiAgICAgIHNldFBhc3N3b3JkTW9kYWwoe1xuICAgICAgICBpc09wZW46IHRydWUsXG4gICAgICAgIGNoYXRJZCxcbiAgICAgICAgY2hhdE5hbWUsXG4gICAgICAgIGFjdGlvblxuICAgICAgfSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENoYXQgbsOjbyBwcm90ZWdpZG8sIGV4ZWN1dGFyIGHDp8OjbyBkaXJldGFtZW50ZVxuICAgICAgZXhlY3V0ZUFjdGlvbihjaGF0SWQsIGNoYXROYW1lLCBhY3Rpb24pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBleGVjdXRlQWN0aW9uID0gKGNoYXRJZDogc3RyaW5nLCBjaGF0TmFtZTogc3RyaW5nLCBhY3Rpb246ICdhY2Nlc3MnIHwgJ2VkaXQnIHwgJ2RlbGV0ZScpID0+IHtcbiAgICBzd2l0Y2ggKGFjdGlvbikge1xuICAgICAgY2FzZSAnYWNjZXNzJzpcbiAgICAgICAgb25DaGF0U2VsZWN0KGNoYXRJZCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnZWRpdCc6XG4gICAgICAgIGNvbnN0IGNoYXQgPSBbLi4udW5vcmdhbml6ZWRDaGF0cywgLi4uZm9sZGVycy5mbGF0TWFwKGYgPT4gZi5jaGF0cyldLmZpbmQoYyA9PiBjLmlkID09PSBjaGF0SWQpO1xuICAgICAgICBpZiAoY2hhdCkge1xuICAgICAgICAgIGhhbmRsZUVkaXRDaGF0KGNoYXQpO1xuICAgICAgICB9XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnZGVsZXRlJzpcbiAgICAgICAgaGFuZGxlRGVsZXRlQ2hhdChjaGF0SWQsIGNoYXROYW1lKTtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkU3VjY2VzcyA9ICgpID0+IHtcbiAgICBleGVjdXRlQWN0aW9uKHBhc3N3b3JkTW9kYWwuY2hhdElkLCBwYXNzd29yZE1vZGFsLmNoYXROYW1lLCBwYXNzd29yZE1vZGFsLmFjdGlvbik7XG4gICAgc2V0UGFzc3dvcmRNb2RhbCh7XG4gICAgICBpc09wZW46IGZhbHNlLFxuICAgICAgY2hhdElkOiAnJyxcbiAgICAgIGNoYXROYW1lOiAnJyxcbiAgICAgIGFjdGlvbjogJ2FjY2VzcydcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRCYWxhbmNlID0gKGJhbGFuY2U6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJywge1xuICAgICAgc3R5bGU6ICdjdXJyZW5jeScsXG4gICAgICBjdXJyZW5jeTogJ1VTRCdcbiAgICB9KS5mb3JtYXQoYmFsYW5jZSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qIFNpZGViYXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YFxuICAgICAgICBmaXhlZCB0b3AtMCBsZWZ0LTAgaC1mdWxsIHctODAgYmctZ3JhZGllbnQtdG8tYiBmcm9tLWJsdWUtOTUwLzk1IHZpYS1ibHVlLTkwMC85NSB0by1ibHVlLTk1MC85NVxuICAgICAgICBiYWNrZHJvcC1ibHVyLXhsIGJvcmRlci1yIGJvcmRlci1ibHVlLTcwMC8zMCB6LTUwIHRyYW5zZm9ybSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgc2hhZG93LTJ4bFxuICAgICAgICAke2lzT3BlbiA/ICd0cmFuc2xhdGUteC0wJyA6ICctdHJhbnNsYXRlLXgtZnVsbCBsZzp0cmFuc2xhdGUteC0wJ31cbiAgICAgIGB9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICAgICAgey8qIEhlYWRlciBjb20gcGVyZmlsIGRvIHVzdcOhcmlvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci13aGl0ZS8xMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEwIGgtMTAgYmctYmx1ZS02MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXNlbWlib2xkIHRleHQtbGdcIj5cbiAgICAgICAgICAgICAgICAgIHt1c2VyRGF0YS51c2VybmFtZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPnt1c2VyRGF0YS51c2VybmFtZX08L2gzPlxuICAgICAgICAgICAgICAgIHtvcGVuUm91dGVyQmFsYW5jZS5pc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMyBoLTMgYm9yZGVyIGJvcmRlci1ibHVlLTMwMCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgdGV4dC1zbVwiPkNhcnJlZ2FuZG8uLi48L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApIDogb3BlblJvdXRlckJhbGFuY2UuZXJyb3IgPyAoXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC00MDAgdGV4dC1zbVwiIHRpdGxlPXtvcGVuUm91dGVyQmFsYW5jZS5lcnJvcn0+XG4gICAgICAgICAgICAgICAgICAgICQwLjAwXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAke29wZW5Sb3V0ZXJCYWxhbmNlLmJhbGFuY2UudG9GaXhlZCg0KX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29uU2V0dGluZ3NPcGVufVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgaG92ZXI6dGV4dC13aGl0ZSB0cmFuc2l0aW9uLWNvbG9ycyBwLTFcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IFxuICAgICAgICAgICAgICAgICAgICBkPVwiTTEwLjMyNSA0LjMxN2MuNDI2LTEuNzU2IDIuOTI0LTEuNzU2IDMuMzUgMGExLjcyNCAxLjcyNCAwIDAwMi41NzMgMS4wNjZjMS41NDMtLjk0IDMuMzEuODI2IDIuMzcgMi4zN2ExLjcyNCAxLjcyNCAwIDAwMS4wNjUgMi41NzJjMS43NTYuNDI2IDEuNzU2IDIuOTI0IDAgMy4zNWExLjcyNCAxLjcyNCAwIDAwLTEuMDY2IDIuNTczYy45NCAxLjU0My0uODI2IDMuMzEtMi4zNyAyLjM3YTEuNzI0IDEuNzI0IDAgMDAtMi41NzIgMS4wNjVjLS40MjYgMS43NTYtMi45MjQgMS43NTYtMy4zNSAwYTEuNzI0IDEuNzI0IDAgMDAtMi41NzMtMS4wNjZjLTEuNTQzLjk0LTMuMzEtLjgyNi0yLjM3LTIuMzdhMS43MjQgMS43MjQgMCAwMC0xLjA2NS0yLjU3MmMtMS43NTYtLjQyNi0xLjc1Ni0yLjkyNCAwLTMuMzVhMS43MjQgMS43MjQgMCAwMDEuMDY2LTIuNTczYy0uOTQtMS41NDMuODI2LTMuMzEgMi4zNy0yLjM3Ljk5Ni42MDggMi4yOTYuMDcgMi41NzItMS4wNjV6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xNSAxMmEzIDMgMCAxMS02IDAgMyAzIDAgMDE2IDB6XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBCb3TDtWVzIGRlIG5hdmVnYcOnw6NvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVIb21lQ2xpY2t9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMyByb3VuZGVkLWxnIFxuICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBcbiAgICAgICAgICAgICAgICAgIGQ9XCJNMyAxMmwyLTJtMCAwbDctNyA3IDdNNSAxMHYxMGExIDEgMCAwMDEgMWgzbTEwLTExbDIgMm0tMi0ydjEwYTEgMSAwIDAxLTEgMWgtM20tNiAwYTEgMSAwIDAwMS0xdi00YTEgMSAwIDAxMS0xaDJhMSAxIDAgMDExIDF2NGExIDEgMCAwMDEgMW0tNiAwaDZcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHNwYW4+w4FyZWEgSW5pY2lhbDwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVOZXdDaGF0fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMyByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMiBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA0djE2bTgtOEg0XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5Ob3ZhIENvbnZlcnNhPC9zcGFuPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTmV3Rm9sZGVyfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMCB0ZXh0LXdoaXRlIHB4LTQgcHktMyByb3VuZGVkLWxnXG4gICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIk5vdmEgUGFzdGFcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTMgN3YxMGEyIDIgMCAwMDIgMmgxNGEyIDIgMCAwMDItMlY5YTIgMiAwIDAwLTItMmgtNmwtMi0ySDVhMiAyIDAgMDAtMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2XDp8OjbyBkZSBjb252ZXJzYXMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3cteS1hdXRvIHNjcm9sbGJhci10aGluIHNjcm9sbGJhci10aHVtYi1ibHVlLTkwMCBzY3JvbGxiYXItdHJhY2stdHJhbnNwYXJlbnRcIj5cbiAgICAgICAgICAgIHsvKiBIZWFkZXIgZGFzIGNvbnZlcnNhcyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJvcmRlci1iIGJvcmRlci13aGl0ZS8xMFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIHRleHQtc20gZm9udC1ib2xkIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5Db252ZXJzYXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIExpc3RhIGRlIHBhc3RhcyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtMiBweS0yXCI+XG4gICAgICAgICAgICAgIHtmb2xkZXJzLm1hcCgoZm9sZGVyKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtmb2xkZXIuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi0yXCJcbiAgICAgICAgICAgICAgICAgIG9uRHJhZ092ZXI9eyhlKSA9PiBoYW5kbGVEcmFnT3ZlcihlLCBmb2xkZXIuaWQpfVxuICAgICAgICAgICAgICAgICAgb25EcmFnTGVhdmU9e2hhbmRsZURyYWdMZWF2ZX1cbiAgICAgICAgICAgICAgICAgIG9uRHJvcD17KGUpID0+IGhhbmRsZURyb3AoZSwgZm9sZGVyLmlkKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7LyogQ2FiZcOnYWxobyBkYSBQYXN0YSAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAgcmVsYXRpdmUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgY3Vyc29yLXBvaW50ZXIgJHtcbiAgICAgICAgICAgICAgICAgICAgICBob3ZlcmVkRm9sZGVyID09PSBmb2xkZXIuaWQgPyAnYmctYmx1ZS04MDAvNDAnIDogJ2hvdmVyOmJnLWJsdWUtODAwLzMwJ1xuICAgICAgICAgICAgICAgICAgICB9ICR7ZHJhZ092ZXJGb2xkZXIgPT09IGZvbGRlci5pZCA/ICdiZy1ibHVlLTUwMC8zMCByaW5nLTIgcmluZy1ibHVlLTQwMC81MCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEhvdmVyZWRGb2xkZXIoZm9sZGVyLmlkKX1cbiAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRIb3ZlcmVkRm9sZGVyKG51bGwpfVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVGb2xkZXIoZm9sZGVyLmlkKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDDjWNvbmUgZGUgZXhwYW5zw6NvICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00IHRleHQtYmx1ZS0zMDAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb2xkZXIuaXNFeHBhbmRlZCA/ICdyb3RhdGUtOTAnIDogJydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05IDVsNyA3LTcgN1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDDjWNvbmUgZGEgcGFzdGEgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNiBoLTYgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmbGV4LXNocmluay0wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiBnZXRGb2xkZXJIZXhDb2xvcihmb2xkZXIuY29sb3IpICsgJzQwJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgY29sb3I6IGdldEZvbGRlckhleENvbG9yKGZvbGRlci5jb2xvcikgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTAgNEg0Yy0xLjExIDAtMiAuODktMiAydjEyYzAgMS4xMS44OSAyIDIgMmgxNmMxLjExIDAgMi0uODkgMi0yVjhjMC0xLjExLS44OS0yLTItMmgtOGwtMi0yelwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBOb21lIGRhIHBhc3RhIGUgY29udGFkb3IgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtYmFzZSBmb250LXNlbWlib2xkIHRleHQtYmx1ZS0xMDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb2xkZXIubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTMwMC83MCBiZy1ibHVlLTkwMC81MCBweC0yIHB5LTAuNSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb2xkZXIuY2hhdHMubGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb2xkZXIuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTMwMC82MCB0cnVuY2F0ZSBtdC0wLjVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb2xkZXIuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQcOnw7VlcyBkYSBwYXN0YSAodmlzw612ZWlzIG5vIGhvdmVyKSAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhvdmVyZWRGb2xkZXIgPT09IGZvbGRlci5pZCA/ICdvcGFjaXR5LTEwMCB0cmFuc2xhdGUteC0wJyA6ICdvcGFjaXR5LTAgdHJhbnNsYXRlLXgtMidcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHJvdW5kZWQtbGcgYmctYmx1ZS03MDAvOTAgaG92ZXI6YmctYmx1ZS02MDAvOTAgdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdGFyIHBhc3RhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUVkaXRGb2xkZXIoZm9sZGVyLmlkKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMuNSBoLTMuNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMSA1SDZhMiAyIDAgMDAtMiAydjExYTIgMiAwIDAwMiAyaDExYTIgMiAwIDAwMi0ydi01bS0xLjQxNC05LjQxNGEyIDIgMCAxMTIuODI4IDIuODI4TDExLjgyOCAxNUg5di0yLjgyOGw4LjU4Ni04LjU4NnpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSByb3VuZGVkLWxnIGJnLWJsdWUtNzAwLzkwIGhvdmVyOmJnLXJlZC02MDAvOTAgdGV4dC1ibHVlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRhciBwYXN0YVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGVGb2xkZXIoZm9sZGVyLmlkLCBmb2xkZXIubmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zLjUgaC0zLjVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTkgN2wtLjg2NyAxMi4xNDJBMiAyIDAgMDExNi4xMzggMjFINy44NjJhMiAyIDAgMDEtMS45OTUtMS44NThMNSA3bTUgNHY2bTQtNnY2bTEtMTBWNGExIDEgMCAwMC0xLTFoLTRhMSAxIDAgMDAtMSAxdjNNNCA3aDE2XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgey8qIExpc3RhIGRlIGNvbnZlcnNhcyAocXVhbmRvIGV4cGFuZGlkYSkgKi99XG4gICAgICAgICAgICAgICAgICB7Zm9sZGVyLmlzRXhwYW5kZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTYgbXQtMiBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Zm9sZGVyLmNoYXRzLm1hcCgoY2hhdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NoYXQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdTdGFydD17KCkgPT4gaGFuZGxlRHJhZ1N0YXJ0KGNoYXQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdFbmQ9e2hhbmRsZURyYWdFbmR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGdyb3VwIHJlbGF0aXZlIHJvdW5kZWQteGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGN1cnNvci1tb3ZlICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3VycmVudENoYXQgPT09IGNoYXQuaWRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMC8yMCB0by1jeWFuLTYwMC8yMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzMwIHNoYWRvdy1sZydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLWJsdWUtODAwLzMwIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0gJHtkcmFnZ2VkQ2hhdCA9PT0gY2hhdC5pZCA/ICdvcGFjaXR5LTUwIHNjYWxlLTk1JyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1sZWZ0IHAtMyBmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUHJvdGVjdGVkQWN0aW9uKGNoYXQuaWQsIGNoYXQubmFtZSwgJ2FjY2VzcycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTggaC04IHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZmxleC1zaHJpbmstMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgcmVsYXRpdmUgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnJlbnRDaGF0ID09PSBjaGF0LmlkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDAgc2hhZG93LWxnIHNoYWRvdy1ibHVlLTUwMC8zMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctYmx1ZS03MDAvNTAgZ3JvdXAtaG92ZXI6YmctYmx1ZS02MDAvNzAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIMONY29uZSBkZSBzZW5oYSBzZSBvIGNoYXQgZm9yIHByb3RlZ2lkbyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjaGF0LnBhc3N3b3JkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTEgLXJpZ2h0LTEgdy0zIGgtMyBiZy1yZWQtNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBib3JkZXIgYm9yZGVyLXNsYXRlLTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0yIGgtMiB0ZXh0LXdoaXRlXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTEyIDE1djJtLTYgNGgxMmEyIDIgMCAwMDItMnYtNmEyIDIgMCAwMC0yLTJINmEyIDIgMCAwMC0yIDJ2NmEyIDIgMCAwMDIgMnptMTAtMTBWN2E0IDQgMCAwMC04IDB2NGg4elwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlV2lkdGg9ezJ9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgZD1cIk04IDEyaC4wMU0xMiAxMmguMDFNMTYgMTJoLjAxTTIxIDEyYzAgNC40MTgtNC4wMyA4LTkgOGE5Ljg2MyA5Ljg2MyAwIDAxLTQuMjU1LS45NDlMMyAyMGwxLjM5NS0zLjcyQzMuNTEyIDE1LjA0MiAzIDEzLjU3NCAzIDEyYzAtNC40MTggNC4wMy04IDktOHM5IDMuNTgyIDkgOHpcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LWhpZGRlbiBmbGV4LTEgbWluLXctMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZ3JvdXAtaG92ZXI6cHItMjBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9e2B0cnVuY2F0ZSB0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgbWItMSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhdCA9PT0gY2hhdC5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ibHVlLTEwMCBncm91cC1ob3Zlcjp0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hhdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRydW5jYXRlIHRleHQteHMgbGVhZGluZy1yZWxheGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhdCA9PT0gY2hhdC5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtYmx1ZS0yMDAvODAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ibHVlLTMwMC83MCBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtMjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hhdC5sYXN0TWVzc2FnZSB8fCAnTmVuaHVtYSBtZW5zYWdlbSBhaW5kYS4uLid9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2hhdC5sYXN0TWVzc2FnZVRpbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgYmxvY2sgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50Q2hhdCA9PT0gY2hhdC5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ibHVlLTMwMC82MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtYmx1ZS00MDAvNTAgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTMwMC83MCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lKGNoYXQubGFzdE1lc3NhZ2VUaW1lKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIEHDp8O1ZXMgZGEgY29udmVyc2EgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgcm91bmRlZC1sZyBiZy1ibHVlLTcwMC85MCBob3ZlcjpiZy1ibHVlLTYwMC85MCB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0YXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlUHJvdGVjdGVkQWN0aW9uKGNoYXQuaWQsIGNoYXQubmFtZSwgJ2VkaXQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMuNSBoLTMuNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTEgNUg2YTIgMiAwIDAwLTIgMnYxMWEyIDIgMCAwMDIgMmgxMWEyIDIgMCAwMDItMnYtNW0tMS40MTQtOS40MTRhMiAyIDAgMTEyLjgyOCAyLjgyOEwxMS44MjggMTVIOXYtMi44MjhsOC41ODYtOC41ODZ6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IHJvdW5kZWQtbGcgYmctYmx1ZS03MDAvOTAgaG92ZXI6YmctcmVkLTYwMC85MCB0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGJhY2tkcm9wLWJsdXItc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGFyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVByb3RlY3RlZEFjdGlvbihjaGF0LmlkLCBjaGF0Lm5hbWUsICdkZWxldGUnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMuNSBoLTMuNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTkgN2wtLjg2NyAxMi4xNDJBMiAyIDAgMDExNi4xMzggMjFINy44NjJhMiAyIDAgMDEtMS45OTUtMS44NThMNSA3bTUgNHY2bTQtNnY2bTEtMTBWNGExIDEgMCAwMC0xLTFoLTRhMSAxIDAgMDAtMSAxdjNNNCA3aDE2XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZcOnw6NvIHNlbSBwYXN0YSAtIHNlbXByZSB2aXPDrXZlbCAqL31cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBweS0yXCJcbiAgICAgICAgICAgICAgb25EcmFnT3Zlcj17KGUpID0+IGhhbmRsZURyYWdPdmVyKGUsIG51bGwpfVxuICAgICAgICAgICAgICBvbkRyYWdMZWF2ZT17aGFuZGxlRHJhZ0xlYXZlfVxuICAgICAgICAgICAgICBvbkRyb3A9eyhlKSA9PiBoYW5kbGVEcm9wKGUsIG51bGwpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMlwiPlxuICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9e2B0ZXh0LWJsdWUtMzAwIHRleHQteHMgZm9udC1ib2xkIHVwcGVyY2FzZSB0cmFja2luZy13aWRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yICR7XG4gICAgICAgICAgICAgICAgICBkcmFnT3ZlckZvbGRlciA9PT0gbnVsbCAmJiBkcmFnZ2VkQ2hhdCA/ICd0ZXh0LWJsdWUtNDAwJyA6ICcnXG4gICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWJsdWUtNDAwIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPlNlbSBQYXN0YSB7ZHJhZ092ZXJGb2xkZXIgPT09IG51bGwgJiYgZHJhZ2dlZENoYXQgPyAnKFNvbHRlIGFxdWkgcGFyYSByZW1vdmVyIGRhIHBhc3RhKScgOiAnJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oNT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgc3BhY2UteS0xIG1pbi1oLVs2MHB4XSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgJHtcbiAgICAgICAgICAgICAgICBkcmFnT3ZlckZvbGRlciA9PT0gbnVsbCAmJiBkcmFnZ2VkQ2hhdCA/ICdiZy1ibHVlLTUwMC8xMCByb3VuZGVkLWxnIHAtMiBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ibHVlLTQwMC81MCcgOiAnJ1xuICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAge3Vub3JnYW5pemVkQ2hhdHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy04IGgtOCBiZy13aGl0ZS81IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC13aGl0ZS8zMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTggMTJoLjAxTTEyIDEyaC4wMU0xNiAxMmguMDFNMjEgMTJjMCA0LjQxOC00LjAzIDgtOSA4YTkuODYzIDkuODYzIDAgMDEtNC4yNTUtLjk0OUwzIDIwbDEuMzk1LTMuNzJDMy41MTIgMTUuMDQyIDMgMTMuNTc0IDMgMTJjMC00LjQxOCA0LjAzLTggOS04czkgMy41ODIgOSA4elwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzQwIHRleHQteHNcIj5OZW5odW1hIGNvbnZlcnNhIHNlbSBwYXN0YTwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICB1bm9yZ2FuaXplZENoYXRzLm1hcCgoY2hhdCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8Q2hhdEl0ZW1cbiAgICAgICAgICAgICAgICAgICAgICBrZXk9e2NoYXQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgY2hhdD17Y2hhdH1cbiAgICAgICAgICAgICAgICAgICAgICBpc0FjdGl2ZT17Y3VycmVudENoYXQgPT09IGNoYXQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUHJvdGVjdGVkQWN0aW9uKGNoYXQuaWQsIGNoYXQubmFtZSwgJ2FjY2VzcycpfVxuICAgICAgICAgICAgICAgICAgICAgIG9uRWRpdD17KGNoYXRJZCwgY2hhdE5hbWUpID0+IGhhbmRsZVByb3RlY3RlZEFjdGlvbihjaGF0SWQsIGNoYXROYW1lLCAnZWRpdCcpfVxuICAgICAgICAgICAgICAgICAgICAgIG9uRGVsZXRlPXsoY2hhdElkLCBjaGF0TmFtZSkgPT4gaGFuZGxlUHJvdGVjdGVkQWN0aW9uKGNoYXRJZCwgY2hhdE5hbWUsICdkZWxldGUnKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkRyYWdTdGFydD17aGFuZGxlRHJhZ1N0YXJ0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uRHJhZ0VuZD17aGFuZGxlRHJhZ0VuZH1cbiAgICAgICAgICAgICAgICAgICAgICBpc0RyYWdnaW5nPXtkcmFnZ2VkQ2hhdCA9PT0gY2hhdC5pZH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEVzdGFkbyB2YXppbyAqL31cbiAgICAgICAgICAgIHtmb2xkZXJzLmxlbmd0aCA9PT0gMCAmJiB1bm9yZ2FuaXplZENoYXRzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzMwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0xMiBoLTEyIG14LWF1dG9cIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBcbiAgICAgICAgICAgICAgICAgICAgICBkPVwiTTggMTJoLjAxTTEyIDEyaC4wMU0xNiAxMmguMDFNMjEgMTJjMCA0LjQxOC00LjAzIDgtOSA4YTkuODYzIDkuODYzIDAgMDEtNC4yNTUtLjk0OUwzIDIwbDEuMzk1LTMuNzJDMy41MTIgMTUuMDQyIDMgMTMuNTc0IDMgMTJjMC00LjQxOCA0LjAzLTggOS04czkgMy41ODIgOSA4elwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzUwIHRleHQtc21cIj5OZW5odW1hIGNvbnZlcnNhIGFpbmRhPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvMzAgdGV4dC14cyBtdC0xXCI+Q2xpcXVlIGVtIFwiTm92YSBDb252ZXJzYVwiIHBhcmEgY29tZcOnYXI8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBCb3TDo28gZGUgZmVjaGFyIChtb2JpbGUpICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIHAtNCBib3JkZXItdCBib3JkZXItd2hpdGUvMTBcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy13aGl0ZS8xMCBob3ZlcjpiZy13aGl0ZS8yMCB0ZXh0LXdoaXRlIHB4LTQgcHktMiByb3VuZGVkLWxnIFxuICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHNwYW4+RmVjaGFyPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBjcmlhciBjb252ZXJzYSAqL31cbiAgICAgIDxDcmVhdGVDaGF0TW9kYWxcbiAgICAgICAgaXNPcGVuPXtjcmVhdGVDaGF0TW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRDcmVhdGVDaGF0TW9kYWxPcGVuKGZhbHNlKX1cbiAgICAgICAgdXNlcm5hbWU9e3VzZXJEYXRhLnVzZXJuYW1lfVxuICAgICAgICBvbkNoYXRDcmVhdGVkPXtoYW5kbGVDaGF0Q3JlYXRlZH1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBlZGl0YXIgY29udmVyc2EgKi99XG4gICAgICB7ZWRpdGluZ0NoYXQgJiYgKFxuICAgICAgICA8Q3JlYXRlQ2hhdE1vZGFsXG4gICAgICAgICAgaXNPcGVuPXtlZGl0Q2hhdE1vZGFsT3Blbn1cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgICBzZXRFZGl0Q2hhdE1vZGFsT3BlbihmYWxzZSk7XG4gICAgICAgICAgICBzZXRFZGl0aW5nQ2hhdChudWxsKTtcbiAgICAgICAgICB9fVxuICAgICAgICAgIHVzZXJuYW1lPXt1c2VyRGF0YS51c2VybmFtZX1cbiAgICAgICAgICBvbkNoYXRDcmVhdGVkPXtoYW5kbGVDaGF0VXBkYXRlZH1cbiAgICAgICAgICBlZGl0aW5nQ2hhdD17ZWRpdGluZ0NoYXR9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7LyogTW9kYWwgZGUgY3JpYXIgcGFzdGEgKi99XG4gICAgICA8Q3JlYXRlRm9sZGVyTW9kYWxcbiAgICAgICAgaXNPcGVuPXtjcmVhdGVGb2xkZXJNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldENyZWF0ZUZvbGRlck1vZGFsT3BlbihmYWxzZSl9XG4gICAgICAgIHVzZXJuYW1lPXt1c2VyRGF0YS51c2VybmFtZX1cbiAgICAgICAgb25Gb2xkZXJDcmVhdGVkPXtoYW5kbGVGb2xkZXJDcmVhdGVkfVxuICAgICAgLz5cblxuICAgICAgey8qIE1vZGFsIGRlIGVkaXRhciBwYXN0YSAqL31cbiAgICAgIDxDcmVhdGVGb2xkZXJNb2RhbFxuICAgICAgICBpc09wZW49e2VkaXRGb2xkZXJNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICBzZXRFZGl0Rm9sZGVyTW9kYWxPcGVuKGZhbHNlKTtcbiAgICAgICAgICBzZXRFZGl0aW5nRm9sZGVyKG51bGwpO1xuICAgICAgICB9fVxuICAgICAgICB1c2VybmFtZT17dXNlckRhdGEudXNlcm5hbWV9XG4gICAgICAgIG9uRm9sZGVyQ3JlYXRlZD17aGFuZGxlRm9sZGVyVXBkYXRlZH1cbiAgICAgICAgZWRpdGluZ0ZvbGRlcj17ZWRpdGluZ0ZvbGRlcn1cbiAgICAgIC8+XG5cbiAgICAgIHsvKiBNb2RhbCBkZSBjb25maXJtYcOnw6NvIGRlIGV4Y2x1c8OjbyAqL31cbiAgICAgIDxDb25maXJtRGVsZXRlTW9kYWxcbiAgICAgICAgaXNPcGVuPXtkZWxldGVDb25maXJtTW9kYWwuaXNPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXREZWxldGVDb25maXJtTW9kYWwoeyBpc09wZW46IGZhbHNlLCB0eXBlOiAnY2hhdCcsIGlkOiAnJywgbmFtZTogJycgfSl9XG4gICAgICAgIG9uQ29uZmlybT17Y29uZmlybURlbGV0ZX1cbiAgICAgICAgdGl0bGU9e2RlbGV0ZUNvbmZpcm1Nb2RhbC50eXBlID09PSAnZm9sZGVyJyA/ICdEZWxldGFyIFBhc3RhJyA6ICdEZWxldGFyIENvbnZlcnNhJ31cbiAgICAgICAgbWVzc2FnZT17XG4gICAgICAgICAgZGVsZXRlQ29uZmlybU1vZGFsLnR5cGUgPT09ICdmb2xkZXInXG4gICAgICAgICAgICA/ICdUZW0gY2VydGV6YSBxdWUgZGVzZWphIGRlbGV0YXIgZXN0YSBwYXN0YT8gVG9kYXMgYXMgY29udmVyc2FzIGRlbnRybyBkZWxhIHNlcsOjbyBtb3ZpZGFzIHBhcmEgXCJTZW0gUGFzdGFcIi4nXG4gICAgICAgICAgICA6ICdUZW0gY2VydGV6YSBxdWUgZGVzZWphIGRlbGV0YXIgZXN0YSBjb252ZXJzYT8gVG9kYXMgYXMgbWVuc2FnZW5zIHNlcsOjbyBwZXJkaWRhcyBwZXJtYW5lbnRlbWVudGUuJ1xuICAgICAgICB9XG4gICAgICAgIGl0ZW1OYW1lPXtkZWxldGVDb25maXJtTW9kYWwubmFtZX1cbiAgICAgICAgaXNMb2FkaW5nPXtpc0RlbGV0aW5nfVxuICAgICAgLz5cblxuICAgICAgey8qIE1vZGFsIGRlIHNlbmhhIHBhcmEgY2hhdCBwcm90ZWdpZG8gKi99XG4gICAgICA8UGFzc3dvcmRQcm90ZWN0ZWRNb2RhbFxuICAgICAgICBpc09wZW49e3Bhc3N3b3JkTW9kYWwuaXNPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRQYXNzd29yZE1vZGFsKHsgaXNPcGVuOiBmYWxzZSwgY2hhdElkOiAnJywgY2hhdE5hbWU6ICcnLCBhY3Rpb246ICdhY2Nlc3MnIH0pfVxuICAgICAgICBvblN1Y2Nlc3M9e2hhbmRsZVBhc3N3b3JkU3VjY2Vzc31cbiAgICAgICAgY2hhdE5hbWU9e3Bhc3N3b3JkTW9kYWwuY2hhdE5hbWV9XG4gICAgICAgIG9uUGFzc3dvcmRTdWJtaXQ9eyhwYXNzd29yZCkgPT4gY2hlY2tDaGF0UGFzc3dvcmQocGFzc3dvcmRNb2RhbC5jaGF0SWQsIHBhc3N3b3JkKX1cbiAgICAgIC8+XG4gICAgPC8+XG4gICk7XG59KTtcblxuU2lkZWJhci5kaXNwbGF5TmFtZSA9ICdTaWRlYmFyJztcblxuZXhwb3J0IGRlZmF1bHQgU2lkZWJhcjtcblxuLy8gQ29tcG9uZW50ZSBwYXJhIGl0ZW0gZGUgY2hhdFxuaW50ZXJmYWNlIENoYXRJdGVtUHJvcHMge1xuICBjaGF0OiBDaGF0O1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgb25DbGljazogKCkgPT4gdm9pZDtcbiAgb25FZGl0OiAoY2hhdElkOiBzdHJpbmcsIGNoYXROYW1lOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uRGVsZXRlOiAoY2hhdElkOiBzdHJpbmcsIGNoYXROYW1lOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uRHJhZ1N0YXJ0OiAoY2hhdElkOiBzdHJpbmcpID0+IHZvaWQ7XG4gIG9uRHJhZ0VuZDogKCkgPT4gdm9pZDtcbiAgaXNEcmFnZ2luZzogYm9vbGVhbjtcbn1cblxuZnVuY3Rpb24gQ2hhdEl0ZW0oeyBjaGF0LCBpc0FjdGl2ZSwgb25DbGljaywgb25FZGl0LCBvbkRlbGV0ZSwgb25EcmFnU3RhcnQsIG9uRHJhZ0VuZCwgaXNEcmFnZ2luZyB9OiBDaGF0SXRlbVByb3BzKSB7XG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAodGltZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVTdHJpbmcpO1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgZGlmZkluSG91cnMgPSAobm93LmdldFRpbWUoKSAtIGRhdGUuZ2V0VGltZSgpKSAvICgxMDAwICogNjAgKiA2MCk7XG5cbiAgICBpZiAoZGlmZkluSG91cnMgPCAyNCkge1xuICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCdwdC1CUicsIHsgaG91cjogJzItZGlnaXQnLCBtaW51dGU6ICcyLWRpZ2l0JyB9KTtcbiAgICB9IGVsc2UgaWYgKGRpZmZJbkhvdXJzIDwgMTY4KSB7IC8vIDcgZGlhc1xuICAgICAgcmV0dXJuIGBow6EgJHtNYXRoLmZsb29yKGRpZmZJbkhvdXJzIC8gMjQpfSBkaWFzYDtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCdwdC1CUicsIHsgZGF5OiAnMi1kaWdpdCcsIG1vbnRoOiAnMi1kaWdpdCcgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXQgPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgb25FZGl0KGNoYXQuaWQsIGNoYXQubmFtZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIG9uRGVsZXRlKGNoYXQuaWQsIGNoYXQubmFtZSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBkcmFnZ2FibGVcbiAgICAgIG9uRHJhZ1N0YXJ0PXsoKSA9PiBvbkRyYWdTdGFydChjaGF0LmlkKX1cbiAgICAgIG9uRHJhZ0VuZD17b25EcmFnRW5kfVxuICAgICAgY2xhc3NOYW1lPXtgcmVsYXRpdmUgdy1mdWxsIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGdyb3VwIGN1cnNvci1tb3ZlICR7XG4gICAgICAgIGlzQWN0aXZlXG4gICAgICAgICAgPyAnYmctYmx1ZS02MDAvNTAnXG4gICAgICAgICAgOiAnaG92ZXI6Ymctd2hpdGUvNSdcbiAgICAgIH0gJHtpc0RyYWdnaW5nID8gJ29wYWNpdHktNTAgc2NhbGUtOTUnIDogJyd9YH1cbiAgICA+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9e29uQ2xpY2t9XG4gICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0ZXh0LWxlZnQgcHgtMyBweS0zIHRleHQtd2hpdGUvODAgaG92ZXI6dGV4dC13aGl0ZVwiXG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctOCBoLTggcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBmbGV4LXNocmluay0wIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCByZWxhdGl2ZSAke1xuICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgPyAnYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMCB0by1jeWFuLTUwMCBzaGFkb3ctbGcgc2hhZG93LWJsdWUtNTAwLzMwJ1xuICAgICAgICAgICAgICA6ICdiZy1ibHVlLTcwMC81MCBncm91cC1ob3ZlcjpiZy1ibHVlLTYwMC83MCdcbiAgICAgICAgICB9YH0+XG4gICAgICAgICAgICB7Lyogw41jb25lIGRlIHNlbmhhIHNlIG8gY2hhdCBmb3IgcHJvdGVnaWRvICovfVxuICAgICAgICAgICAge2NoYXQucGFzc3dvcmQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMSAtcmlnaHQtMSB3LTMgaC0zIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJvcmRlciBib3JkZXItc2xhdGUtOTAwXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTIgaC0yIHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17M31cbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTIgMTV2Mm0tNiA0aDEyYTIgMiAwIDAwMi0ydi02YTIgMiAwIDAwLTItMkg2YTIgMiAwIDAwLTIgMnY2YTIgMiAwIDAwMiAyem0xMC0xMFY3YTQgNCAwIDAwLTggMHY0aDh6XCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2VXaWR0aD17Mn0+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBkPVwiTTggMTJoLjAxTTEyIDEyaC4wMU0xNiAxMmguMDFNMjEgMTJjMCA0LjQxOC00LjAzIDgtOSA4YTkuODYzIDkuODYzIDAgMDEtNC4yNTUtLjk0OUwzIDIwbDEuMzk1LTMuNzJDMy41MTIgMTUuMDQyIDMgMTMuNTc0IDMgMTJjMC00LjQxOCA0LjAzLTggOS04czkgMy41ODIgOSA4elwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0xXCI+XG4gICAgICAgICAgICAgIDxoNiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIHRydW5jYXRlXCI+e2NoYXQubmFtZX08L2g2PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2hpdGUvNTAgZmxleC1zaHJpbmstMCBtbC0yIGdyb3VwLWhvdmVyOm9wYWNpdHktMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwXCI+XG4gICAgICAgICAgICAgICAge2Zvcm1hdFRpbWUoY2hhdC5sYXN0TWVzc2FnZVRpbWUpfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13aGl0ZS82MCBsaW5lLWNsYW1wLTIgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIHtjaGF0Lmxhc3RNZXNzYWdlfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYnV0dG9uPlxuXG4gICAgICB7LyogQm90w7VlcyBkZSBhw6fDo28gLSBhcGFyZWNlbSBubyBob3ZlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMiB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMjAwIGZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFZGl0fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IGJnLWJsdWUtNjAwLzgwIGhvdmVyOmJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgIHRpdGxlPVwiQ29uZmlndXJhciBjaGF0XCJcbiAgICAgICAgPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtM1wiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgZD1cIk0xMC4zMjUgNC4zMTdjLjQyNi0xLjc1NiAyLjkyNC0xLjc1NiAzLjM1IDBhMS43MjQgMS43MjQgMCAwMDIuNTczIDEuMDY2YzEuNTQzLS45NCAzLjMxLjgyNiAyLjM3IDIuMzdhMS43MjQgMS43MjQgMCAwMDEuMDY1IDIuNTcyYzEuNzU2LjQyNiAxLjc1NiAyLjkyNCAwIDMuMzVhMS43MjQgMS43MjQgMCAwMC0xLjA2NiAyLjU3M2MuOTQgMS41NDMtLjgyNiAzLjMxLTIuMzcgMi4zN2ExLjcyNCAxLjcyNCAwIDAwLTIuNTcyIDEuMDY1Yy0uNDI2IDEuNzU2LTIuOTI0IDEuNzU2LTMuMzUgMGExLjcyNCAxLjcyNCAwIDAwLTIuNTczLTEuMDY2Yy0xLjU0My45NC0zLjMxLS44MjYtMi4zNy0yLjM3YTEuNzI0IDEuNzI0IDAgMDAtMS4wNjUtMi41NzJjLTEuNzU2LS40MjYtMS43NTYtMi45MjQgMC0zLjM1YTEuNzI0IDEuNzI0IDAgMDAxLjA2Ni0yLjU3M2MtLjk0LTEuNTQzLjgyNi0zLjMxIDIuMzctMi4zNy45OTYuNjA4IDIuMjk2LjA3IDIuNTcyLTEuMDY1elwiIC8+XG4gICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTUgMTJhMyAzIDAgMTEtNiAwIDMgMyAwIDAxNiAwelwiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17aGFuZGxlRGVsZXRlfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInAtMS41IGJnLXJlZC02MDAvODAgaG92ZXI6YmctcmVkLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICB0aXRsZT1cIkRlbGV0YXIgY2hhdFwiXG4gICAgICAgID5cbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMyBoLTNcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgN2wtLjg2NyAxMi4xNDJBMiAyIDAgMDExNi4xMzggMjFINy44NjJhMiAyIDAgMDEtMS45OTUtMS44NThMNSA3bTUgNHY2bTQtNnY2bTEtMTBWNGExIDEgMCAwMC0xLTFoLTRhMSAxIDAgMDAtMSAxdjNNNCA3aDE2XCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJmb3J3YXJkUmVmIiwidXNlSW1wZXJhdGl2ZUhhbmRsZSIsInVzZVJlZiIsImNvbGxlY3Rpb24iLCJnZXREb2NzIiwib3JkZXJCeSIsInF1ZXJ5IiwidXBkYXRlRG9jIiwiZG9jIiwiZGVsZXRlRG9jIiwiZGVsZXRlT2JqZWN0IiwibGlzdEFsbCIsImRiIiwic3RvcmFnZSIsIkNyZWF0ZUNoYXRNb2RhbCIsIkNyZWF0ZUZvbGRlck1vZGFsIiwiQ29uZmlybURlbGV0ZU1vZGFsIiwiUGFzc3dvcmRQcm90ZWN0ZWRNb2RhbCIsIlNpZGViYXIiLCJyZWYiLCJ1c2VyRGF0YSIsImlzT3BlbiIsIm9uVG9nZ2xlIiwib25TZXR0aW5nc09wZW4iLCJvbkNoYXRTZWxlY3QiLCJjdXJyZW50Q2hhdCIsIm9uVXBkYXRlT3BlblJvdXRlckJhbGFuY2UiLCJmb2xkZXJzIiwic2V0Rm9sZGVycyIsInVub3JnYW5pemVkQ2hhdHMiLCJzZXRVbm9yZ2FuaXplZENoYXRzIiwib3BlblJvdXRlckJhbGFuY2UiLCJzZXRPcGVuUm91dGVyQmFsYW5jZSIsImJhbGFuY2UiLCJpc0xvYWRpbmciLCJlcnJvciIsInVuZGVmaW5lZCIsImNyZWF0ZUNoYXRNb2RhbE9wZW4iLCJzZXRDcmVhdGVDaGF0TW9kYWxPcGVuIiwiZWRpdENoYXRNb2RhbE9wZW4iLCJzZXRFZGl0Q2hhdE1vZGFsT3BlbiIsImVkaXRpbmdDaGF0Iiwic2V0RWRpdGluZ0NoYXQiLCJjcmVhdGVGb2xkZXJNb2RhbE9wZW4iLCJzZXRDcmVhdGVGb2xkZXJNb2RhbE9wZW4iLCJlZGl0Rm9sZGVyTW9kYWxPcGVuIiwic2V0RWRpdEZvbGRlck1vZGFsT3BlbiIsImVkaXRpbmdGb2xkZXIiLCJzZXRFZGl0aW5nRm9sZGVyIiwiZHJhZ2dlZENoYXQiLCJzZXREcmFnZ2VkQ2hhdCIsImRyYWdPdmVyRm9sZGVyIiwic2V0RHJhZ092ZXJGb2xkZXIiLCJob3ZlcmVkRm9sZGVyIiwic2V0SG92ZXJlZEZvbGRlciIsImRlbGV0ZUNvbmZpcm1Nb2RhbCIsInNldERlbGV0ZUNvbmZpcm1Nb2RhbCIsInR5cGUiLCJpZCIsIm5hbWUiLCJpc0RlbGV0aW5nIiwic2V0SXNEZWxldGluZyIsInBhc3N3b3JkTW9kYWwiLCJzZXRQYXNzd29yZE1vZGFsIiwiY2hhdElkIiwiY2hhdE5hbWUiLCJhY3Rpb24iLCJmZXRjaGluZ0JhbGFuY2VSZWYiLCJmZXRjaE9wZW5Sb3V0ZXJCYWxhbmNlIiwiY3VycmVudCIsImNvbnNvbGUiLCJsb2ciLCJwcmV2Iiwib3BlblJvdXRlckFwaUtleSIsImVuZHBvaW50c1JlZiIsInVzZXJuYW1lIiwiZW5kcG9pbnRzU25hcHNob3QiLCJmb3JFYWNoIiwiZGF0YSIsImlzQWN0aXZlIiwidXJsIiwiaW5jbHVkZXMiLCJhcGlLZXkiLCJkb2NJbXBvcnQiLCJnZXREb2MiLCJjb25maWdSZWYiLCJjb25maWdEb2MiLCJleGlzdHMiLCJjb25maWciLCJlbmRwb2ludHMiLCJPYmplY3QiLCJ2YWx1ZXMiLCJlbmRwb2ludCIsImF0aXZvIiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImpzb24iLCJzdWNjZXNzIiwibG9hZENoYXRzIiwiY2hhdHNSZWYiLCJjaGF0c1F1ZXJ5IiwiY2hhdHNTbmFwc2hvdCIsImNoYXRzIiwicHVzaCIsImxhc3RNZXNzYWdlIiwidWx0aW1hTWVuc2FnZW0iLCJsYXN0TWVzc2FnZVRpbWUiLCJ1bHRpbWFNZW5zYWdlbUVtIiwiY3JlYXRlZEF0IiwiZm9sZGVyIiwiZm9sZGVySWQiLCJwYXNzd29yZCIsImZvbGRlcnNSZWYiLCJmb2xkZXJzUXVlcnkiLCJmb2xkZXJzU25hcHNob3QiLCJsb2FkZWRGb2xkZXJzIiwiZm9sZGVyQ2hhdHMiLCJmaWx0ZXIiLCJjaGF0IiwiZGVzY3JpcHRpb24iLCJjb2xvciIsImlzRXhwYW5kZWQiLCJleHBhbmRlZEJ5RGVmYXVsdCIsInVub3JnYW5pemVkIiwiYmFsYW5jZUxvYWRlZFJlZiIsInJlbG9hZENoYXRzIiwidXBkYXRlT3BlblJvdXRlckJhbGFuY2UiLCJoYW5kbGVOZXdDaGF0IiwiaGFuZGxlTmV3Rm9sZGVyIiwiaGFuZGxlRm9sZGVyQ3JlYXRlZCIsImhhbmRsZUZvbGRlclVwZGF0ZWQiLCJoYW5kbGVFZGl0Rm9sZGVyIiwiZm9sZGVyRG9jIiwiZm9sZGVyRGF0YSIsImRvY3MiLCJmaW5kIiwiYWxlcnQiLCJoYW5kbGVEZWxldGVGb2xkZXIiLCJmb2xkZXJOYW1lIiwiaGFuZGxlRGVsZXRlQ2hhdCIsImRlbGV0ZUNoYXRBdHRhY2htZW50cyIsImF0dGFjaG1lbnRzUmVmIiwiYXR0YWNobWVudHNMaXN0IiwiZGVsZXRlUHJvbWlzZXMiLCJpdGVtcyIsIm1hcCIsIml0ZW0iLCJQcm9taXNlIiwiYWxsIiwibGVuZ3RoIiwiY29uZmlybURlbGV0ZSIsImNoYXRzSW5Gb2xkZXIiLCJmIiwidXBkYXRlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3RvcmFnZVJlZiIsInN0b3JhZ2VFcnJvciIsImhhbmRsZURyYWdTdGFydCIsImhhbmRsZURyYWdFbmQiLCJoYW5kbGVEcmFnT3ZlciIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZURyYWdMZWF2ZSIsImhhbmRsZURyb3AiLCJoYW5kbGVDaGF0Q3JlYXRlZCIsImhhbmRsZUVkaXRDaGF0IiwiaGFuZGxlQ2hhdFVwZGF0ZWQiLCJoYW5kbGVIb21lQ2xpY2siLCJ0b2dnbGVGb2xkZXIiLCJmb3JtYXRUaW1lIiwidGltZVN0cmluZyIsImRhdGUiLCJub3ciLCJkaWZmSW5Ib3VycyIsImdldFRpbWUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJob3VyIiwibWludXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwid2Vla2RheSIsImRheSIsIm1vbnRoIiwiZ2V0Rm9sZGVySGV4Q29sb3IiLCJjb2xvck5hbWUiLCJjb2xvck1hcCIsImNoZWNrQ2hhdFBhc3N3b3JkIiwiaW5wdXRQYXNzd29yZCIsImNoYXREb2MiLCJjaGF0RGF0YSIsImhhbmRsZVByb3RlY3RlZEFjdGlvbiIsImZsYXRNYXAiLCJjIiwiZXhlY3V0ZUFjdGlvbiIsImhhbmRsZVBhc3N3b3JkU3VjY2VzcyIsImZvcm1hdEJhbGFuY2UiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsImZvcm1hdCIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImgzIiwicCIsInRpdGxlIiwidG9GaXhlZCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoNCIsIm9uRHJhZ092ZXIiLCJvbkRyYWdMZWF2ZSIsIm9uRHJvcCIsIm9uTW91c2VFbnRlciIsIm9uTW91c2VMZWF2ZSIsImJhY2tncm91bmRDb2xvciIsInN0b3BQcm9wYWdhdGlvbiIsImRyYWdnYWJsZSIsIm9uRHJhZ1N0YXJ0Iiwib25EcmFnRW5kIiwiaDUiLCJDaGF0SXRlbSIsIm9uRWRpdCIsIm9uRGVsZXRlIiwiaXNEcmFnZ2luZyIsIm9uQ2xvc2UiLCJvbkNoYXRDcmVhdGVkIiwib25Gb2xkZXJDcmVhdGVkIiwib25Db25maXJtIiwibWVzc2FnZSIsIml0ZW1OYW1lIiwib25TdWNjZXNzIiwib25QYXNzd29yZFN1Ym1pdCIsImRpc3BsYXlOYW1lIiwiTWF0aCIsImZsb29yIiwiaGFuZGxlRWRpdCIsImhhbmRsZURlbGV0ZSIsImg2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});