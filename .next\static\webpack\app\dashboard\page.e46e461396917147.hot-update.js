"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/ChatInterface.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\");\n/* harmony import */ var _AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AttachmentDisplay */ \"(app-pages-browser)/./src/components/dashboard/AttachmentDisplay.tsx\");\n/* harmony import */ var _DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DeleteMessageModal */ \"(app-pages-browser)/./src/components/dashboard/DeleteMessageModal.tsx\");\n/* harmony import */ var _RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RegenerateMessageModal */ \"(app-pages-browser)/./src/components/dashboard/RegenerateMessageModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, isLoading, isLoadingChat, onDeleteMessage, onRegenerateMessage, onEditMessage, onCopyMessage } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [editingMessageId, setEditingMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingContent, setEditingContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModal, setDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        isDeleting: false\n    });\n    const [regenerateModal, setRegenerateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        messageId: \"\",\n        messageContent: \"\",\n        messagesAffectedCount: 0,\n        isRegenerating: false\n    });\n    const scrollToBottom = ()=>{\n        // Usar requestAnimationFrame para otimizar o scroll\n        requestAnimationFrame(()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: \"smooth\"\n            });\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Debounce o scroll para evitar múltiplas chamadas\n        const timeoutId = setTimeout(()=>{\n            scrollToBottom();\n        }, 100);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        messages\n    ]);\n    const formatTime = (timestamp)=>{\n        return new Date(timestamp).toLocaleTimeString(\"pt-BR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const handleStartEdit = (message)=>{\n        setEditingMessageId(message.id);\n        setEditingContent(message.content);\n    };\n    const handleSaveEdit = ()=>{\n        if (editingMessageId && editingContent.trim() !== \"\") {\n            onEditMessage(editingMessageId, editingContent.trim());\n        }\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    const handleCancelEdit = ()=>{\n        setEditingMessageId(null);\n        setEditingContent(\"\");\n    };\n    const handleDeleteClick = (message)=>{\n        setDeleteModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            isDeleting: false\n        });\n    };\n    const handleDeleteConfirm = async ()=>{\n        setDeleteModal((prev)=>({\n                ...prev,\n                isDeleting: true\n            }));\n        try {\n            await onDeleteMessage(deleteModal.messageId);\n            setDeleteModal({\n                isOpen: false,\n                messageId: \"\",\n                messageContent: \"\",\n                isDeleting: false\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar mensagem:\", error);\n            setDeleteModal((prev)=>({\n                    ...prev,\n                    isDeleting: false\n                }));\n        }\n    };\n    const handleDeleteCancel = ()=>{\n        setDeleteModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            isDeleting: false\n        });\n    };\n    const handleRegenerateClick = (message)=>{\n        // Encontrar o índice da mensagem atual\n        const messageIndex = messages.findIndex((msg)=>msg.id === message.id);\n        // Contar quantas mensagens vêm depois desta (apenas as posteriores serão removidas)\n        const messagesAfterCount = messages.length - messageIndex - 1;\n        setRegenerateModal({\n            isOpen: true,\n            messageId: message.id,\n            messageContent: message.content,\n            messagesAffectedCount: messagesAfterCount,\n            isRegenerating: false\n        });\n    };\n    const handleRegenerateConfirm = async ()=>{\n        // Fechar o modal imediatamente\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n        try {\n            // Iniciar a regeneração (não aguardar conclusão)\n            onRegenerateMessage(regenerateModal.messageId);\n        } catch (error) {\n            console.error(\"Erro ao regenerar mensagem:\", error);\n        }\n    };\n    const handleRegenerateCancel = ()=>{\n        setRegenerateModal({\n            isOpen: false,\n            messageId: \"\",\n            messageContent: \"\",\n            messagesAffectedCount: 0,\n            isRegenerating: false\n        });\n    };\n    const MessageActions = (param)=>/*#__PURE__*/ {\n        let { message, isUser } = param;\n        return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleDeleteClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-red-400 hover:bg-red-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Excluir mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 7\n                }, this),\n                isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleRegenerateClick(message),\n                    className: \"p-1.5 text-white/40 hover:text-blue-400 hover:bg-blue-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Regenerar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleStartEdit(message),\n                    className: \"p-1.5 text-white/40 hover:text-green-400 hover:bg-green-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Editar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onCopyMessage(message.content),\n                    className: \"p-1.5 text-white/40 hover:text-purple-400 hover:bg-purple-400/10 rounded transition-all duration-200 hover:scale-110\",\n                    title: \"Copiar mensagem\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n            lineNumber: 188,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n        style: {\n            maxHeight: \"100%\"\n        },\n        children: [\n            isLoadingChat ? // Estado de carregamento de chat\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400 animate-spin\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Carregando mensagens\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Aguarde enquanto carregamos o hist\\xf3rico da conversa...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-1 bg-gray-700/50 rounded-full mx-auto overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 247,\n                columnNumber: 9\n            }, this) : messages.length === 0 ? // Área vazia quando não há mensagens - centralizada e mais bonita\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-md mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 h-20 mx-auto bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center border border-blue-500/30 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-10 h-10 text-blue-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-white/90 bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"Comece uma nova conversa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-base leading-relaxed\",\n                                    children: \"Digite sua mensagem abaixo para come\\xe7ar a conversar com a IA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 grid grid-cols-1 gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-900/20 backdrop-blur-sm rounded-lg p-4 border border-blue-600/20 hover:border-blue-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-blue-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Fa\\xe7a perguntas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Tire d\\xfavidas sobre qualquer assunto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-cyan-900/20 backdrop-blur-sm rounded-lg p-4 border border-cyan-600/20 hover:border-cyan-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-cyan-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-cyan-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Pe\\xe7a ajuda com c\\xf3digo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Programa\\xe7\\xe3o, debugging e explica\\xe7\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-900/20 backdrop-blur-sm rounded-lg p-4 border border-purple-600/20 hover:border-purple-500/40 transition-all duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-purple-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm font-medium\",\n                                                                children: \"Crie conte\\xfado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: \"Textos, resumos e ideias criativas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, this) : // Container das mensagens com altura controlada\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 min-h-0\",\n                children: [\n                    // Mensagens do chat\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-2 group animate-message-slide-in \".concat(message.sender === \"user\" ? \"flex-row-reverse space-x-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg border-2 \".concat(message.sender === \"user\" ? \"bg-gradient-to-br from-green-400 via-emerald-500 to-green-600 border-green-400/30\" : \"bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 border-blue-400/30\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                        children: message.sender === \"user\" ? \"U\" : \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm rounded-2xl p-3 max-w-3xl border \".concat(message.sender === \"user\" ? \"bg-green-600/20 border-green-500/20 rounded-tr-md ml-auto\" : \"bg-blue-600/20 border-blue-500/20 rounded-tl-md\"),\n                                            children: editingMessageId === message.id ? // Campo de edição inline\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: editingContent,\n                                                        onChange: (e)=>setEditingContent(e.target.value),\n                                                        className: \"w-full min-h-[120px] p-3 bg-gray-800/50 border border-gray-600/30 rounded-lg text-white placeholder-gray-400 resize-y focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50\",\n                                                        placeholder: \"Digite sua mensagem...\",\n                                                        autoFocus: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleCancelEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-gray-400 hover:text-white bg-gray-700/50 hover:bg-gray-600/50 rounded-md transition-all duration-200\",\n                                                                children: \"Cancelar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSaveEdit,\n                                                                className: \"px-3 py-1.5 text-sm text-white bg-blue-600 hover:bg-blue-500 rounded-md transition-all duration-200\",\n                                                                children: \"Salvar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    message.attachments && message.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AttachmentDisplay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        attachments: message.attachments,\n                                                        isUserMessage: message.sender === \"user\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    message.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        content: message.content,\n                                                        hasWebSearch: message.hasWebSearch\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mt-2 \".concat(message.sender === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                            children: message.sender === \"user\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: message.sender === \"user\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: formatTime(message.timestamp)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageActions, {\n                                                        message: message,\n                                                        isUser: message.sender === \"user\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-2 animate-message-slide-in\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-gradient-to-br from-blue-400 via-purple-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 animate-pulse shadow-lg border-2 border-blue-400/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm font-bold drop-shadow-sm\",\n                                    children: \"AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-600/20 backdrop-blur-sm rounded-2xl rounded-tl-md p-3 max-w-3xl border border-blue-500/20 shimmer-effect\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.2s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-white/60 rounded-full animate-typing\",\n                                                        style: {\n                                                            animationDelay: \"0.4s\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white/60 text-sm animate-pulse\",\n                                                children: \"Digitando...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef,\n                        className: \"h-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 347,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DeleteMessageModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: deleteModal.isOpen,\n                onClose: handleDeleteCancel,\n                onConfirm: handleDeleteConfirm,\n                messagePreview: deleteModal.messageContent,\n                isDeleting: deleteModal.isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RegenerateMessageModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: regenerateModal.isOpen,\n                onClose: handleRegenerateCancel,\n                onConfirm: handleRegenerateConfirm,\n                messagePreview: regenerateModal.messageContent,\n                messagesAffectedCount: regenerateModal.messagesAffectedCount,\n                isRegenerating: regenerateModal.isRegenerating\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\ChatInterface.tsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"Xi1vQBWdII3ljTSq2OLW0g786XU=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ChatInterface.tsx\n"));

/***/ })

});