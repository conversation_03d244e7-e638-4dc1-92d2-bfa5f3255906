"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/MarkdownRenderer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-math */ \"(app-pages-browser)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rehype-katex */ \"(app-pages-browser)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var rehype_highlight__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rehype-highlight */ \"(app-pages-browser)/./node_modules/rehype-highlight/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _styles_markdown_latex_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/markdown-latex.css */ \"(app-pages-browser)/./src/styles/markdown-latex.css\");\n/* harmony import */ var _styles_markdown_advanced_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/markdown-advanced.css */ \"(app-pages-browser)/./src/styles/markdown-advanced.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Importar estilos do KaTeX e Markdown\n\n\n\nconst MarkdownRenderer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(_c = (param)=>{\n    let { content, className = \"\", hasWebSearch = false } = param;\n    // Função para detectar se o conteúdo contém citações de web search\n    const detectWebSearch = (text)=>{\n        // Detecta links no formato [dominio.com] que são característicos do web search\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern);\n        return matches !== null && matches.length > 0;\n    };\n    // Função para contar e extrair informações sobre as fontes\n    const getWebSearchInfo = (text)=>{\n        const webSearchPattern = /\\[[\\w.-]+\\.[\\w]+\\]/g;\n        const matches = text.match(webSearchPattern) || [];\n        const sourceSet = new Set(matches.map((match)=>match.slice(1, -1))); // Remove [ e ]\n        const uniqueSources = Array.from(sourceSet);\n        return {\n            sourceCount: matches.length,\n            sources: uniqueSources\n        };\n    };\n    const isWebSearchMessage = hasWebSearch || detectWebSearch(content);\n    const webSearchInfo = isWebSearchMessage ? getWebSearchInfo(content) : {\n        sourceCount: 0,\n        sources: []\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: [\n            webSearchSection && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"web-search-section mb-4 p-3 rounded-lg bg-gradient-to-r from-cyan-900/20 to-blue-900/20 border border-cyan-500/30 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-cyan-400\",\n                                fill: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-cyan-300 text-sm font-medium\",\n                                children: \"\\uD83C\\uDF10 Busca na Web Ativada\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-200/80 leading-relaxed web-search-info\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                            remarkPlugins: [\n                                remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n                            ],\n                            children: webSearchSection\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_5__.Markdown, {\n                remarkPlugins: [\n                    remark_gfm__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    remark_math__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                ],\n                rehypePlugins: [\n                    rehype_katex__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    [\n                        rehype_highlight__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        {\n                            detect: true,\n                            ignoreMissing: true\n                        }\n                    ]\n                ],\n                components: {\n                    // Customizar renderização de código\n                    code (param) {\n                        let { node, inline, className, children, ...props } = param;\n                        const match = /language-(\\w+)/.exec(className || \"\");\n                        return !inline && match ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"bg-gray-800 rounded-lg p-4 overflow-x-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: className,\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de links\n                    a (param) {\n                        let { children, href, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: href,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-blue-400 hover:text-blue-300 underline\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de tabelas\n                    table (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto my-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"min-w-full border-collapse border border-gray-600\",\n                                ...props,\n                                children: children\n                            }, void 0, false, void 0, void 0)\n                        }, void 0, false, void 0, void 0);\n                    },\n                    th (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                            className: \"border border-gray-600 bg-gray-700 px-4 py-2 text-left font-semibold\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    td (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                            className: \"border border-gray-600 px-4 py-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de blockquotes\n                    blockquote (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                            className: \"border-l-4 border-blue-500 pl-4 py-2 my-4 bg-gray-800/50 italic\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de listas\n                    ul (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    ol (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                            className: \"list-decimal list-inside space-y-1 my-2\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de títulos\n                    h1 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold mb-4 mt-6 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h2 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold mb-3 mt-5 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    h3 (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-bold mb-2 mt-4 text-white\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de parágrafos\n                    p (param) {\n                        let { children, ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-3 leading-relaxed text-gray-200\",\n                            ...props,\n                            children: children\n                        }, void 0, false, void 0, void 0);\n                    },\n                    // Customizar renderização de linha horizontal\n                    hr (param) {\n                        let { ...props } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"border-gray-600 my-6\",\n                            ...props\n                        }, void 0, false, void 0, void 0);\n                    }\n                },\n                children: mainContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\MarkdownRenderer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n});\n_c1 = MarkdownRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MarkdownRenderer);\nvar _c, _c1;\n$RefreshReg$(_c, \"MarkdownRenderer$React.memo\");\n$RefreshReg$(_c1, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9NYXJrZG93blJlbmRlcmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2lCO0FBQ1I7QUFDRTtBQUNFO0FBQ1E7QUFFL0MsdUNBQXVDO0FBQ0w7QUFDRztBQUNHO0FBUXhDLE1BQU1NLGlDQUFvRE4saURBQVUsTUFBQztRQUFDLEVBQ3BFUSxPQUFPLEVBQ1BDLFlBQVksRUFBRSxFQUNkQyxlQUFlLEtBQUssRUFDckI7SUFDQyxtRUFBbUU7SUFDbkUsTUFBTUMsa0JBQWtCLENBQUNDO1FBQ3ZCLCtFQUErRTtRQUMvRSxNQUFNQyxtQkFBbUI7UUFDekIsTUFBTUMsVUFBVUYsS0FBS0csS0FBSyxDQUFDRjtRQUMzQixPQUFPQyxZQUFZLFFBQVFBLFFBQVFFLE1BQU0sR0FBRztJQUM5QztJQUVBLDJEQUEyRDtJQUMzRCxNQUFNQyxtQkFBbUIsQ0FBQ0w7UUFDeEIsTUFBTUMsbUJBQW1CO1FBQ3pCLE1BQU1DLFVBQVVGLEtBQUtHLEtBQUssQ0FBQ0YscUJBQXFCLEVBQUU7UUFDbEQsTUFBTUssWUFBWSxJQUFJQyxJQUFJTCxRQUFRTSxHQUFHLENBQUNMLENBQUFBLFFBQVNBLE1BQU1NLEtBQUssQ0FBQyxHQUFHLENBQUMsTUFBTSxlQUFlO1FBQ3BGLE1BQU1DLGdCQUFnQkMsTUFBTUMsSUFBSSxDQUFDTjtRQUVqQyxPQUFPO1lBQ0xPLGFBQWFYLFFBQVFFLE1BQU07WUFDM0JVLFNBQVNKO1FBQ1g7SUFDRjtJQUVBLE1BQU1LLHFCQUFxQmpCLGdCQUFnQkMsZ0JBQWdCSDtJQUMzRCxNQUFNb0IsZ0JBQWdCRCxxQkFBcUJWLGlCQUFpQlQsV0FBVztRQUFFaUIsYUFBYTtRQUFHQyxTQUFTLEVBQUU7SUFBQztJQUNyRyxxQkFDRSw4REFBQ0c7UUFBSXBCLFdBQVcsb0JBQThCLE9BQVZBOztZQUVqQ3FCLGtDQUNDLDhEQUFDRDtnQkFBSXBCLFdBQVU7O2tDQUNiLDhEQUFDb0I7d0JBQUlwQixXQUFVOzswQ0FDYiw4REFBQ3NCO2dDQUFJdEIsV0FBVTtnQ0FBd0J1QixNQUFLO2dDQUFlQyxTQUFROzBDQUNqRSw0RUFBQ0M7b0NBQUtDLEdBQUU7Ozs7Ozs7Ozs7OzBDQUVWLDhEQUFDQztnQ0FBSzNCLFdBQVU7MENBQW9DOzs7Ozs7Ozs7Ozs7a0NBRXRELDhEQUFDb0I7d0JBQUlwQixXQUFVO2tDQUNiLDRFQUFDUixvREFBYUE7NEJBQ1pvQyxlQUFlO2dDQUFDbkMsa0RBQVNBOzZCQUFDO3NDQUV6QjRCOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFPVCw4REFBQzdCLG9EQUFhQTtnQkFDWm9DLGVBQWU7b0JBQUNuQyxrREFBU0E7b0JBQUVDLG1EQUFVQTtpQkFBQztnQkFDdENtQyxlQUFlO29CQUNibEMsb0RBQVdBO29CQUNYO3dCQUFDQyx3REFBZUE7d0JBQUU7NEJBQUVrQyxRQUFROzRCQUFNQyxlQUFlO3dCQUFLO3FCQUFFO2lCQUN6RDtnQkFDREMsWUFBWTtvQkFDVixvQ0FBb0M7b0JBQ3BDQyxNQUFLLEtBQW9EOzRCQUFwRCxFQUFFQyxJQUFJLEVBQUVDLE1BQU0sRUFBRW5DLFNBQVMsRUFBRW9DLFFBQVEsRUFBRSxHQUFHQyxPQUFZLEdBQXBEO3dCQUNILE1BQU0vQixRQUFRLGlCQUFpQmdDLElBQUksQ0FBQ3RDLGFBQWE7d0JBQ2pELE9BQU8sQ0FBQ21DLFVBQVU3QixzQkFDaEIsOERBQUNpQzs0QkFBSXZDLFdBQVU7c0NBQ2IsNEVBQUNpQztnQ0FBS2pDLFdBQVdBO2dDQUFZLEdBQUdxQyxLQUFLOzBDQUNsQ0Q7OzBFQUlMLDhEQUFDSDs0QkFDQ2pDLFdBQVU7NEJBQ1QsR0FBR3FDLEtBQUs7c0NBRVJEOztvQkFHUDtvQkFFQSxtQ0FBbUM7b0JBQ25DSSxHQUFFLEtBQTRCOzRCQUE1QixFQUFFSixRQUFRLEVBQUVLLElBQUksRUFBRSxHQUFHSixPQUFPLEdBQTVCO3dCQUNBLHFCQUNFLDhEQUFDRzs0QkFDQ0MsTUFBTUE7NEJBQ05DLFFBQU87NEJBQ1BDLEtBQUk7NEJBQ0ozQyxXQUFVOzRCQUNULEdBQUdxQyxLQUFLO3NDQUVSRDs7b0JBR1A7b0JBRUEscUNBQXFDO29CQUNyQ1EsT0FBTSxLQUFzQjs0QkFBdEIsRUFBRVIsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7d0JBQ0oscUJBQ0UsOERBQUNqQjs0QkFBSXBCLFdBQVU7c0NBQ2IsNEVBQUM0QztnQ0FDQzVDLFdBQVU7Z0NBQ1QsR0FBR3FDLEtBQUs7MENBRVJEOzs7b0JBSVQ7b0JBRUFTLElBQUcsS0FBc0I7NEJBQXRCLEVBQUVULFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO3dCQUNELHFCQUNFLDhEQUFDUTs0QkFDQzdDLFdBQVU7NEJBQ1QsR0FBR3FDLEtBQUs7c0NBRVJEOztvQkFHUDtvQkFFQVUsSUFBRyxLQUFzQjs0QkFBdEIsRUFBRVYsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7d0JBQ0QscUJBQ0UsOERBQUNTOzRCQUNDOUMsV0FBVTs0QkFDVCxHQUFHcUMsS0FBSztzQ0FFUkQ7O29CQUdQO29CQUVBLHlDQUF5QztvQkFDekNXLFlBQVcsS0FBc0I7NEJBQXRCLEVBQUVYLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO3dCQUNULHFCQUNFLDhEQUFDVTs0QkFDQy9DLFdBQVU7NEJBQ1QsR0FBR3FDLEtBQUs7c0NBRVJEOztvQkFHUDtvQkFFQSxvQ0FBb0M7b0JBQ3BDWSxJQUFHLEtBQXNCOzRCQUF0QixFQUFFWixRQUFRLEVBQUUsR0FBR0MsT0FBTyxHQUF0Qjt3QkFDRCxxQkFDRSw4REFBQ1c7NEJBQUdoRCxXQUFVOzRCQUF3QyxHQUFHcUMsS0FBSztzQ0FDM0REOztvQkFHUDtvQkFFQWEsSUFBRyxLQUFzQjs0QkFBdEIsRUFBRWIsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7d0JBQ0QscUJBQ0UsOERBQUNZOzRCQUFHakQsV0FBVTs0QkFBMkMsR0FBR3FDLEtBQUs7c0NBQzlERDs7b0JBR1A7b0JBRUEscUNBQXFDO29CQUNyQ2MsSUFBRyxLQUFzQjs0QkFBdEIsRUFBRWQsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7d0JBQ0QscUJBQ0UsOERBQUNhOzRCQUFHbEQsV0FBVTs0QkFBMkMsR0FBR3FDLEtBQUs7c0NBQzlERDs7b0JBR1A7b0JBRUFlLElBQUcsS0FBc0I7NEJBQXRCLEVBQUVmLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO3dCQUNELHFCQUNFLDhEQUFDYzs0QkFBR25ELFdBQVU7NEJBQTBDLEdBQUdxQyxLQUFLO3NDQUM3REQ7O29CQUdQO29CQUVBZ0IsSUFBRyxLQUFzQjs0QkFBdEIsRUFBRWhCLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEdBQXRCO3dCQUNELHFCQUNFLDhEQUFDZTs0QkFBR3BELFdBQVU7NEJBQTBDLEdBQUdxQyxLQUFLO3NDQUM3REQ7O29CQUdQO29CQUVBLHdDQUF3QztvQkFDeENpQixHQUFFLEtBQXNCOzRCQUF0QixFQUFFakIsUUFBUSxFQUFFLEdBQUdDLE9BQU8sR0FBdEI7d0JBQ0EscUJBQ0UsOERBQUNnQjs0QkFBRXJELFdBQVU7NEJBQXNDLEdBQUdxQyxLQUFLO3NDQUN4REQ7O29CQUdQO29CQUVBLDhDQUE4QztvQkFDOUNrQixJQUFHLEtBQVk7NEJBQVosRUFBRSxHQUFHakIsT0FBTyxHQUFaO3dCQUNELHFCQUNFLDhEQUFDaUI7NEJBQUd0RCxXQUFVOzRCQUF3QixHQUFHcUMsS0FBSzs7b0JBRWxEO2dCQUNGOzBCQUVDa0I7Ozs7Ozs7Ozs7OztBQUlUOztBQUVBLCtEQUFlMUQsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9NYXJrZG93blJlbmRlcmVyLnRzeD8wYTk5Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdE1hcmtkb3duIGZyb20gJ3JlYWN0LW1hcmtkb3duJztcbmltcG9ydCByZW1hcmtHZm0gZnJvbSAncmVtYXJrLWdmbSc7XG5pbXBvcnQgcmVtYXJrTWF0aCBmcm9tICdyZW1hcmstbWF0aCc7XG5pbXBvcnQgcmVoeXBlS2F0ZXggZnJvbSAncmVoeXBlLWthdGV4JztcbmltcG9ydCByZWh5cGVIaWdobGlnaHQgZnJvbSAncmVoeXBlLWhpZ2hsaWdodCc7XG5cbi8vIEltcG9ydGFyIGVzdGlsb3MgZG8gS2FUZVggZSBNYXJrZG93blxuaW1wb3J0ICdrYXRleC9kaXN0L2thdGV4Lm1pbi5jc3MnO1xuaW1wb3J0ICdAL3N0eWxlcy9tYXJrZG93bi1sYXRleC5jc3MnO1xuaW1wb3J0ICdAL3N0eWxlcy9tYXJrZG93bi1hZHZhbmNlZC5jc3MnO1xuXG5pbnRlcmZhY2UgTWFya2Rvd25SZW5kZXJlclByb3BzIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG4gIGhhc1dlYlNlYXJjaD86IGJvb2xlYW47XG59XG5cbmNvbnN0IE1hcmtkb3duUmVuZGVyZXI6IFJlYWN0LkZDPE1hcmtkb3duUmVuZGVyZXJQcm9wcz4gPSBSZWFjdC5tZW1vKCh7XG4gIGNvbnRlbnQsXG4gIGNsYXNzTmFtZSA9ICcnLFxuICBoYXNXZWJTZWFyY2ggPSBmYWxzZVxufSkgPT4ge1xuICAvLyBGdW7Dp8OjbyBwYXJhIGRldGVjdGFyIHNlIG8gY29udGXDumRvIGNvbnTDqW0gY2l0YcOnw7VlcyBkZSB3ZWIgc2VhcmNoXG4gIGNvbnN0IGRldGVjdFdlYlNlYXJjaCA9ICh0ZXh0OiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICAvLyBEZXRlY3RhIGxpbmtzIG5vIGZvcm1hdG8gW2RvbWluaW8uY29tXSBxdWUgc8OjbyBjYXJhY3RlcsOtc3RpY29zIGRvIHdlYiBzZWFyY2hcbiAgICBjb25zdCB3ZWJTZWFyY2hQYXR0ZXJuID0gL1xcW1tcXHcuLV0rXFwuW1xcd10rXFxdL2c7XG4gICAgY29uc3QgbWF0Y2hlcyA9IHRleHQubWF0Y2god2ViU2VhcmNoUGF0dGVybik7XG4gICAgcmV0dXJuIG1hdGNoZXMgIT09IG51bGwgJiYgbWF0Y2hlcy5sZW5ndGggPiAwO1xuICB9O1xuXG4gIC8vIEZ1bsOnw6NvIHBhcmEgY29udGFyIGUgZXh0cmFpciBpbmZvcm1hw6fDtWVzIHNvYnJlIGFzIGZvbnRlc1xuICBjb25zdCBnZXRXZWJTZWFyY2hJbmZvID0gKHRleHQ6IHN0cmluZyk6IHsgc291cmNlQ291bnQ6IG51bWJlcjsgc291cmNlczogc3RyaW5nW10gfSA9PiB7XG4gICAgY29uc3Qgd2ViU2VhcmNoUGF0dGVybiA9IC9cXFtbXFx3Li1dK1xcLltcXHddK1xcXS9nO1xuICAgIGNvbnN0IG1hdGNoZXMgPSB0ZXh0Lm1hdGNoKHdlYlNlYXJjaFBhdHRlcm4pIHx8IFtdO1xuICAgIGNvbnN0IHNvdXJjZVNldCA9IG5ldyBTZXQobWF0Y2hlcy5tYXAobWF0Y2ggPT4gbWF0Y2guc2xpY2UoMSwgLTEpKSk7IC8vIFJlbW92ZSBbIGUgXVxuICAgIGNvbnN0IHVuaXF1ZVNvdXJjZXMgPSBBcnJheS5mcm9tKHNvdXJjZVNldCk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgc291cmNlQ291bnQ6IG1hdGNoZXMubGVuZ3RoLFxuICAgICAgc291cmNlczogdW5pcXVlU291cmNlc1xuICAgIH07XG4gIH07XG5cbiAgY29uc3QgaXNXZWJTZWFyY2hNZXNzYWdlID0gaGFzV2ViU2VhcmNoIHx8IGRldGVjdFdlYlNlYXJjaChjb250ZW50KTtcbiAgY29uc3Qgd2ViU2VhcmNoSW5mbyA9IGlzV2ViU2VhcmNoTWVzc2FnZSA/IGdldFdlYlNlYXJjaEluZm8oY29udGVudCkgOiB7IHNvdXJjZUNvdW50OiAwLCBzb3VyY2VzOiBbXSB9O1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgbWFya2Rvd24tY29udGVudCAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiBTZcOnw6NvIGRlIFdlYiBTZWFyY2ggZXN0aWxpemFkYSAqL31cbiAgICAgIHt3ZWJTZWFyY2hTZWN0aW9uICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3ZWItc2VhcmNoLXNlY3Rpb24gbWItNCBwLTMgcm91bmRlZC1sZyBiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi05MDAvMjAgdG8tYmx1ZS05MDAvMjAgYm9yZGVyIGJvcmRlci1jeWFuLTUwMC8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yXCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1jeWFuLTQwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjEuNzIxIDEyLjc1MmE5LjcxMSA5LjcxMSAwIDAgMC0uOTQ1LTUuMDAzIDEyLjc1NCAxMi43NTQgMCAwIDEtNC4zMzkgMi43MDggMTguOTkxIDE4Ljk5MSAwIDAgMS0uMjE0IDQuNzcyIDE3LjE2NSAxNy4xNjUgMCAwIDAgNS40OTgtMi40NzdaTTE0LjYzNCAxNS41NWExNy4zMjQgMTcuMzI0IDAgMCAwIC4zMzItNC42NDdjLS45NTIuMjI3LTEuOTQ1LjM0Ny0yLjk2Ni4zNDctMS4wMjEgMC0yLjAxNC0uMTItMi45NjYtLjM0N2ExNy41MTUgMTcuNTE1IDAgMCAwIC4zMzIgNC42NDcgMTcuMzg1IDE3LjM4NSAwIDAgMCA1LjI2OCAwWk05Ljc3MiAxNy4xMTlhMTguOTYzIDE4Ljk2MyAwIDAgMCA0LjQ1NiAwQTE3LjE4MiAxNy4xODIgMCAwIDEgMTIgMjEuNzI0YTE3LjE4IDE3LjE4IDAgMCAxLTIuMjI4LTQuNjA1Wk03Ljc3NyAxNS4yM2ExOC44NyAxOC44NyAwIDAgMS0uMjE0LTQuNzc0IDEyLjc1MyAxMi43NTMgMCAwIDEtNC4zNC0yLjcwOCA5LjcxMSA5LjcxMSAwIDAgMC0uOTQ0IDUuMDA0IDE3LjE2NSAxNy4xNjUgMCAwIDAgNS40OTggMi40NzdaTTIxLjM1NiAxNC43NTJhOS43NjUgOS43NjUgMCAwIDEtNy40NzggNi44MTcgMTguNjQgMTguNjQgMCAwIDAgMS45ODgtNC43MTggMTguNjI3IDE4LjYyNyAwIDAgMCA1LjQ5LTIuMDk4Wk0yLjY0NCAxNC43NTJjMS42ODIuOTcxIDMuNTMgMS42ODggNS40OSAyLjA5OWExOC42NCAxOC42NCAwIDAgMCAxLjk4OCA0LjcxOCA5Ljc2NSA5Ljc2NSAwIDAgMS03LjQ3OC02LjgxNlpNMTMuODc4IDIuNDNhOS43NTUgOS43NTUgMCAwIDEgNi4xMTYgMy45ODYgMTEuMjY3IDExLjI2NyAwIDAgMS0zLjc0NiAyLjUwNCAxOC42MyAxOC42MyAwIDAgMC0yLjM3LTYuNDlaTTEyIDIuMjc2YTE3LjE1MiAxNy4xNTIgMCAwIDEgMi44MDUgNy4xMjFjLS44OTcuMjMtMS44MzcuMzUzLTIuODA1LjM1My0uOTY4IDAtMS45MDgtLjEyMi0yLjgwNS0uMzUzQTE3LjE1MSAxNy4xNTEgMCAwIDEgMTIgMi4yNzZaTTEwLjEyMiAyLjQzYTE4LjYyOSAxOC42MjkgMCAwIDAtMi4zNyA2LjQ5IDExLjI2NiAxMS4yNjYgMCAwIDEtMy43NDYtMi41MDQgOS43NTQgOS43NTQgMCAwIDEgNi4xMTYtMy45ODVaXCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1jeWFuLTMwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+8J+MkCBCdXNjYSBuYSBXZWIgQXRpdmFkYTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1jeWFuLTIwMC84MCBsZWFkaW5nLXJlbGF4ZWQgd2ViLXNlYXJjaC1pbmZvXCI+XG4gICAgICAgICAgICA8UmVhY3RNYXJrZG93blxuICAgICAgICAgICAgICByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtXX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3dlYlNlYXJjaFNlY3Rpb259XG4gICAgICAgICAgICA8L1JlYWN0TWFya2Rvd24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIENvbnRlw7pkbyBwcmluY2lwYWwgKi99XG4gICAgICA8UmVhY3RNYXJrZG93blxuICAgICAgICByZW1hcmtQbHVnaW5zPXtbcmVtYXJrR2ZtLCByZW1hcmtNYXRoXX1cbiAgICAgICAgcmVoeXBlUGx1Z2lucz17W1xuICAgICAgICAgIHJlaHlwZUthdGV4LFxuICAgICAgICAgIFtyZWh5cGVIaWdobGlnaHQsIHsgZGV0ZWN0OiB0cnVlLCBpZ25vcmVNaXNzaW5nOiB0cnVlIH1dXG4gICAgICAgIF19XG4gICAgICAgIGNvbXBvbmVudHM9e3tcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGPDs2RpZ29cbiAgICAgICAgICBjb2RlKHsgbm9kZSwgaW5saW5lLCBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBhbnkpIHtcbiAgICAgICAgICAgIGNvbnN0IG1hdGNoID0gL2xhbmd1YWdlLShcXHcrKS8uZXhlYyhjbGFzc05hbWUgfHwgJycpO1xuICAgICAgICAgICAgcmV0dXJuICFpbmxpbmUgJiYgbWF0Y2ggPyAoXG4gICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcm91bmRlZC1sZyBwLTQgb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgPGNvZGUgY2xhc3NOYW1lPXtjbGFzc05hbWV9IHsuLi5wcm9wc30+XG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgICA8L3ByZT5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxjb2RlIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYXktNzAwIHB4LTEuNSBweS0wLjUgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbW9ub1wiIFxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9jb2RlPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIFxuICAgICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgbGlua3NcbiAgICAgICAgICBhKHsgY2hpbGRyZW4sIGhyZWYsIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj17aHJlZn1cbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTMwMCB1bmRlcmxpbmVcIlxuICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIFxuICAgICAgICAgIC8vIEN1c3RvbWl6YXIgcmVuZGVyaXphw6fDo28gZGUgdGFiZWxhc1xuICAgICAgICAgIHRhYmxlKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvIG15LTRcIj5cbiAgICAgICAgICAgICAgICA8dGFibGUgXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGJvcmRlci1jb2xsYXBzZSBib3JkZXIgYm9yZGVyLWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgICAgIHsuLi5wcm9wc31cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgdGgoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPHRoIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS02MDAgYmctZ3JheS03MDAgcHgtNCBweS0yIHRleHQtbGVmdCBmb250LXNlbWlib2xkXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgdGQoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPHRkIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciBib3JkZXItZ3JheS02MDAgcHgtNCBweS0yXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSBibG9ja3F1b3Rlc1xuICAgICAgICAgIGJsb2NrcXVvdGUoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfSkge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPGJsb2NrcXVvdGUgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWwtNCBib3JkZXItYmx1ZS01MDAgcGwtNCBweS0yIG15LTQgYmctZ3JheS04MDAvNTAgaXRhbGljXCJcbiAgICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvYmxvY2txdW90ZT5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGxpc3Rhc1xuICAgICAgICAgIHVsKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgc3BhY2UteS0xIG15LTJcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBvbCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8b2wgY2xhc3NOYW1lPVwibGlzdC1kZWNpbWFsIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBteS0yXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvb2w+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgXG4gICAgICAgICAgLy8gQ3VzdG9taXphciByZW5kZXJpemHDp8OjbyBkZSB0w610dWxvc1xuICAgICAgICAgIGgxKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNCBtdC02IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBoMih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItMyBtdC01IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICBoMyh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItMiBtdC00IHRleHQtd2hpdGVcIiB7Li4ucHJvcHN9PlxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIHBhcsOhZ3JhZm9zXG4gICAgICAgICAgcCh7IGNoaWxkcmVuLCAuLi5wcm9wcyB9KSB7XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zIGxlYWRpbmctcmVsYXhlZCB0ZXh0LWdyYXktMjAwXCIgey4uLnByb3BzfT5cbiAgICAgICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBDdXN0b21pemFyIHJlbmRlcml6YcOnw6NvIGRlIGxpbmhhIGhvcml6b250YWxcbiAgICAgICAgICBocih7IC4uLnByb3BzIH0pIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxociBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgbXktNlwiIHsuLi5wcm9wc30gLz5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICB9fVxuICAgICAgPlxuICAgICAgICB7bWFpbkNvbnRlbnR9XG4gICAgICA8L1JlYWN0TWFya2Rvd24+XG4gICAgPC9kaXY+XG4gICk7XG59KTtcblxuZXhwb3J0IGRlZmF1bHQgTWFya2Rvd25SZW5kZXJlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlYWN0TWFya2Rvd24iLCJyZW1hcmtHZm0iLCJyZW1hcmtNYXRoIiwicmVoeXBlS2F0ZXgiLCJyZWh5cGVIaWdobGlnaHQiLCJNYXJrZG93blJlbmRlcmVyIiwibWVtbyIsImNvbnRlbnQiLCJjbGFzc05hbWUiLCJoYXNXZWJTZWFyY2giLCJkZXRlY3RXZWJTZWFyY2giLCJ0ZXh0Iiwid2ViU2VhcmNoUGF0dGVybiIsIm1hdGNoZXMiLCJtYXRjaCIsImxlbmd0aCIsImdldFdlYlNlYXJjaEluZm8iLCJzb3VyY2VTZXQiLCJTZXQiLCJtYXAiLCJzbGljZSIsInVuaXF1ZVNvdXJjZXMiLCJBcnJheSIsImZyb20iLCJzb3VyY2VDb3VudCIsInNvdXJjZXMiLCJpc1dlYlNlYXJjaE1lc3NhZ2UiLCJ3ZWJTZWFyY2hJbmZvIiwiZGl2Iiwid2ViU2VhcmNoU2VjdGlvbiIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImQiLCJzcGFuIiwicmVtYXJrUGx1Z2lucyIsInJlaHlwZVBsdWdpbnMiLCJkZXRlY3QiLCJpZ25vcmVNaXNzaW5nIiwiY29tcG9uZW50cyIsImNvZGUiLCJub2RlIiwiaW5saW5lIiwiY2hpbGRyZW4iLCJwcm9wcyIsImV4ZWMiLCJwcmUiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInRhYmxlIiwidGgiLCJ0ZCIsImJsb2NrcXVvdGUiLCJ1bCIsIm9sIiwiaDEiLCJoMiIsImgzIiwicCIsImhyIiwibWFpbkNvbnRlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarkdownRenderer.tsx\n"));

/***/ })

});