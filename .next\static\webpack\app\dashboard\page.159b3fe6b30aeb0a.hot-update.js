"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Upperbar.tsx":
/*!***********************************************!*\
  !*** ./src/components/dashboard/Upperbar.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Upperbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Hook para tempo de sessão\nconst useSessionTime = (chatId)=>{\n    _s();\n    const [sessionTime, setSessionTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0s\");\n    const [startTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now());\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para formatar tempo de forma inteligente\n    const formatTime = (timeInMs)=>{\n        const totalSeconds = Math.floor(timeInMs / 1000);\n        const totalMinutes = Math.floor(totalSeconds / 60);\n        const totalHours = Math.floor(totalMinutes / 60);\n        const totalDays = Math.floor(totalHours / 24);\n        const totalMonths = Math.floor(totalDays / 30);\n        const seconds = totalSeconds % 60;\n        const minutes = totalMinutes % 60;\n        const hours = totalHours % 24;\n        const days = totalDays % 30;\n        // Menos de 1 minuto: apenas segundos\n        if (totalMinutes === 0) {\n            return \"\".concat(totalSeconds, \"s\");\n        }\n        // Menos de 1 hora: MM:SS\n        if (totalHours === 0) {\n            return \"\".concat(minutes, \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 dia: HH:MM:SS\n        if (totalDays === 0) {\n            return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n        }\n        // Menos de 1 mês: Xd HH:MM\n        if (totalMonths === 0) {\n            return \"\".concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n        }\n        // 1 mês ou mais: Xm Xd HH:MM\n        return \"\".concat(totalMonths, \"m \").concat(days, \"d \").concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chatId) {\n            intervalRef.current = setInterval(()=>{\n                const elapsed = Date.now() - startTime;\n                const formattedTime = formatTime(elapsed);\n                setSessionTime(formattedTime);\n            }, 1000);\n        }\n        return ()=>{\n            if (intervalRef.current) {\n                clearInterval(intervalRef.current);\n                intervalRef.current = null;\n            }\n        };\n    }, [\n        chatId,\n        startTime\n    ]);\n    return sessionTime;\n};\n_s(useSessionTime, \"D9h1eTTjB/UJUr/8kCtwXx6uawQ=\");\nfunction Upperbar(param) {\n    let { currentChat, chatName, aiModel, onDownload, onAttachments, isLoading = false, attachmentsCount = 0, aiMetadata } = param;\n    _s1();\n    const sessionTime = useSessionTime(currentChat);\n    const handleAttachments = ()=>{\n        if (onAttachments) {\n            onAttachments();\n        } else {\n            console.log(\"Anexos clicado\");\n        }\n    };\n    const handleStats = ()=>{\n        console.log(\"Estat\\xedsticas clicado\");\n    };\n    const handleDownload = ()=>{\n        if (onDownload) {\n            onDownload();\n        } else {\n            console.log(\"Download clicado\");\n        }\n    };\n    const currentModel = aiModel || \"GPT-4.1 Nano\";\n    const displayChatName = chatName || (currentChat ? \"Chat \".concat(currentChat) : \"Nova Conversa\");\n    // Debug temporário\n    if (true) {\n        console.log(\"=== DEBUG UPPERBAR ===\");\n        console.log(\"chatName prop:\", chatName);\n        console.log(\"currentChat prop:\", currentChat);\n        console.log(\"isLoading prop:\", isLoading);\n        console.log(\"displayChatName calculado:\", displayChatName);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-14 sm:h-16 bg-gradient-to-r from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl border-b border-blue-700/30 shadow-xl relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-full flex items-center justify-between px-3 sm:px-4 lg:px-6 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 sm:space-x-4 flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/30 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-400 rounded-full shadow-lg shadow-blue-400/50 flex-shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm font-medium text-blue-100 truncate\",\n                                        children: currentModel\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    (aiMetadata === null || aiMetadata === void 0 ? void 0 : aiMetadata.usedCoT) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-1.5 sm:px-2 py-0.5 sm:py-1 bg-cyan-600/20 text-cyan-300 text-xs rounded-full border border-cyan-500/30 hidden sm:block\",\n                                        children: \"CoT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-px h-4 sm:h-6 bg-blue-600/30 hidden sm:block\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1.5 sm:space-x-2 bg-blue-900/20 backdrop-blur-sm border border-blue-600/20 rounded-lg sm:rounded-xl px-2 sm:px-3 py-1.5 sm:py-2 hidden sm:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-3 h-3 sm:w-4 sm:h-4 text-blue-300\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs sm:text-sm text-blue-200 font-mono\",\n                                        children: sessionTime\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-1/2 transform -translate-x-1/2 hidden sm:block\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 sm:h-8 bg-blue-700/30 rounded-lg sm:rounded-xl w-32 sm:w-48 backdrop-blur-sm\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 sm:space-x-3 bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 shadow-lg max-w-xs lg:max-w-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1.5 h-1.5 sm:w-2 sm:h-2 bg-cyan-400 rounded-full shadow-lg shadow-cyan-400/50 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-sm sm:text-lg font-semibold text-white truncate\",\n                                    children: displayChatName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1 sm:space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAttachments,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105 relative\",\n                                title: \"Anexos\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, this),\n                                    attachmentsCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 bg-cyan-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg\",\n                                        children: attachmentsCount > 99 ? \"99+\" : attachmentsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStats,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-blue-900/30 hover:bg-blue-800/40 backdrop-blur-sm border border-blue-600/20 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/20 hover:scale-105\",\n                                title: \"Estat\\xedsticas\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDownload,\n                                className: \"p-1.5 sm:p-2.5 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/30 hover:scale-105 border border-blue-500/30\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 sm:w-5 sm:h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Upperbar.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s1(Upperbar, \"QJdsJtw+rWTUW8x2bLmlvIfMLsk=\", false, function() {\n    return [\n        useSessionTime\n    ];\n});\n_c = Upperbar;\nvar _c;\n$RefreshReg$(_c, \"Upperbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Upperbar.tsx\n"));

/***/ })

});