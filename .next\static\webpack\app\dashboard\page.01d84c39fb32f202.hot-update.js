"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _CreateChatModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CreateChatModal */ \"(app-pages-browser)/./src/components/dashboard/CreateChatModal.tsx\");\n/* harmony import */ var _CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CreateFolderModal */ \"(app-pages-browser)/./src/components/dashboard/CreateFolderModal.tsx\");\n/* harmony import */ var _ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ConfirmDeleteModal */ \"(app-pages-browser)/./src/components/dashboard/ConfirmDeleteModal.tsx\");\n/* harmony import */ var _PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordProtectedModal */ \"(app-pages-browser)/./src/components/dashboard/PasswordProtectedModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Sidebar = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { userData, isOpen, onToggle, onSettingsOpen, onChatSelect, currentChat } = param;\n    _s();\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unorganizedChats, setUnorganizedChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [createChatModalOpen, setCreateChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editChatModalOpen, setEditChatModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingChat, setEditingChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createFolderModalOpen, setCreateFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFolderModalOpen, setEditFolderModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingFolder, setEditingFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [draggedChat, setDraggedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragOverFolder, setDragOverFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredFolder, setHoveredFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estados para modais de confirmação\n    const [deleteConfirmModal, setDeleteConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        type: \"chat\",\n        id: \"\",\n        name: \"\"\n    });\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para modal de senha\n    const [passwordModal, setPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isOpen: false,\n        chatId: \"\",\n        chatName: \"\",\n        action: \"access\"\n    });\n    const loadChats = async ()=>{\n        try {\n            // Carregar chats\n            const chatsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\");\n            const chatsQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(chatsRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"lastUpdatedAt\", \"desc\"));\n            const chatsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(chatsQuery);\n            const chats = [];\n            chatsSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                chats.push({\n                    id: doc.id,\n                    name: data.name || \"Conversa sem nome\",\n                    lastMessage: data.ultimaMensagem || \"Nenhuma mensagem ainda\",\n                    lastMessageTime: data.ultimaMensagemEm || data.createdAt,\n                    folder: data.folderId,\n                    password: data.password\n                });\n            });\n            // Carregar pastas\n            const foldersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\");\n            const foldersQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)(foldersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.orderBy)(\"createdAt\", \"asc\"));\n            const foldersSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)(foldersQuery);\n            const loadedFolders = [];\n            foldersSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                const folderChats = chats.filter((chat)=>chat.folder === doc.id);\n                loadedFolders.push({\n                    id: doc.id,\n                    name: data.name,\n                    description: data.description,\n                    color: data.color || \"#3B82F6\",\n                    isExpanded: data.expandedByDefault !== false,\n                    chats: folderChats\n                });\n            });\n            // Chats sem pasta\n            const unorganized = chats.filter((chat)=>!chat.folder);\n            setFolders(loadedFolders);\n            setUnorganizedChats(unorganized);\n        } catch (error) {\n            console.error(\"Erro ao carregar chats e pastas:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (userData.username) {\n            loadChats();\n        }\n    }, [\n        userData.username\n    ]);\n    // Expor a função loadChats para o componente pai\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            reloadChats: loadChats\n        }));\n    const handleNewChat = ()=>{\n        setCreateChatModalOpen(true);\n    };\n    const handleNewFolder = ()=>{\n        setCreateFolderModalOpen(true);\n    };\n    const handleFolderCreated = (folderId)=>{\n        console.log(\"Pasta criada:\", folderId);\n        loadChats(); // Recarregar para mostrar a nova pasta\n    };\n    const handleFolderUpdated = (folderId)=>{\n        console.log(\"Pasta atualizada:\", folderId);\n        loadChats(); // Recarregar para mostrar as alterações\n        setEditFolderModalOpen(false);\n        setEditingFolder(null);\n    };\n    const handleEditFolder = async (folderId)=>{\n        try {\n            var _folderDoc_docs_find;\n            // Buscar dados da pasta no Firestore\n            const folderDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\")));\n            const folderData = (_folderDoc_docs_find = folderDoc.docs.find((doc)=>doc.id === folderId)) === null || _folderDoc_docs_find === void 0 ? void 0 : _folderDoc_docs_find.data();\n            if (folderData) {\n                setEditingFolder({\n                    id: folderId,\n                    name: folderData.name,\n                    description: folderData.description,\n                    color: folderData.color || \"#3B82F6\",\n                    expandedByDefault: folderData.expandedByDefault !== false\n                });\n                setEditFolderModalOpen(true);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da pasta:\", error);\n            alert(\"Erro ao carregar dados da pasta.\");\n        }\n    };\n    const handleDeleteFolder = (folderId, folderName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"folder\",\n            id: folderId,\n            name: folderName\n        });\n    };\n    const handleDeleteChat = (chatId, chatName)=>{\n        setDeleteConfirmModal({\n            isOpen: true,\n            type: \"chat\",\n            id: chatId,\n            name: chatName\n        });\n    };\n    const confirmDelete = async ()=>{\n        setIsDeleting(true);\n        try {\n            if (deleteConfirmModal.type === \"folder\") {\n                var _folders_find;\n                // Deletar pasta\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"pastas\", deleteConfirmModal.id));\n                // Mover todos os chats da pasta para \"sem pasta\"\n                const chatsInFolder = ((_folders_find = folders.find((f)=>f.id === deleteConfirmModal.id)) === null || _folders_find === void 0 ? void 0 : _folders_find.chats) || [];\n                for (const chat of chatsInFolder){\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", chat.id), {\n                        folderId: null,\n                        updatedAt: new Date().toISOString()\n                    });\n                }\n                console.log(\"Pasta deletada:\", deleteConfirmModal.id);\n            } else {\n                // Deletar chat\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.deleteDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", deleteConfirmModal.id));\n                // Tentar deletar arquivo do Storage\n                try {\n                    const storageRef = ref(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.storage, \"usuarios/\".concat(userData.username, \"/conversas/\").concat(deleteConfirmModal.id, \"/chat.json\"));\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.deleteObject)(storageRef);\n                } catch (storageError) {\n                    console.log(\"Arquivo no storage n\\xe3o encontrado:\", storageError);\n                }\n                // Se era o chat ativo, limpar seleção\n                if (currentChat === deleteConfirmModal.id) {\n                    onChatSelect(\"\");\n                }\n                console.log(\"Chat deletado:\", deleteConfirmModal.id);\n            }\n            // Recarregar dados\n            loadChats();\n            // Fechar modal\n            setDeleteConfirmModal({\n                isOpen: false,\n                type: \"chat\",\n                id: \"\",\n                name: \"\"\n            });\n        } catch (error) {\n            console.error(\"Erro ao deletar:\", error);\n            alert(\"Erro ao deletar. Tente novamente.\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // Funções de drag and drop\n    const handleDragStart = (chatId)=>{\n        setDraggedChat(chatId);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedChat(null);\n        setDragOverFolder(null);\n    };\n    const handleDragOver = (e, folderId)=>{\n        e.preventDefault();\n        setDragOverFolder(folderId);\n    };\n    const handleDragLeave = ()=>{\n        setDragOverFolder(null);\n    };\n    const handleDrop = async (e, folderId)=>{\n        e.preventDefault();\n        if (!draggedChat) return;\n        try {\n            // Atualizar o chat no Firestore\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.updateDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\", draggedChat), {\n                folderId: folderId,\n                updatedAt: new Date().toISOString()\n            });\n            console.log(\"Chat \".concat(draggedChat, \" movido para pasta \").concat(folderId || \"sem pasta\"));\n            // Recarregar chats para refletir a mudança\n            loadChats();\n        } catch (error) {\n            console.error(\"Erro ao mover chat:\", error);\n        } finally{\n            setDraggedChat(null);\n            setDragOverFolder(null);\n        }\n    };\n    const handleChatCreated = (chatId)=>{\n        console.log(\"Chat criado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        onChatSelect(chatId);\n    };\n    const handleEditChat = (chat)=>{\n        setEditingChat(chat);\n        setEditChatModalOpen(true);\n    };\n    const handleChatUpdated = (chatId)=>{\n        console.log(\"Chat atualizado:\", chatId);\n        // Recarregar a lista de chats\n        loadChats();\n        setEditChatModalOpen(false);\n        setEditingChat(null);\n    };\n    const handleHomeClick = ()=>{\n        onChatSelect(null); // Limpar chat atual para ir para área inicial\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((prev)=>prev.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return date.toLocaleDateString(\"pt-BR\", {\n                weekday: \"short\"\n            });\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const getFolderHexColor = (colorName)=>{\n        const colorMap = {\n            \"blue\": \"#3B82F6\",\n            \"green\": \"#10B981\",\n            \"yellow\": \"#F59E0B\",\n            \"red\": \"#EF4444\",\n            \"purple\": \"#8B5CF6\",\n            \"cyan\": \"#06B6D4\",\n            \"lime\": \"#84CC16\",\n            \"orange\": \"#F97316\",\n            \"pink\": \"#EC4899\",\n            \"gray\": \"#6B7280\"\n        };\n        return colorMap[colorName] || colorName;\n    };\n    // Funções para chat protegido por senha\n    const checkChatPassword = async (chatId, inputPassword)=>{\n        try {\n            var _chatDoc_docs_find;\n            const chatDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDocs)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.query)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\", userData.username, \"conversas\")));\n            const chatData = (_chatDoc_docs_find = chatDoc.docs.find((doc)=>doc.id === chatId)) === null || _chatDoc_docs_find === void 0 ? void 0 : _chatDoc_docs_find.data();\n            if (chatData && chatData.password) {\n                return chatData.password === inputPassword;\n            }\n            return true; // Se não tem senha, permite acesso\n        } catch (error) {\n            console.error(\"Erro ao verificar senha:\", error);\n            return false;\n        }\n    };\n    const handleProtectedAction = (chatId, chatName, action)=>{\n        const chat = [\n            ...unorganizedChats,\n            ...folders.flatMap((f)=>f.chats)\n        ].find((c)=>c.id === chatId);\n        if (chat === null || chat === void 0 ? void 0 : chat.password) {\n            // Chat protegido, abrir modal de senha\n            setPasswordModal({\n                isOpen: true,\n                chatId,\n                chatName,\n                action\n            });\n        } else {\n            // Chat não protegido, executar ação diretamente\n            executeAction(chatId, chatName, action);\n        }\n    };\n    const executeAction = (chatId, chatName, action)=>{\n        switch(action){\n            case \"access\":\n                onChatSelect(chatId);\n                break;\n            case \"edit\":\n                const chat = [\n                    ...unorganizedChats,\n                    ...folders.flatMap((f)=>f.chats)\n                ].find((c)=>c.id === chatId);\n                if (chat) {\n                    handleEditChat(chat);\n                }\n                break;\n            case \"delete\":\n                handleDeleteChat(chatId, chatName);\n                break;\n        }\n    };\n    const handlePasswordSuccess = ()=>{\n        executeAction(passwordModal.chatId, passwordModal.chatName, passwordModal.action);\n        setPasswordModal({\n            isOpen: false,\n            chatId: \"\",\n            chatName: \"\",\n            action: \"access\"\n        });\n    };\n    const formatBalance = (balance)=>{\n        return new Intl.NumberFormat(\"en-US\", {\n            style: \"currency\",\n            currency: \"USD\"\n        }).format(balance);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n        fixed top-0 left-0 h-full w-80 bg-gradient-to-b from-blue-950/95 via-blue-900/95 to-blue-950/95\\n        backdrop-blur-xl border-r border-blue-700/30 z-50 transform transition-all duration-300 shadow-2xl\\n        \".concat(isOpen ? \"translate-x-0\" : \"-translate-x-full lg:translate-x-0\", \"\\n      \"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-semibold text-lg\",\n                                            children: userData.username.charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-white font-semibold text-lg\",\n                                                children: userData.username\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: formatBalance(userData.balance)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onSettingsOpen,\n                                        className: \"text-blue-200 hover:text-white transition-colors p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleHomeClick,\n                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg  transition-all duration-200 flex items-center space-x-3 font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xc1rea Inicial\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewChat,\n                                            className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Nova Conversa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleNewFolder,\n                                            className: \"bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center font-medium\",\n                                            title: \"Nova Pasta\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-blue-900 scrollbar-track-transparent\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-2 border-b border-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-blue-300 text-sm font-bold uppercase tracking-wider flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Conversas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-2\",\n                                            onDragOver: (e)=>handleDragOver(e, folder.id),\n                                            onDragLeave: handleDragLeave,\n                                            onDrop: (e)=>handleDrop(e, folder.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative rounded-lg transition-all duration-300 cursor-pointer \".concat(hoveredFolder === folder.id ? \"bg-blue-800/40\" : \"hover:bg-blue-800/30\", \" \").concat(dragOverFolder === folder.id ? \"bg-blue-500/30 ring-2 ring-blue-400/50\" : \"\"),\n                                                    onMouseEnter: ()=>setHoveredFolder(folder.id),\n                                                    onMouseLeave: ()=>setHoveredFolder(null),\n                                                    onClick: ()=>toggleFolder(folder.id),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-300 transition-transform duration-200 \".concat(folder.isExpanded ? \"rotate-90\" : \"\"),\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 5l7 7-7 7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-6 h-6 rounded flex items-center justify-center flex-shrink-0\",\n                                                                        style: {\n                                                                            backgroundColor: getFolderHexColor(folder.color) + \"40\"\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4\",\n                                                                            style: {\n                                                                                color: getFolderHexColor(folder.color)\n                                                                            },\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 600,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"text-base font-semibold text-blue-100 truncate\",\n                                                                                        children: folder.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 607,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-blue-300/70 bg-blue-900/50 px-2 py-0.5 rounded-full\",\n                                                                                        children: folder.chats.length\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 610,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 606,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            folder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-blue-300/60 truncate mt-0.5\",\n                                                                                children: folder.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 615,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 transition-all duration-300 \".concat(hoveredFolder === folder.id ? \"opacity-100 translate-x-0\" : \"opacity-0 translate-x-2\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Editar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleEditFolder(folder.id);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200\",\n                                                                        title: \"Deletar pasta\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            handleDeleteFolder(folder.id, folder.name);\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-3.5 h-3.5\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 647,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 646,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                        lineNumber: 638,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-6 mt-2 space-y-1\",\n                                                    children: folder.chats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: ()=>handleDragStart(chat.id),\n                                                            onDragEnd: handleDragEnd,\n                                                            className: \"group relative rounded-xl transition-all duration-300 cursor-move \".concat(currentChat === chat.id ? \"bg-gradient-to-r from-blue-600/20 to-cyan-600/20 border border-blue-500/30 shadow-lg\" : \"hover:bg-blue-800/30 border border-transparent\", \" \").concat(draggedChat === chat.id ? \"opacity-50 scale-95\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"w-full text-left p-3 flex items-start space-x-3\",\n                                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(currentChat === chat.id ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                                                                            children: [\n                                                                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-2 h-2 text-white\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 3,\n                                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                            lineNumber: 682,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 681,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 680,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-white\",\n                                                                                    fill: \"none\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    strokeWidth: 2,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                        lineNumber: 688,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 687,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 673,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"overflow-hidden flex-1 min-w-0 transition-all duration-300 group-hover:pr-20\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"truncate text-sm font-semibold mb-1 \".concat(currentChat === chat.id ? \"text-white\" : \"text-blue-100 group-hover:text-white\"),\n                                                                                    children: chat.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"truncate text-xs leading-relaxed transition-all duration-300 \".concat(currentChat === chat.id ? \"text-blue-200/80\" : \"text-blue-300/70 group-hover:text-blue-200\"),\n                                                                                    children: chat.lastMessage || \"Nenhuma mensagem ainda...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 700,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                chat.lastMessageTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs mt-1 block \".concat(currentChat === chat.id ? \"text-blue-300/60\" : \"text-blue-400/50 group-hover:text-blue-300/70\"),\n                                                                                    children: formatTime(chat.lastMessageTime)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 708,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-blue-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Editar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"edit\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 730,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 729,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"p-1.5 rounded-lg bg-blue-700/90 hover:bg-red-600/90 text-blue-300 hover:text-white transition-all duration-200 backdrop-blur-sm\",\n                                                                            title: \"Deletar\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                handleProtectedAction(chat.id, chat.name, \"delete\");\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-3.5 h-3.5\",\n                                                                                fill: \"none\",\n                                                                                stroke: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                    lineNumber: 742,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                                lineNumber: 741,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                            lineNumber: 733,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                    lineNumber: 720,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, chat.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, folder.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 557,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-2 py-2\",\n                                    onDragOver: (e)=>handleDragOver(e, null),\n                                    onDragLeave: handleDragLeave,\n                                    onDrop: (e)=>handleDrop(e, null),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"text-blue-300 text-xs font-bold uppercase tracking-wider transition-all duration-200 flex items-center space-x-2 \".concat(dragOverFolder === null && draggedChat ? \"text-blue-400\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Sem Pasta \",\n                                                            dragOverFolder === null && draggedChat ? \"(Solte aqui para remover da pasta)\" : \"\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 min-h-[60px] transition-all duration-200 \".concat(dragOverFolder === null && draggedChat ? \"bg-blue-500/10 rounded-lg p-2 border-2 border-dashed border-blue-400/50\" : \"\"),\n                                            children: unorganizedChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-white/5 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4 text-white/30\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                                lineNumber: 776,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                            lineNumber: 775,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/40 text-xs\",\n                                                        children: \"Nenhuma conversa sem pasta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 19\n                                            }, undefined) : unorganizedChats.map((chat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatItem, {\n                                                    chat: chat,\n                                                    isActive: currentChat === chat.id,\n                                                    onClick: ()=>handleProtectedAction(chat.id, chat.name, \"access\"),\n                                                    onEdit: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"edit\"),\n                                                    onDelete: (chatId, chatName)=>handleProtectedAction(chatId, chatName, \"delete\"),\n                                                    onDragStart: handleDragStart,\n                                                    onDragEnd: handleDragEnd,\n                                                    isDragging: draggedChat === chat.id\n                                                }, chat.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 755,\n                                    columnNumber: 13\n                                }, undefined),\n                                folders.length === 0 && unorganizedChats.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-4 py-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white/30 mb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-12 h-12 mx-auto\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                    lineNumber: 805,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/50 text-sm\",\n                                            children: \"Nenhuma conversa ainda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/30 text-xs mt-1\",\n                                            children: 'Clique em \"Nova Conversa\" para come\\xe7ar'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden p-4 border-t border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"w-full bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg  transition-all duration-200 flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Fechar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 817,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 471,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: createChatModalOpen,\n                onClose: ()=>setCreateChatModalOpen(false),\n                username: userData.username,\n                onChatCreated: handleChatCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 832,\n                columnNumber: 7\n            }, undefined),\n            editingChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateChatModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: editChatModalOpen,\n                onClose: ()=>{\n                    setEditChatModalOpen(false);\n                    setEditingChat(null);\n                },\n                username: userData.username,\n                onChatCreated: handleChatUpdated,\n                editingChat: editingChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 841,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: createFolderModalOpen,\n                onClose: ()=>setCreateFolderModalOpen(false),\n                username: userData.username,\n                onFolderCreated: handleFolderCreated\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 854,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateFolderModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: editFolderModalOpen,\n                onClose: ()=>{\n                    setEditFolderModalOpen(false);\n                    setEditingFolder(null);\n                },\n                username: userData.username,\n                onFolderCreated: handleFolderUpdated,\n                editingFolder: editingFolder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 862,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDeleteModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: deleteConfirmModal.isOpen,\n                onClose: ()=>setDeleteConfirmModal({\n                        isOpen: false,\n                        type: \"chat\",\n                        id: \"\",\n                        name: \"\"\n                    }),\n                onConfirm: confirmDelete,\n                title: deleteConfirmModal.type === \"folder\" ? \"Deletar Pasta\" : \"Deletar Conversa\",\n                message: deleteConfirmModal.type === \"folder\" ? 'Tem certeza que deseja deletar esta pasta? Todas as conversas dentro dela ser\\xe3o movidas para \"Sem Pasta\".' : \"Tem certeza que deseja deletar esta conversa? Todas as mensagens ser\\xe3o perdidas permanentemente.\",\n                itemName: deleteConfirmModal.name,\n                isLoading: isDeleting\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 874,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PasswordProtectedModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: passwordModal.isOpen,\n                onClose: ()=>setPasswordModal({\n                        isOpen: false,\n                        chatId: \"\",\n                        chatName: \"\",\n                        action: \"access\"\n                    }),\n                onSuccess: handlePasswordSuccess,\n                chatName: passwordModal.chatName,\n                onPasswordSubmit: (password)=>checkChatPassword(passwordModal.chatId, password)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 889,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n}, \"xlG3ZhdqeBXXbjvoWe1Rswv+jVo=\")), \"xlG3ZhdqeBXXbjvoWe1Rswv+jVo=\");\n_c1 = Sidebar;\nSidebar.displayName = \"Sidebar\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (Sidebar);\nfunction ChatItem(param) {\n    let { chat, isActive, onClick, onEdit, onDelete, onDragStart, onDragEnd, isDragging } = param;\n    const formatTime = (timeString)=>{\n        const date = new Date(timeString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return date.toLocaleTimeString(\"pt-BR\", {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        } else if (diffInHours < 168) {\n            return \"h\\xe1 \".concat(Math.floor(diffInHours / 24), \" dias\");\n        } else {\n            return date.toLocaleDateString(\"pt-BR\", {\n                day: \"2-digit\",\n                month: \"2-digit\"\n            });\n        }\n    };\n    const handleEdit = (e)=>{\n        e.stopPropagation();\n        onEdit(chat.id, chat.name);\n    };\n    const handleDelete = (e)=>{\n        e.stopPropagation();\n        onDelete(chat.id, chat.name);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: ()=>onDragStart(chat.id),\n        onDragEnd: onDragEnd,\n        className: \"relative w-full rounded-lg transition-all duration-200 group cursor-move \".concat(isActive ? \"bg-blue-600/50\" : \"hover:bg-white/5\", \" \").concat(isDragging ? \"opacity-50 scale-95\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onClick,\n                className: \"w-full text-left px-3 py-3 text-white/80 hover:text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300 relative \".concat(isActive ? \"bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg shadow-blue-500/30\" : \"bg-blue-700/50 group-hover:bg-blue-600/70\"),\n                            children: [\n                                chat.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center border border-slate-900\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-2 h-2 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 3,\n                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: 2,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 971,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"font-medium text-sm truncate\",\n                                            children: chat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-white/50 flex-shrink-0 ml-2 group-hover:opacity-0 transition-opacity duration-200\",\n                                            children: formatTime(chat.lastMessageTime)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 976,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-white/60 line-clamp-2 leading-relaxed\",\n                                    children: chat.lastMessage\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 982,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 956,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 952,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleEdit,\n                        className: \"p-1.5 bg-blue-600/80 hover:bg-blue-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Configurar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 991,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleDelete,\n                        className: \"p-1.5 bg-red-600/80 hover:bg-red-600 text-white rounded-md transition-colors duration-200\",\n                        title: \"Deletar chat\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                        lineNumber: 1002,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                lineNumber: 990,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 942,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ChatItem;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Sidebar$forwardRef\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"ChatItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\n"));

/***/ })

});