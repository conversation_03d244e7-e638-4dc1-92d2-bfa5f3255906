"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/Sidebar */ \"(app-pages-browser)/./src/components/dashboard/Sidebar.tsx\");\n/* harmony import */ var _components_dashboard_ChatArea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/ChatArea */ \"(app-pages-browser)/./src/components/dashboard/ChatArea.tsx\");\n/* harmony import */ var _components_dashboard_SettingsModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/SettingsModal */ \"(app-pages-browser)/./src/components/dashboard/SettingsModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [settingsOpen, setSettingsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentChat, setCurrentChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Função para lidar com a criação de chat automático\n    const handleChatCreated = (chatId)=>{\n        var // Recarregar a sidebar para mostrar o novo chat\n        _sidebarRef_current;\n        setCurrentChat(chatId);\n        (_sidebarRef_current = sidebarRef.current) === null || _sidebarRef_current === void 0 ? void 0 : _sidebarRef_current.reloadChats();\n    };\n    // Redirecionar se não estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !user) {\n            router.push(\"/login\");\n        }\n    }, [\n        user,\n        authLoading,\n        router\n    ]);\n    // Buscar dados do usuário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchUserData = async ()=>{\n            if (!user) return;\n            try {\n                // Buscar todos os documentos na coleção usuarios para encontrar o usuário pelo email\n                const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n                const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.db, \"usuarios\");\n                const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n                const querySnapshot = await getDocs(q);\n                if (!querySnapshot.empty) {\n                    var _user_email;\n                    // Usuário encontrado\n                    const userDoc = querySnapshot.docs[0];\n                    const data = userDoc.data();\n                    const newUserData = {\n                        username: data.username || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                        email: data.email || user.email || \"\",\n                        balance: data.balance || 0,\n                        createdAt: data.createdAt || new Date().toISOString()\n                    };\n                    setUserData(newUserData);\n                    // Carregar saldo do OpenRouter após definir userData\n                    setTimeout(()=>{\n                        if (sidebarRef.current) {\n                            sidebarRef.current.updateOpenRouterBalance();\n                        }\n                    }, 500);\n                } else {\n                    var _user_email1;\n                    // Se não encontrar o documento, criar dados padrão\n                    setUserData({\n                        username: ((_user_email1 = user.email) === null || _user_email1 === void 0 ? void 0 : _user_email1.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                        email: user.email || \"\",\n                        balance: 0,\n                        createdAt: new Date().toISOString()\n                    });\n                }\n            } catch (error) {\n                var _user_email2;\n                console.error(\"Erro ao buscar dados do usu\\xe1rio:\", error);\n                // Dados padrão em caso de erro\n                setUserData({\n                    username: ((_user_email2 = user.email) === null || _user_email2 === void 0 ? void 0 : _user_email2.split(\"@\")[0]) || \"Usu\\xe1rio\",\n                    email: user.email || \"\",\n                    balance: 0,\n                    createdAt: new Date().toISOString()\n                });\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (user && !authLoading) {\n            fetchUserData();\n        }\n    }, [\n        user,\n        authLoading\n    ]);\n    const handleUpdateOpenRouterBalance = ()=>{\n        if (sidebarRef.current) {\n            sidebarRef.current.updateOpenRouterBalance();\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-rafthor flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-xl\",\n                children: \"Carregando...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user || !userData) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-rafthor flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                ref: sidebarRef,\n                userData: userData,\n                isOpen: sidebarOpen,\n                onToggle: ()=>setSidebarOpen(!sidebarOpen),\n                onSettingsOpen: ()=>setSettingsOpen(true),\n                onChatSelect: setCurrentChat,\n                currentChat: currentChat\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col lg:ml-80 h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden bg-white/10 backdrop-blur-sm border-b border-white/20 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSidebarOpen(true),\n                            className: \"text-white hover:text-white/80 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ChatArea__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        currentChat: currentChat,\n                        onChatCreated: handleChatCreated,\n                        onUpdateOpenRouterBalance: handleUpdateOpenRouterBalance\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SettingsModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: settingsOpen,\n                onClose: ()=>setSettingsOpen(false),\n                userData: userData,\n                onUserDataUpdate: setUserData\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black/50 z-40\",\n                onClick: ()=>setSidebarOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SiteRafthor\\\\rafthor\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"OqHlHnq7MCvSPycr8PY9OiPzVFM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});